-- 修正服务变更字段配置中的变更原因枚举值
-- 执行日期: 2025-01-29
-- 说明: 根据代码中的EnumGtisServiceChangeProject枚举定义修正脚本中的错误枚举值

-- ================================
-- 修正枚举值映射关系
-- ================================
-- 原脚本错误值 -> 正确值
-- 增加SalesWits服务 = 5 -> 6 (OpenSaleWits)
-- 增加SalesWits账户数 = 6 -> 8 (SalesWitsAddAccount)  
-- 其他 = 8 -> 5 (OtherReason)

-- ================================
-- 使用临时值避免逻辑冲突的修正方法
-- ================================

-- 步骤1: 将"其他"的枚举值从8改为临时值99
UPDATE crm_service_change_reason_field_config 
SET ChangeReasonEnum = 99 
WHERE ChangeReasonEnum = 8;

-- 步骤2: 将"增加SalesWits账户数"的枚举值从6改为临时值98
UPDATE crm_service_change_reason_field_config 
SET ChangeReasonEnum = 98 
WHERE ChangeReasonEnum = 6;

-- 步骤3: 将"增加SalesWits服务"的枚举值从5改为6
UPDATE crm_service_change_reason_field_config 
SET ChangeReasonEnum = 6 
WHERE ChangeReasonEnum = 5;

-- 步骤4: 将临时值98改为正确的"SalesWits新增子账号"枚举值8
UPDATE crm_service_change_reason_field_config 
SET ChangeReasonEnum = 8 
WHERE ChangeReasonEnum = 98;

-- 步骤5: 将临时值99改为正确的"其他"枚举值5
UPDATE crm_service_change_reason_field_config 
SET ChangeReasonEnum = 5 
WHERE ChangeReasonEnum = 99;

-- ================================
-- 验证修正结果
-- ================================
SELECT 
    '修正后的枚举值分布:' AS message;

SELECT 
    ChangeReasonEnum,
    COUNT(*) as count
FROM crm_service_change_reason_field_config 
WHERE Deleted = 0
GROUP BY ChangeReasonEnum 
ORDER BY ChangeReasonEnum;

-- ================================
-- 显示正确的枚举值映射
-- ================================
SELECT 
    '正确的枚举值映射:' AS message;

SELECT 
    1 as ChangeReasonEnum, '尾款申请剩余服务 (ApplyResidualService)' as Description
UNION ALL
SELECT 2, '变更服务内容 (ChangeServiceContent)'
UNION ALL  
SELECT 3, '个人服务天数延期 (DelayPersonalServiceDays)'
UNION ALL
SELECT 4, '优惠券延期 (DelayCoupons)'
UNION ALL
SELECT 5, '其他 (OtherReason)'
UNION ALL
SELECT 6, '开通SaleWits (OpenSaleWits)'
UNION ALL
SELECT 7, 'SalesWits充值 (SalesWitsRecharge)'
UNION ALL
SELECT 8, 'SalesWits新增子账号 (SalesWitsAddAccount)'
ORDER BY ChangeReasonEnum;

SELECT '变更原因枚举值修正完成！' AS message; 