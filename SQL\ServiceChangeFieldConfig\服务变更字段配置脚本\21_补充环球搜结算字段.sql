-- 补充环球搜结算字段配置
-- 执行日期: 2025-01-29
-- 说明: 为个人服务天数延期(ChangeReasonEnum=3)和优惠券延期(ChangeReasonEnum=4)的环球搜服务补充缺失的结算字段
-- 补充字段: GlobalSearchPaymentPeriod(收款时段)、GlobalSearchAccountLevel(账户级别)

-- ================================
-- 个人服务天数延期 - 环球搜结算字段补充
-- ================================

-- 环球搜收款时段字段（个人服务天数延期审核场景）
INSERT INTO crm_service_change_reason_field_config
(Id, ServiceType, ChangeReasonEnum, FieldKey, FieldName, DisplayOrder, IsActive, FieldPermission, ApplyScenario, TriggerFields, Remark, CreateDate, CreateUser, UpdateDate, UpdateUser, Deleted)
VALUES
(UUID(), 3, 3, 'GlobalSearchPaymentPeriod', '环球搜收款时段', 94, 1, 1, 'audit', 'GlobalSearchServiceStart,GlobalSearchServiceEnd,GlobalSearchServiceMonth,PersonalServiceDays,SelfPaidDays,GlobalSearchPaymentPeriod,GlobalSearchAccountLevel', '个人服务天数延期时审核可修改', NOW(), 'system', NULL, 'system', 0);

-- 环球搜账户级别字段（个人服务天数延期审核场景）
INSERT INTO crm_service_change_reason_field_config
(Id, ServiceType, ChangeReasonEnum, FieldKey, FieldName, DisplayOrder, IsActive, FieldPermission, ApplyScenario, TriggerFields, Remark, CreateDate, CreateUser, UpdateDate, UpdateUser, Deleted)
VALUES
(UUID(), 3, 3, 'GlobalSearchAccountLevel', '环球搜账户级别', 95, 1, 1, 'audit', 'GlobalSearchServiceStart,GlobalSearchServiceEnd,GlobalSearchServiceMonth,PersonalServiceDays,SelfPaidDays,GlobalSearchPaymentPeriod,GlobalSearchAccountLevel', '个人服务天数延期时审核可修改', NOW(), 'system', NULL, 'system', 0);

-- ================================
-- 优惠券延期 - 环球搜结算字段补充
-- ================================

-- 环球搜收款时段字段（优惠券延期审核场景）
INSERT INTO crm_service_change_reason_field_config
(Id, ServiceType, ChangeReasonEnum, FieldKey, FieldName, DisplayOrder, IsActive, FieldPermission, ApplyScenario, TriggerFields, Remark, CreateDate, CreateUser, UpdateDate, UpdateUser, Deleted)
VALUES
(UUID(), 3, 4, 'GlobalSearchPaymentPeriod', '环球搜收款时段', 94, 1, 1, 'audit', 'GlobalSearchServiceStart,GlobalSearchServiceEnd,GlobalSearchServiceMonth,CouponList,GlobalSearchPaymentPeriod,GlobalSearchAccountLevel', '优惠券延期时审核可修改', NOW(), 'system', NULL, 'system', 0);

-- 环球搜账户级别字段（优惠券延期审核场景）
INSERT INTO crm_service_change_reason_field_config
(Id, ServiceType, ChangeReasonEnum, FieldKey, FieldName, DisplayOrder, IsActive, FieldPermission, ApplyScenario, TriggerFields, Remark, CreateDate, CreateUser, UpdateDate, UpdateUser, Deleted)
VALUES
(UUID(), 3, 4, 'GlobalSearchAccountLevel', '环球搜账户级别', 95, 1, 1, 'audit', 'GlobalSearchServiceStart,GlobalSearchServiceEnd,GlobalSearchServiceMonth,CouponList,GlobalSearchPaymentPeriod,GlobalSearchAccountLevel', '优惠券延期时审核可修改', NOW(), 'system', NULL, 'system', 0);

-- ================================
-- 验证补充结果
-- ================================
SELECT
    '补充后的环球搜审核字段配置:' AS message;

-- 个人服务天数延期的环球搜字段
SELECT
    '个人服务天数延期(ChangeReasonEnum=3)的环球搜字段:' AS section;
    
SELECT
    FieldKey,
    FieldName,
    DisplayOrder,
    FieldPermission,
    TriggerFields
FROM crm_service_change_reason_field_config
WHERE ServiceType = 3 
  AND ChangeReasonEnum = 3 
  AND ApplyScenario = 'audit'
  AND Deleted = 0
ORDER BY DisplayOrder;

-- 优惠券延期的环球搜字段
SELECT
    '优惠券延期(ChangeReasonEnum=4)的环球搜字段:' AS section;
    
SELECT
    FieldKey,
    FieldName,
    DisplayOrder,
    FieldPermission,
    TriggerFields
FROM crm_service_change_reason_field_config
WHERE ServiceType = 3 
  AND ChangeReasonEnum = 4 
  AND ApplyScenario = 'audit'
  AND Deleted = 0
ORDER BY DisplayOrder;

-- ================================
-- 字段配置说明
-- ================================
SELECT '补充字段说明:' AS message;
SELECT '1. GlobalSearchPaymentPeriod(收款时段): 环球搜服务的收款时段配置' AS description;
SELECT '2. GlobalSearchAccountLevel(账户级别): 环球搜服务的账户级别配置' AS description;
SELECT '3. 这两个字段在个人服务天数延期和优惠券延期场景中都是可修改的' AS description;
SELECT '4. TriggerFields包含了相关的触发条件，确保权限控制的准确性' AS description;

SELECT '环球搜结算字段补充完成！' AS message; 