using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.System;
using CRM2_API.Common;
using CRM2_API.DAL.DbCommon;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Model.ControllersViewModel;

namespace CRM2_API.BLL.Common
{
    /// <summary>
    /// 合同产品申请状态管理助手类
    /// 统一管理合同产品表(crm_contract_productinfo)的ApplState字段更新
    /// 主要用于增项产品的服务状态跟踪，支持动态服务变更原因判断
    /// </summary>
    public static class ContractProductApplStateHelper
    {
        /// <summary>
        /// 更新合同产品的申请状态（通过合同产品表ID）
        /// </summary>
        /// <param name="contractProductInfoId">合同产品信息ID</param>
        /// <param name="applState">申请状态</param>
        /// <param name="userId">操作用户ID</param>
        public static void UpdateContractProductApplState(string contractProductInfoId, EnumProcessStatus applState, string userId)
        {
            if (string.IsNullOrEmpty(contractProductInfoId))
                return;

            try
            {
                var newState = applState.ToInt();
                // 更新ApplState状态
                DbContext.Crm2Db.Updateable<Db_crm_contract_productinfo>()
                .Where(r => r.Id == contractProductInfoId)
                .SetColumns(r => new Db_crm_contract_productinfo {
                    ApplState = newState,
                    UpdateUser = userId,
                    UpdateDate = DateTime.Now
                }).ExecuteCommand();
            }
            catch (Exception ex)
            { 
                // 简化异常处理，避免LogUtil依赖
            }
        }

      
                /// <summary>
        /// 更新合同产品申请状态
        /// 根据申请的具体服务和变更原因精确更新对应的合同产品状态
        /// </summary>
        /// <param name="witsApplId">witsApplId</param>
        /// <param name="applState">申请状态</param>
        /// <param name="userId">用户ID</param>
        /// <param name="changeReasonDetails">产品级别的变更原因详情（可选）</param>
        /// <param name="reasonJson">更新原因json</param>
        public static void UpdateWitsContractProductApplState(string witsApplId, EnumProcessStatus applState, string userId, List<VM_ContractServiceChange.ProductChangeReason> changeReasonDetails = null, string reasonJson = "{}")
        {
            try
            {
                var newState = applState.ToInt();
            
                // 需要更新的具体产品ID集合（产品级别的精确控制）
                var targetProductIds = new List<string>();
                var witsAppl = DbOpe_crm_contract_productserviceinfo_wits_appl.Instance.GetDataById(witsApplId);
                if(witsAppl == null)
                {
                    throw new ApiException("未找到慧思申请信息");
                }
                
                // 基础服务申请的产品ID
                if (witsAppl.IsGtisApply)//写入Gtis申请数据 
                { 
                    var gtis_appl =  DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetData(r=>r.WitsApplId == witsAppl.Id);
                    if(gtis_appl != null)
                    {
                        targetProductIds.Add(gtis_appl.ContractProductInfoId);
                    }
                }
                if (witsAppl.IsGlobalSearchApply)//写入环球搜申请数据
                {   
                    var global_search_appl =  DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.GetData(r=>r.WitsApplId == witsAppl.Id);
                    if(global_search_appl != null)
                    {
                        targetProductIds.Add(global_search_appl.ContractProductInfoId);
                    }
                } 
                if (witsAppl.IsCollegeApply)//写入慧思学院申请数据 
                {
                    var college_appl =  DbOpe_crm_contract_productserviceinfo_college_appl.Instance.GetData(r=>r.WitsApplId == witsAppl.Id);
                    if(college_appl != null)
                    {
                        targetProductIds.Add(college_appl.ContractProductInfoId);
                    }
                }
                if (witsAppl.IsSalesWitsApply)//写入SalesWits申请数据
                {
                    var sales_wits_appl =  DbOpe_crm_contract_productserviceinfo_saleswits_appl.Instance.GetData(r=>r.WitsApplId == witsAppl.Id);
                    if(sales_wits_appl != null)
                    {
                        targetProductIds.Add(sales_wits_appl.ContractProductInfoId);
                    }
                }

                // 解析JSON格式的变更原因
                if (!string.IsNullOrEmpty(witsAppl.ChangeReasonEnums))
                {
                    try
                    {
                        var changeReasonData = System.Text.Json.JsonSerializer.Deserialize<JsonElement>(witsAppl.ChangeReasonEnums);
                        if (changeReasonData.ValueKind != JsonValueKind.Null && changeReasonData.TryGetProperty("ChangeReasons", out var changeReasonsElement) && changeReasonsElement.ValueKind == JsonValueKind.Array)
                        {
                            var changeReasons = changeReasonsElement;
                            foreach (var reason in changeReasons.EnumerateArray())
                            {
                                if (reason.ValueKind == JsonValueKind.Object && 
                                    reason.TryGetProperty("ChangeReason", out var changeReasonElement) && 
                                    reason.TryGetProperty("ContractProductId", out var contractProductIdElement))
                                {
                                    var changeReason = changeReasonElement.GetInt32();
                                    var contractProductId = contractProductIdElement.GetString();
                                    
                                    // 只处理特定的变更原因
                                    switch (changeReason)
                                    {
                                        case (int)EnumGtisServiceChangeProject.OpenSaleWits: // 6
                                        case (int)EnumGtisServiceChangeProject.SalesWitsRecharge: // 7
                                        case (int)EnumGtisServiceChangeProject.SalesWitsAddAccount: // 8
                                            if (!string.IsNullOrEmpty(contractProductId))
                                            {
                                                targetProductIds.Add(contractProductId);
                                            }
                                            break;
                                    }
                                }
                            }
                        }
                    }
                    catch (System.Text.Json.JsonException ex )
                    {
                        // JSON解析失败，可能是旧格式，忽略
                        throw new ApiException($"慧思申请信息变更原因解析失败:{ex.Message}"); 
                    }
                }

                // 如果传入了产品级别的变更原因，也添加到列表中
                if (changeReasonDetails != null && changeReasonDetails.Count > 0)
                {
                    foreach (var detail in changeReasonDetails)
                    {
                        switch (detail.ChangeReason)
                        {
                            case EnumGtisServiceChangeProject.OpenSaleWits: // 6
                            case EnumGtisServiceChangeProject.SalesWitsRecharge: // 7
                            case EnumGtisServiceChangeProject.SalesWitsAddAccount: // 8
                                if (!string.IsNullOrEmpty(detail.ContractProductId))
                                {
                                    targetProductIds.Add(detail.ContractProductId);
                                }
                                break;
                        }
                    }
                }


                // 对于SalesWits产品同合同的增项产品（子账号和充值金额），需要与SaleWits同步修改状态（因为他们默认是一起申请的）
                if(witsAppl.IsSalesWitsApply)
                {
                    var saleWitsAddItemProductIds = DbContext.Crm2Db.Queryable<Db_crm_contract_productinfo>()
                    .LeftJoin<Db_crm_product>((r,p)=>r.ProductId == p.Id)
                    .Where((r,p)=>
                        r.ContractId == witsAppl.ContractId 
                        && (p.ProductType == (int)EnumProductType.AddCredit || p.ProductType == (int)EnumProductType.AdditionalResource)
                        && r.Deleted == false
                        && p.Deleted == false
                        )
                        .Select(r=>r.Id.ToString()).ToList();

                    if(saleWitsAddItemProductIds.Count > 0)
                    {
                        targetProductIds.AddRange(saleWitsAddItemProductIds);
                    }
                }


                // 去重并更新匹配的合同产品状态
                var uniqueProductIds = targetProductIds.Distinct().ToList();
                foreach (var id in uniqueProductIds)
                {
                    if (!string.IsNullOrEmpty(id))
                    {
                        DbContext.Crm2Db.Updateable<Db_crm_contract_productinfo>()
                            .Where(r => r.Id == id)
                            .SetColumns(r => new Db_crm_contract_productinfo {
                                ApplState = newState,
                                UpdateUser = userId,
                                UpdateDate = DateTime.Now
                            }).ExecuteCommand();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new ApiException($"更新合同产品申请状态异常: {ex}");
            }
        }


    }
}