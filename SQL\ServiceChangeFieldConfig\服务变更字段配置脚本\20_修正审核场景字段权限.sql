-- 修正审核场景字段权限配置
-- 执行日期: 2025-01-29
-- 说明: 将审核场景的FieldPermission从1改为2，实现正确的权限控制逻辑
-- 逻辑: 审核时字段默认只读，只有申请时修改了TriggerFields中的字段，审核时才能修改

-- ================================
-- 修正变更服务内容审核场景字段权限
-- ================================

-- GTIS服务审核场景字段权限修正
UPDATE crm_service_change_reason_field_config
SET FieldPermission = 2,
    UpdateDate = NOW(),
    UpdateUser = 'system'
WHERE ServiceType = 1 
  AND ChangeReasonEnum = 2 
  AND ApplyScenario = 'audit'
  AND FieldPermission = 1
  AND Deleted = 0;

-- 环球搜服务审核场景字段权限修正
UPDATE crm_service_change_reason_field_config
SET FieldPermission = 2,
    UpdateDate = NOW(),
    UpdateUser = 'system'
WHERE ServiceType = 3 
  AND ChangeReasonEnum = 2 
  AND ApplyScenario = 'audit'
  AND FieldPermission = 1
  AND Deleted = 0;

-- 慧思学院服务审核场景字段权限修正
UPDATE crm_service_change_reason_field_config
SET FieldPermission = 2,
    UpdateDate = NOW(),
    UpdateUser = 'system'
WHERE ServiceType = 4 
  AND ChangeReasonEnum = 2 
  AND ApplyScenario = 'audit'
  AND FieldPermission = 1
  AND Deleted = 0;

-- SalesWits服务审核场景字段权限修正
UPDATE crm_service_change_reason_field_config
SET FieldPermission = 2,
    UpdateDate = NOW(),
    UpdateUser = 'system'
WHERE ServiceType = 2 
  AND ChangeReasonEnum = 2 
  AND ApplyScenario = 'audit'
  AND FieldPermission = 1
  AND Deleted = 0;

-- 慧思服务通用字段审核场景权限修正
UPDATE crm_service_change_reason_field_config
SET FieldPermission = 2,
    UpdateDate = NOW(),
    UpdateUser = 'system'
WHERE ServiceType = 5 
  AND ChangeReasonEnum = 2 
  AND ApplyScenario = 'audit'
  AND FieldPermission = 1
  AND Deleted = 0;

-- ================================
-- 验证修正结果
-- ================================
SELECT
    '修正后的审核场景字段权限分布:' AS message;

SELECT
    ServiceType,
    ChangeReasonEnum,
    ApplyScenario,
    FieldPermission,
    COUNT(*) as count
FROM crm_service_change_reason_field_config
WHERE ChangeReasonEnum = 2 
  AND ApplyScenario = 'audit'
  AND Deleted = 0
GROUP BY ServiceType, ChangeReasonEnum, ApplyScenario, FieldPermission
ORDER BY ServiceType, FieldPermission;

-- ================================
-- 显示修正后的字段配置示例
-- ================================
SELECT
    '修正后的GTIS服务审核字段配置示例:' AS message;

SELECT
    FieldKey,
    FieldName,
    FieldPermission,
    TriggerFields,
    Remark
FROM crm_service_change_reason_field_config
WHERE ServiceType = 1 
  AND ChangeReasonEnum = 2 
  AND ApplyScenario = 'audit'
  AND Deleted = 0
ORDER BY DisplayOrder
LIMIT 5;

-- ================================
-- 权限控制逻辑说明
-- ================================
SELECT '权限控制逻辑说明:' AS message;
SELECT '1. 审核场景字段默认权限为只读(FieldPermission=2)' AS logic;
SELECT '2. 只有申请时修改了TriggerFields中的字段，审核时才能修改' AS logic;
SELECT '3. 例如：申请时修改了账号数量，审核时才能修改账号数量相关字段' AS logic;
SELECT '4. 这样可以确保审核权限的精确控制' AS logic;

SELECT '审核场景字段权限修正完成！' AS message; 