using CRM2_API.DAL.DbCommon;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Model.System;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CRM2_API.DAL.DbModelOpe.Crm2
{
    /// <summary>
    /// 服务变更字段记录表操作
    /// </summary>
    public class DbOpe_crm_service_change_field_record : DbOperateCrm2<Db_crm_service_change_field_record, DbOpe_crm_service_change_field_record>
    {

        /// <summary>
        /// 批量保存字段变更记录
        /// </summary>
        /// <param name="records">字段变更记录列表</param>
        /// <param name="applyId">申请ID</param>
        /// <param name="userId">用户ID</param>
        /// <returns>是否保存成功</returns>
        public bool SaveFieldRecords(List<Db_crm_service_change_field_record> records, string applyId, string userId)
        {
            try
            {
                // 添加新记录
                if (records != null && records.Any())
                {
                    // 在保存前进行去重：基于FieldKey去重，避免同一字段在不同serviceType下重复
                    var distinctRecords = records
                        .GroupBy(r => r.FieldKey)
                        .Select(g => g.First())
                        .ToList();

                    foreach (var record in distinctRecords)
                    {
                        record.Id = Guid.NewGuid().ToString();
                        record.ApplyId = applyId;
                        record.CreateDate = DateTime.Now;
                        record.CreateUser = userId;
                        record.Deleted = false;
                    }

                    Insert(distinctRecords);
                }
                return true;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"保存字段变更记录异常: {ex.Message}");
                throw new ApiException("保存字段变更记录失败");
            }
        }

        /// <summary>
        /// 获取申请中修改的字段键名列表
        /// </summary>
        /// <param name="applyId">申请ID</param>
        /// <returns>修改的字段键名列表</returns>
        public List<string> GetChangedFieldKeys(string applyId)
        {
            return Queryable
                .Where(e => e.ApplyId == applyId)
                .Where(e => e.Deleted == false)
                .Select(e => e.FieldKey)
                .ToList();
        }

        /// <summary>
        /// 获取申请中的字段变更记录详情
        /// </summary>
        /// <param name="applyId">申请ID</param>
        /// <returns>字段变更记录列表</returns>
        public List<Db_crm_service_change_field_record> GetFieldChangeRecords(string applyId)
        {
            return Queryable
                .Where(e => e.ApplyId == applyId)
                .Where(e => e.Deleted == false)
                .ToList();
        }
    }
}