using CRM2_API.BLL;
using CRM2_API.DAL.DbCommon;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.System;
using JiebaNet.Segmenter.Common;
using Spire.Pdf.Exporting.XPS.Schema.Mc;
using SqlSugar;
using static CRM2_API.Model.BLLModel.Enum.MessageCenterEnumOption;
using static CRM2_API.Model.BLLModel.Enum.PrivateServiceEnumOption;
using static CRM2_API.Model.ControllersViewModel.VM_PrivateService;

namespace CRM2_API.DAL.DbModelOpe.Crm2
{
    /// <summary>
    /// crm_privateservice_detail表操作
    /// </summary>
    public class DbOpe_crm_privateservice_detail : DbOperateCrm2<Db_crm_privateservice_detail, DbOpe_crm_privateservice_detail>
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="contractId"></param>
        /// <param name="privateServiceType"></param>
        /// <param name="applId">申请ID</param>
        /// <param name="useDays"></param>
        /// <param name="chargeDays"></param>
        public void UsingPrivateServiceForContractService(string contractId, EnumPrivateServiceType privateServiceType, string applId, int useDays, int chargeDays)
        {
            Db_crm_privateservice mainData = null;

            if (privateServiceType != EnumPrivateServiceType.Using && applId.IsNotNullOrEmpty())
            {
                string mainId = Queryable.Where(e => e.ContractServiceInfoGtisId == applId).Where(e => e.Deleted == false).Select(e => e.PrivateServiceId).First();
                if (mainId != null)
                {
                    mainData = DbOpe_crm_privateservice.Instance.GetDataById(mainId);
                }
            }
            else
            {
                mainData = DbOpe_crm_privateservice.Instance.GetYearMainData();
            }

            if (mainData.IsNull())
            {
                //throw new ApiException("个人服务时间不存在");
                // return;
                Db_crm_privateservice nullData = new Db_crm_privateservice();
                nullData.Year = DateTime.Now.Year;
                nullData.UserId = UserId;
                nullData.PrivateServiceDays = 0;
                nullData.UsedDays = 0;
                nullData.ChargeDays = 0;
                nullData.ConfirmedDays = 0;
                nullData.State = 0;
                mainData = nullData;
                mainData.Id = DbOpe_crm_privateservice.Instance.InsertDataQueueReturnId(nullData);
                DbOpe_crm_privateservice.Instance.SaveQueues();
            }

            switch (privateServiceType)
            {
                //使用时插入对应记录
                case EnumPrivateServiceType.Using:
                    Db_crm_privateservice_detail newData = new Db_crm_privateservice_detail();
                    newData.ContractId = contractId;
                    newData.PrivateServiceId = mainData.Id;
                    newData.ContractServiceInfoGtisId = applId;
                    newData.UserId = UserId;
                    newData.PrivateServiceType = (int)privateServiceType;
                    newData.UsedDays = useDays;
                    newData.ChargeDays = chargeDays;
                    Instance.InsertDataQueue(newData);
                    //更新主表待确认天数字段
                    Db.Updateable<Db_crm_privateservice>().SetColumns(o => o.ConfirmedDays == o.ConfirmedDays + useDays).SetColumns(o => o.ChargeDays == o.ChargeDays + chargeDays).Where(o => o.Id == mainData.Id && o.Deleted == false && o.Year == mainData.Year).AddQueue();
                    break;
                case EnumPrivateServiceType.Used:
                    //更新之前的数据状态
                    //先获取之前的数据
                    var confirmData = Queryable.Where(e => e.Deleted == false)
                                                .Where(e => e.ContractId == contractId)
                                                .Where(e => e.PrivateServiceId == mainData.Id && e.UserId == mainData.UserId)
                                                .Where(e => e.PrivateServiceType == (int)EnumPrivateServiceType.Using)
                                                .Select(e => new
                                                {
                                                    Id = e.Id,
                                                    PrivateServiceId = e.PrivateServiceId,
                                                    UsedDays = e.UsedDays,

                                                }).First();
                    if (confirmData.IsNull())
                    {
                        return;
                    }
                    //更新子表数据
                    Updateable.SetColumns(e => e.PrivateServiceType == (int)EnumPrivateServiceType.Used).SetColumns(e => e.ContractServiceInfoGtisId == applId).SetColumns(e => e.UpdateUser == UserId).SetColumns(e => e.UpdateDate == DateTime.Now).Where(e => e.Id == confirmData.Id).AddQueue();

                    //更新主表待确认天数和已使用天数字段
                    Db.Updateable<Db_crm_privateservice>()
                      .SetColumns(o => o.UsedDays == o.UsedDays + confirmData.UsedDays)
                      .SetColumnsIF(mainData.ConfirmedDays != 0 && mainData.ConfirmedDays >= confirmData.UsedDays, o => o.ConfirmedDays == o.ConfirmedDays - confirmData.UsedDays).SetColumns(o => o.UpdateUser == UserId).SetColumns(o => o.UpdateDate == DateTime.Now)
                      .Where(o => o.Id == confirmData.PrivateServiceId && o.Deleted == false && o.Year == mainData.Year).AddQueue();
                    break;
                case EnumPrivateServiceType.Refuse:
                    //更新之前的数据状态
                    //先获取之前的数据
                    var refuseData = Queryable.Where(e => e.Deleted == false)
                                                .Where(e => e.ContractId == contractId)
                                                .Where(e => e.PrivateServiceId == mainData.Id && e.UserId == mainData.UserId)
                                                .Where(e => e.PrivateServiceType == (int)EnumPrivateServiceType.Using)
                                                .Select(e => new
                                                {
                                                    Id = e.Id,
                                                    UsedDays = e.UsedDays,
                                                    ChargeDays = e.ChargeDays,
                                                }).First();
                    if (refuseData.IsNull())
                    {
                        return;
                    }
                    //更新子表数据
                    Updateable.SetColumns(e => e.PrivateServiceType == (int)EnumPrivateServiceType.Refuse).SetColumns(e => e.UpdateUser == UserId).SetColumns(e => e.UpdateDate == DateTime.Now).Where(e => e.Id == refuseData.Id).AddQueue();
                    //更新主表待确认天数和已使用天数字段
                    Db.Updateable<Db_crm_privateservice>()
                      .SetColumns(o => o.ConfirmedDays == SqlFunc.IIF(o.ConfirmedDays - refuseData.UsedDays >= 0, o.ConfirmedDays - refuseData.UsedDays, 0))
                      .SetColumns(o => o.ChargeDays == SqlFunc.IIF(o.ChargeDays - refuseData.ChargeDays >= 0, o.ChargeDays - refuseData.ChargeDays, 0))
                      .SetColumns(o => o.UpdateUser == UserId).SetColumns(o => o.UpdateDate == DateTime.Now)
                      .Where(o => o.Id == mainData.Id && o.Deleted == false && o.Year == mainData.Year).AddQueue();
                    break;
                case EnumPrivateServiceType.GiveBacking:
                    //插入一条返还记录
                    Db_crm_privateservice_detail backData = new Db_crm_privateservice_detail();
                    backData.ContractId = contractId;
                    backData.PrivateServiceId = mainData.Id;
                    backData.UserId = DbOpe_crm_contract.Instance.GetContractById(contractId).Issuer;//UserId;
                    backData.PrivateServiceType = (int)privateServiceType;
                    backData.ContractServiceInfoGtisId = applId;
                    backData.UsedDays = -useDays;
                    backData.ChargeDays = -chargeDays;
                    Instance.InsertDataQueue(backData);
                    break;
                case EnumPrivateServiceType.GiveBack:
                    //更新之前的数据状态
                    //先获取之前的数据
                    var backingData = Queryable.Where(e => e.Deleted == false)
                                                .Where(e => e.ContractId == contractId)
                                                .Where(e => e.ContractServiceInfoGtisId == applId && e.UserId == mainData.UserId)
                                                .Where(e => e.PrivateServiceType == (int)EnumPrivateServiceType.GiveBacking)
                                                .Select(e => new
                                                {
                                                    Id = e.Id,
                                                    UsedDays = e.UsedDays,
                                                    ChargeDays = chargeDays,
                                                }).First();
                    if (backingData.IsNull())
                    {
                        return;
                    }
                    //更新子表数据
                    Updateable.SetColumns(e => e.PrivateServiceType == (int)EnumPrivateServiceType.GiveBack).SetColumns(e => e.UpdateUser == UserId).SetColumns(e => e.UpdateDate == DateTime.Now).Where(e => e.Id == backingData.Id).AddQueue();
                    //更新主表已使用天数字段和超额天数
                    Db.Updateable<Db_crm_privateservice>()
                      .SetColumns(o => o.UsedDays == SqlFunc.IIF(o.UsedDays + backingData.UsedDays >= 0, o.UsedDays + backingData.UsedDays, 0))
                      .SetColumns(o => o.ChargeDays == SqlFunc.IIF(o.ChargeDays + backingData.ChargeDays >= 0, o.ChargeDays + backingData.ChargeDays, 0))
                      .SetColumns(o => o.UpdateUser == UserId).SetColumns(o => o.UpdateDate == DateTime.Now)
                      .Where(o => o.Id == mainData.Id && o.Deleted == false && o.Year == mainData.Year).AddQueue();
                    break;
                default:
                    break;
            }

            Instance.SaveQueues();

        }

        public void RollBackePrivateServiceFromDelContarct(string contractId, List<string> applIdList)
        {
            Db_crm_privateservice mainData = null;
            //获取要退回的记录
            var rollBackData = Queryable.Where(e => e.Deleted == false).Where(e => e.ContractId == contractId && applIdList.Contains(e.ContractServiceInfoGtisId)).ToList();
            if (rollBackData.Count > 0)
            {
                rollBackData.ForEach(rollback =>
                {
                    //删除使用记录
                    Updateable.SetColumns(i => i.Deleted == true).Where(i => i.Id == rollback.Id).AddQueue();
                    Db.Updateable<Db_crm_privateservice>()
                      .SetColumns(p => p.ConfirmedDays == p.ConfirmedDays - rollback.UsedDays)
                      .SetColumns(p => p.ChargeDays == p.ChargeDays - rollback.ChargeDays)
                      .Where(p => p.Deleted == false)
                      .Where(p => p.Id == rollback.PrivateServiceId).AddQueue();
                });
            }
            Instance.SaveQueues();

        }


        public List<PrivateServiceDetail_Out> SearchPrivateServiceDetailDataList(PrivateServiceDetail_In privateServiceDetail_In, ref int total)
        {
            //使用情况包括：客户编码、人员名称，合同名称、甲方公司、免费服务天数、超额服务天数、使用状态（审核中、已使用、拒绝、退还），生效时间，发放年度
            //查询条件：发放年度，人员名称，客户编码，合同名称，甲方公司，使用状态，生效时间

            //bool superRole = BLL_Role.Instance.CheckSuperUser();    //高级管理员权限
            //bool managerRole = BLL_Role.Instance.CheckSalesManagerUser();   //销售总监权限

            bool canQuery = Db.Queryable<Db_sys_user>().Where(i => i.Id == UserId).Select(i => i.OrganizationId).First() == Guid.Empty.ToString() ? true : false;

            return Queryable.LeftJoin<Db_v_userwithorg>((e, u) => e.UserId == u.Id)
                            .LeftJoin<Db_crm_contract>((e, u, c) => e.ContractId == c.Id)
                            .LeftJoin<Db_crm_customer_subcompany>((e, u, c, cs) => cs.Id == c.FirstParty)
                            .LeftJoin<Db_crm_privateservice>((e, u, c, cs, cp) => cp.Id == e.PrivateServiceId)
                            .WhereIF(privateServiceDetail_In.UserName.IsNotNullOrEmpty(), (e, u, c, cs, cp) => u.Id.Contains(privateServiceDetail_In.UserName))
                            .WhereIF(privateServiceDetail_In.UserYear.IsNotNull(), (e, u, c, cs, cp) => cp.Year == privateServiceDetail_In.UserYear)
                            .WhereIF(privateServiceDetail_In.ContractNum.IsNotNullOrEmpty(), (e, u, c, cs, cp) => c.ContractNum.Contains(privateServiceDetail_In.ContractNum))
                            .WhereIF(privateServiceDetail_In.ContractName.IsNotNullOrEmpty(), (e, u, c, cs, cp) => c.ContractName.Contains(privateServiceDetail_In.ContractName))
                            .WhereIF(privateServiceDetail_In.FirstPartyName.IsNotNullOrEmpty(), (e, u, c, cs, cp) => cs.CompanyName.Contains(privateServiceDetail_In.FirstPartyName))
                            .WhereIF(privateServiceDetail_In.PrivateServiceType.IsNotNull(), (e, u, c, cs, cp) => e.PrivateServiceType == (int)privateServiceDetail_In.PrivateServiceType)
                            .WhereIF(privateServiceDetail_In.CreateDateStart != null, (e, u, c, cs, cp) => e.CreateDate >= privateServiceDetail_In.CreateDateStart.Value)
                            .WhereIF(privateServiceDetail_In.CreateDateEnd != null, (e, u, c, cs, cp) => e.CreateDate <= privateServiceDetail_In.CreateDateEnd.Value)
                            .WhereIF(!canQuery, (e, u, c, cs, cp) => e.UserId == UserId)
                            .Where((e, u, c, cs, cp) => e.Deleted == false)
                            .OrderByPropertyName(privateServiceDetail_In.SortField, privateServiceDetail_In.IsDESC ? OrderByType.Desc : OrderByType.Asc)
                            .OrderByIF(StringUtil.IsNullOrEmpty(privateServiceDetail_In.SortField), " CreateDate desc,UserName asc")
                            .Select((e, u, c, cs, cp) => new PrivateServiceDetail_Out
                            {
                                Id = e.Id,
                                UserName = u.Name,
                                FirstPartyName = cs.CompanyName,
                                UsedDays = e.UsedDays.Value,
                                ContractName = c.ContractName,
                                ContractNum = c.ContractNum,
                                UserYear = cp.Year.Value,
                                PrivateServiceType = e.PrivateServiceType,
                                ChargeDays = e.ChargeDays.Value,
                                CreateDate = e.CreateDate.Value.ToString("yyyy-MM-dd HH:mm")
                            }, true)
                            .ToPageList(privateServiceDetail_In.PageNumber, privateServiceDetail_In.PageSize, ref total);
        }

        public List<PrivateServiceDetailSum> GetPrivateServiceDetailDataDownload(PrivateServiceDetail_In privateServiceDetail_In)
        {
            //数据部分组装 
            var getSumTypes = new List<int>() {
                (int)EnumPrivateServiceType.Used
               // ,(int)EnumPrivateServiceType.GiveBacking
                ,(int)EnumPrivateServiceType.GiveBack
            };

            bool canQuery = Db.Queryable<Db_sys_user>().Where(i => i.Id == UserId).Select(i => i.OrganizationId).First() == Guid.Empty.ToString() ? true : false;


            var q = Queryable.LeftJoin<Db_crm_contract>((e, c) => e.ContractId == c.Id)
                             .LeftJoin<Db_crm_contract_productinfo>((e, c, cp) => cp.ContractId == c.Id)
                             .LeftJoin<Db_v_userwithorg>((e, c, cp, u) => u.Id == e.UserId)
                             .LeftJoin<Db_crm_privateservice>((e, c, cp, u, pp) => pp.Id == e.PrivateServiceId)
                             .InnerJoin<Db_crm_product>((e, c, cp, u, pp, pro) => pro.Id == cp.ProductId && pro.ProductType == 1)
                             .WhereIF(privateServiceDetail_In.UserName.IsNotNullOrEmpty(), (e, c, cp, u) => u.Id.Contains(privateServiceDetail_In.UserName))
                             .WhereIF(privateServiceDetail_In.UserYear.IsNotNull(), (e, c, cp, u, pp) => pp.Year == privateServiceDetail_In.UserYear)
                             .WhereIF(privateServiceDetail_In.ContractNum.IsNotNullOrEmpty(), (e, c, cp, u, pp) => c.ContractNum.Contains(privateServiceDetail_In.ContractNum))
                             .WhereIF(privateServiceDetail_In.ContractName.IsNotNullOrEmpty(), (e, c, cp, u, pp) => c.ContractName.Contains(privateServiceDetail_In.ContractName))
                             //.WhereIF(privateServiceDetail_In.FirstPartyName.IsNotNullOrEmpty(), (e, c, cp, u, pp) => cp.CompanyName.Contains(privateServiceDetail_In.FirstPartyName))
                             .WhereIF(privateServiceDetail_In.PrivateServiceType.IsNotNull(), (e, c, cp, u, pp) => e.PrivateServiceType == (int)privateServiceDetail_In.PrivateServiceType)
                             .WhereIF(privateServiceDetail_In.CreateDateStart != null, (e, c, cp, u, pp) => e.CreateDate >= privateServiceDetail_In.CreateDateStart.Value)
                             .WhereIF(privateServiceDetail_In.CreateDateEnd != null, (e, c, cp, u, pp) => e.CreateDate <= privateServiceDetail_In.CreateDateEnd.Value)
                             .Where((e, c, cp, u, pp) => e.Deleted == false)
                             .Where((e, c, cp, u, pp) => getSumTypes.Contains(e.PrivateServiceType))
                             .WhereIF(!canQuery, (e, c, cp, u, pp) => e.UserId == UserId)
                             .Select((e, c, cp, u, pp) => new
                             {
                                 UserName = u.Name,
                                 ContractId = c.Id,
                                 PriceId = SqlFunc.IIF(cp.ProductPriceId == "00000000-0000-0000-0000-000000000000", cp.ParentProductPriceId, cp.ProductPriceId),
                                 ChargeDays = e.ChargeDays,
                             }).MergeTable()
                             .LeftJoin<Db_crm_product_price>((merge, price) => price.Id == merge.PriceId)
                             .Select((merge, price) => new PrivateServiceDetailSingle
                             {
                                 UserName = merge.UserName,
                                 ContractId = merge.ContractId,
                                 ChargePrice = SqlFunc.ToDecimal(price.Price / 14 / 30 * merge.ChargeDays),
                                 ChargeDays = SqlFunc.ToInt32(merge.ChargeDays),
                             }, true)
                             .MergeTable()
                             .GroupBy("UserName")
                             .Select(re => new PrivateServiceDetailSum
                             {
                                 UserName = re.UserName,
                                 SumChargePrice = SqlFunc.Ceil(SqlFunc.AggregateSum(re.ChargePrice)),
                                 SumChargeDays = SqlFunc.AggregateSum(re.ChargeDays)
                             }).ToList();

            return q;

        }

        public List<Db_crm_privateservice_detail> GetBackDetailList()
        {
            return Queryable.Where(e => e.Deleted == false).Where(e => e.PrivateServiceType == (int)EnumPrivateServiceType.GiveBacking).Select<Db_crm_privateservice_detail>().ToList();
        }

        public void CheckWrongData()
        {
            var wrongData = Queryable.InnerJoin<Db_crm_contract>((pd, c) => pd.ContractId == c.Id && c.Deleted == true).Where((pd, c) => pd.Deleted == false)
                                     .Select((pd, c) => new
                                     {
                                         Id = pd.Id,
                                         mainId = pd.PrivateServiceId,
                                         usedays = pd.UsedDays,
                                         chargeday = pd.ChargeDays,
                                     }).ToList();
            wrongData.ForEach(it =>
            {
                //删除使用记录
                Updateable.SetColumns(i => i.Deleted == true).Where(i => i.Id == it.Id).AddQueue();
                Db.Updateable<Db_crm_privateservice>()
                  .SetColumns(p => p.ConfirmedDays == p.ConfirmedDays - it.usedays)
                  .SetColumns(p => p.ChargeDays == p.ChargeDays - it.chargeday)
                  .Where(p => p.Deleted == false)
                  .Where(p => p.Id == it.mainId).AddQueue();
            });
            Instance.SaveQueues();
        }

        public PrivcateServiceResult OperatedRollBackPrivateServiceDay(RollBackPrivateServiceDetail_In rollBackPrivateServiceDetail_In)
        {
            //第一步 先更新主记录
            //先检查主记录，如果退还天数超过了使用的天数那不能进行操作
            var checkData = Db.Queryable<Db_crm_privateservice>().Where(p => p.Id == rollBackPrivateServiceDetail_In.Id).Select<Db_crm_privateservice>().First();
            if (checkData.IsNull())
            {
                throw new ApiException("退还的记录存在异常，请检查");
            }
            if (checkData.UsedDays - rollBackPrivateServiceDetail_In.UsedDays < 0 || checkData.ChargeDays - rollBackPrivateServiceDetail_In.ChargeDays < 0)
            {
                throw new ApiException("退还天数已大于使用天数，请检查");
            }
            if (rollBackPrivateServiceDetail_In.UsedDays == 0 && rollBackPrivateServiceDetail_In.ChargeDays == 0)
            {
                throw new ApiException("退还天数不可以都为0");
            }

            Db.Updateable<Db_crm_privateservice>().SetColumns(p => p.UsedDays == SqlFunc.IIF(p.UsedDays - rollBackPrivateServiceDetail_In.UsedDays >= 0, p.UsedDays - rollBackPrivateServiceDetail_In.UsedDays, 0))
                                                  .SetColumns(p => p.ChargeDays == SqlFunc.IIF(p.ChargeDays - rollBackPrivateServiceDetail_In.ChargeDays >= 0, p.ChargeDays - rollBackPrivateServiceDetail_In.ChargeDays, 0))
                                                  .SetColumns(p => p.RollbackRemark == p.RollbackRemark + ";" + rollBackPrivateServiceDetail_In.Remark)
                                                  .Where(p => p.Id == rollBackPrivateServiceDetail_In.Id)
                                                  .AddQueue();

            //第二步 添加详细记录
            Db_crm_privateservice_detail newdata = new Db_crm_privateservice_detail();
            newdata.PrivateServiceId = rollBackPrivateServiceDetail_In.Id;
            newdata.UsedDays = -rollBackPrivateServiceDetail_In.UsedDays;
            newdata.ChargeDays = -rollBackPrivateServiceDetail_In.ChargeDays;
            newdata.Remark = rollBackPrivateServiceDetail_In.Remark;
            newdata.UserId = rollBackPrivateServiceDetail_In.UserId;
            newdata.PrivateServiceType = (int)EnumPrivateServiceType.GiveBack;

            InsertDataQueue(newdata);

            Instance.SaveQueues();
            PrivcateServiceResult result = new PrivcateServiceResult();
            result.affectedRow = Instance.SaveQueues();
            result.state = 0;
            return result;

        }
    }
}
