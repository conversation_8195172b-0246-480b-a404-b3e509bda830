using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using CRM2_API.BLL.Common;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Model.BLLModel.ServiceChange;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.Enum;
using CRM2_API.DAL.DbCommon;
using CRM2_API.Model.System;
using CRM2_API.Model.BLLModel;
using CRM2_API.Model.BLLModel.ServiceOpening;
using CRM2_API.Common.Log;
using SqlSugar;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using CRM2_API.Model.ControllersViewModel;

namespace CRM2_API.BLL
{
    /// <summary>
    /// 服务变更字段权限业务逻辑类
    /// </summary>
    public class BLL_ServiceChangeFieldPermission : BaseBLL<BLL_ServiceChangeFieldPermission>
    {
        private readonly SqlSugarScope Db = DbContext.Crm2Db;

        /// <summary>
        /// 获取申请场景的字段权限配置
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns>字段权限配置</returns>
        public ServiceChangeFieldPermissionsResponse GetApplyFieldPermissions(ServiceChangeFieldPermissionsRequest request)
        {
            try
            {
                if (request == null)
                    throw new ApiException("请求参数不能为空");

                if (request.ContractProductIds == null || !request.ContractProductIds.Any())
                    throw new ApiException("合同产品ID列表不能为空");

                if (request.ChangeReasons == null || !request.ChangeReasons.Any())
                    throw new ApiException("变更原因列表不能为空");

                // 获取服务类型
                var serviceTypes = GetServiceTypesByContractProducts(request.ContractProductIds);
                LogUtil.AddLog($"合同产品ID: {string.Join(",", request.ContractProductIds)}, 服务类型: {string.Join(",", serviceTypes)}");

                // 获取字段权限配置
                var fieldPermissions = new List<FieldPermissionInfo>();

                foreach (var serviceType in serviceTypes)
                {
                    foreach (var changeReason in request.ChangeReasons)
                    {
                        var permissions = GetApplyFieldPermissions(serviceType, changeReason);
                        fieldPermissions.AddRange(permissions);
                    }
                }

                // 去重并按显示顺序排序
                var distinctPermissions = fieldPermissions
                    .GroupBy(p => p.FieldKey)
                    .Select(g => g.First())
                    .OrderBy(p => p.DisplayOrder)
                    .ToList();

                return new ServiceChangeFieldPermissionsResponse
                {
                    Success = true,
                    Message = "获取字段权限配置成功",
                    FieldPermissions = distinctPermissions
                };
            }
            catch (ApiException)
            {
                throw;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取申请字段权限配置异常: {ex}");
                throw new ApiException($"获取字段权限配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取审核场景的字段权限配置
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns>字段权限配置</returns>
        public ServiceChangeFieldPermissionsResponse GetAuditFieldPermissions(ServiceChangeFieldPermissionsRequest request)
        {
            try
            {
                if (request == null)
                    throw new ApiException("请求参数不能为空");

                if (request.ContractProductIds == null || !request.ContractProductIds.Any())
                    throw new ApiException("合同产品ID列表不能为空");

                if (request.ChangeReasons == null || !request.ChangeReasons.Any())
                    throw new ApiException("变更原因列表不能为空");

                if (string.IsNullOrEmpty(request.WitsApplyId))
                    throw new ApiException("申请ID不能为空");

                // 获取服务类型
                var serviceTypes = GetServiceTypesByContractProducts(request.ContractProductIds);
                LogUtil.AddLog($"审核场景 - 合同产品ID: {string.Join(",", request.ContractProductIds)}, 服务类型: {string.Join(",", serviceTypes)}");

                // 获取实际修改的字段列表
                var changedFields = GetChangedFields(request.WitsApplyId);
                if (changedFields == null || !changedFields.Any())
                {
                    LogUtil.AddLog($"未找到慧思服务ID为{request.WitsApplyId}的变更记录");
                    changedFields = new List<string>();
                }

                // 获取字段变更记录详情
                var fieldChangeRecords = GetFieldChangeRecords(request.WitsApplyId);
                string test = JsonConvert.SerializeObject(fieldChangeRecords);
                // 获取字段权限配置
                var fieldPermissions = new List<FieldPermissionInfo>();

                foreach (var serviceType in serviceTypes)
                {
                    foreach (var changeReason in request.ChangeReasons)
                    {
                        var permissions = GetAuditFieldPermissions(serviceType, changeReason, changedFields, fieldChangeRecords);
                        fieldPermissions.AddRange(permissions);
                    }
                }

                // 去重并按显示顺序排序
                var distinctPermissions = fieldPermissions
                    .GroupBy(p => p.FieldKey)
                    .Select(g => g.First())
                    .OrderBy(p => p.DisplayOrder)
                    .ToList();

                return new ServiceChangeFieldPermissionsResponse
                {
                    Success = true,
                    Message = "获取审核字段权限配置成功",
                    FieldPermissions = distinctPermissions
                };
            }
            catch (ApiException)
            {
                throw;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取审核字段权限配置异常: {ex}");
                throw new ApiException($"获取审核字段权限配置失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 获取实际修改的字段列表
        /// </summary>
        /// <param name="applyId">申请ID</param>
        /// <returns>修改的字段键名列表</returns>
        private List<string> GetChangedFields(string applyId)
        {
            try
            {
                // 尝试从新表获取字段变更记录
                var newRecords = DbOpe_crm_service_change_field_record.Instance.GetChangedFieldKeys(applyId);
                if (newRecords != null && newRecords.Any())
                {
                    LogUtil.AddLog($"从新表获取到{newRecords.Count}个变更字段");
                    return newRecords;
                }
                // 都没有找到，返回空列表
                LogUtil.AddLog($"未找到申请ID为{applyId}的变更记录");
                return new List<string>();
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取变更字段异常: {ex}");
                return new List<string>();
            }
        }

        /// <summary>
        /// 获取字段变更记录详情
        /// </summary>
        /// <param name="applyId">申请ID</param>
        /// <returns>字段变更记录字典</returns>
        private Dictionary<string, Db_crm_service_change_field_record> GetFieldChangeRecords(string applyId)
        {
            try
            {
                var records = DbOpe_crm_service_change_field_record.Instance.GetFieldChangeRecords(applyId);
                if (records != null && records.Any())
                {
                    return records.ToDictionary(r => r.FieldKey, r => r);
                }
                
                LogUtil.AddLog($"未找到申请ID为{applyId}的变更记录详情");
                return new Dictionary<string, Db_crm_service_change_field_record>();
            }
            catch (Exception ex)
            {
                throw new ApiException($"获取字段变更记录详情异常: {ex}");
            }
        }

        /// <summary>
        /// 获取服务类型列表
        /// </summary>
        /// <param name="contractProductIds">合同产品ID列表</param>
        /// <returns>服务类型列表</returns>
        private List<EnumServiceType> GetServiceTypesByContractProducts(List<string> contractProductIds)
        {
            try
            {
                // 查询合同产品信息，获取产品类型
                var contractProductsWithType = Db.Queryable<Db_crm_contract_productinfo>()
                    .LeftJoin<Db_crm_product>((cpi, p) => cpi.ProductId == p.Id)
                    .Where((cpi, p) => contractProductIds.Contains(cpi.Id) && cpi.Deleted != true)
                    .Select((cpi, p) => new { ProductType = p.ProductType })
                    .ToList();

                if (!contractProductsWithType.Any())
                {
                    LogUtil.AddLog("未找到合同产品信息，返回默认服务类型");
                    return new List<EnumServiceType> { EnumServiceType.Gtis };
                }

                // 将产品类型映射为服务类型
                var serviceTypes = new List<EnumServiceType>();
                foreach (var product in contractProductsWithType)
                {
                    var serviceType = MapProductTypeToServiceType(product.ProductType ?? 1);
                    if (!serviceTypes.Contains(serviceType))
                    {
                        serviceTypes.Add(serviceType);
                    }
                }

                LogUtil.AddLog($"合同产品服务类型: {string.Join(",", serviceTypes)}");
                return serviceTypes;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取服务类型异常: {ex}");
                return new List<EnumServiceType> { EnumServiceType.Gtis };
            }
        }

        /// <summary>
        /// 将产品类型映射为服务类型
        /// </summary>
        /// <param name="productType">产品类型</param>
        /// <returns>服务类型</returns>
        private EnumServiceType MapProductTypeToServiceType(int productType)
        {
            return productType switch
            {
                1 => EnumServiceType.Gtis,        // GTIS专业版
                4 => EnumServiceType.Gtis,        // VIP零售 (属于GTIS服务类型)
                5 => EnumServiceType.GlobalSearch, // 环球搜
                6 => EnumServiceType.College,     // 慧思学院 (GlobalWitsSchool)
                10 => EnumServiceType.SalesWits,  // SalesWits
                11 => EnumServiceType.SalesWits,  // 充值 (通常与SalesWits相关)
                12 => EnumServiceType.SalesWits,       // 新增资源 (通常与GTIS相关)
                99 => EnumServiceType.Wits,       // 慧思产品 (虚拟类型，使用Wits通用服务类型)
                _ => EnumServiceType.Wits         // 默认GTIS
            };
        }

        /// <summary>
        /// 获取申请场景的字段权限配置（重载方法，接受变更原因枚举列表）
        /// </summary>
        /// <param name="changeReasons">变更原因枚举列表</param>
        /// <returns>字段权限配置列表</returns>
        public List<Db_crm_service_change_reason_field_config> GetApplyFieldPermissions(List<EnumGtisServiceChangeProject> changeReasons)
        {
            var configs = new List<Db_crm_service_change_reason_field_config>();
            
            foreach (var changeReason in changeReasons)
            {
                var reasonConfigs = Db.Queryable<Db_crm_service_change_reason_field_config>()
                    .Where(x => x.ChangeReasonEnum == (int)changeReason &&
                               x.ApplyScenario == "apply" && // 申请场景
                               x.IsActive == 1 &&
                               x.Deleted != 1)
                    .OrderBy(x => x.DisplayOrder)
                    .ToList();
                configs.AddRange(reasonConfigs);
            }
            
            // 去重
            return configs.GroupBy(c => c.FieldKey).Select(g => g.First()).ToList();
        }

        /// <summary>
        /// 获取申请场景的字段权限配置
        /// </summary>
        /// <param name="serviceType">服务类型</param>
        /// <param name="changeReason">变更原因</param>
        /// <returns>字段权限配置列表</returns>
        private List<FieldPermissionInfo> GetApplyFieldPermissions(EnumServiceType serviceType, int changeReason)
        {
            var permissions = new List<FieldPermissionInfo>();

            // 根据服务类型和变更原因获取权限配置
            // 支持ServiceType=5（慧思服务通用）和具体服务类型
            var configs = Db.Queryable<Db_crm_service_change_reason_field_config>()
                .Where(x => (x.ServiceType == (int)serviceType || x.ServiceType == 5) &&
                           x.ChangeReasonEnum == changeReason &&
                           x.ApplyScenario == "apply" && // 申请场景
                           x.IsActive == 1 &&
                           x.Deleted != 1)
                .OrderBy(x => x.DisplayOrder)
                .ToList()
                .GroupBy(x => x.FieldKey)
                .Select(g => g.OrderBy(x => x.ServiceType == 5 ? 1 : 0).First()) // 优先选择具体服务类型，其次选择通用类型
                .OrderBy(x => x.DisplayOrder)
                .ToList();

            foreach (var config in configs)
            {
                permissions.Add(new FieldPermissionInfo
                {
                    FieldKey = config.FieldKey,
                    FieldName = config.FieldName,
                    Permission = config.FieldPermission ?? 2, // 默认为仅展示
                    DisplayOrder = config.DisplayOrder ?? 0,
                    Remark = ""
                });
            }

            return permissions;
        }

        /// <summary>
        /// 获取审核场景的字段权限配置
        /// </summary>
        /// <param name="serviceType">服务类型</param>
        /// <param name="changeReason">变更原因</param>
        /// <param name="changedFields">已修改的字段</param>
        /// <param name="fieldChangeRecords">字段变更记录字典</param>
        /// <returns>字段权限配置列表</returns>
        private List<FieldPermissionInfo> GetAuditFieldPermissions(EnumServiceType serviceType, int changeReason, 
            List<string> changedFields, Dictionary<string, Db_crm_service_change_field_record> fieldChangeRecords)
        {
            var permissions = new List<FieldPermissionInfo>();

            // 根据服务类型和变更原因获取权限配置
            // 支持ServiceType=5（慧思服务通用）和具体服务类型
            var configs = Db.Queryable<Db_crm_service_change_reason_field_config>()
                .Where(x => (x.ServiceType == (int)serviceType || x.ServiceType == 5) &&
                           x.ChangeReasonEnum == changeReason &&
                           x.ApplyScenario == "audit" && // 审核场景
                           x.IsActive == 1 &&
                           x.Deleted != 1)
                .OrderBy(x => x.DisplayOrder)
                .ToList()
                .GroupBy(x => x.FieldKey)
                .Select(g => g.OrderBy(x => x.ServiceType == 5 ? 1 : 0).First()) // 优先选择具体服务类型，其次选择通用类型
                .OrderBy(x => x.DisplayOrder)
                .ToList();

            foreach (var config in configs)
            {
                // 获取字段的默认权限（1-可修改，2-只读）
                var permission = config.FieldPermission ?? 2;
                bool shouldIncludeField = true; // 默认包含字段

                // 如果字段有TriggerFields配置，需要检查触发条件
                if (!string.IsNullOrEmpty(config.TriggerFields))
                {
                    var triggerFields = config.TriggerFields.Split(',').Select(tf => tf.Trim()).ToList();

                    // 如果任何一个触发字段在changedFields中，则该字段可修改
                    bool hasTriggeredField = triggerFields.Any(tf => changedFields.Contains(tf));

                    // 审核场景的逻辑：
                    // 1. 如果字段默认权限是可修改(1)，则始终包含该字段
                    // 2. 如果字段默认权限是只读(2)，只有在触发条件满足时才包含
                    if (config.FieldPermission == 1)
                    {
                        // 默认可修改的字段，始终包含
                        permission = 1;
                        shouldIncludeField = true;
                    }
                    else
                    {
                        // 默认只读的字段，只有触发时才可修改
                        if (hasTriggeredField)
                        {
                            permission = 1;
                            shouldIncludeField = true;
                        }
                        else
                        {
                            // 没有触发条件满足，不包含此字段
                            shouldIncludeField = false;
                        }
                    }
                }

                // 只有满足条件的字段才添加到结果中
                if (!shouldIncludeField)
                {
                    continue;
                }

                // 获取变更记录中的原始值和变更值
                string originValue = "";
                string changedValue = "";
                bool isArray = false;
                
                if (fieldChangeRecords.TryGetValue(config.FieldKey, out var changeRecord))
                {
                    originValue = changeRecord.OriginValue ?? "";
                    changedValue = changeRecord.ChangedValue ?? "";
                    isArray = changeRecord.IsArray ?? false;
                }
                
                permissions.Add(new FieldPermissionInfo
                {
                    FieldKey = config.FieldKey,
                    FieldName = config.FieldName,
                    Permission = permission,
                    DisplayOrder = config.DisplayOrder ?? 0,
                    Remark = "",
                    OriginValue = originValue,
                    ChangedValue = changedValue,
                    IsArray = isArray
                });
            }

            return permissions;
        }

        #region 测试
        // ================================
        // 测试用合同和产品信息方法
        // ================================

        /// <summary>
        /// 获取测试合同列表
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns>合同列表响应</returns>
        public TestContractListResponse GetTestContractList(TestContractListRequest request)
        {
            try
            {
                // 查询真实合同数据
                var query = Db.Queryable<Db_crm_contract>()
                    .Where(c => c.Deleted != true)
                    .Select(c => new TestContractInfo
                    {
                        Id = c.Id,
                        ContractNo = c.ContractNo,
                        CustomerName = "客户" + c.Id.Substring(0, 8), // 临时使用ID作为客户名称
                        ProductNames = "", // 稍后填充
                        CreateDate = c.CreateDate.HasValue ? c.CreateDate.Value.ToString("yyyy-MM-dd") : ""
                    });

                // 添加搜索条件
                if (!string.IsNullOrEmpty(request.SearchKey))
                {
                    query = query.Where(c =>
                        c.ContractNo.Contains(request.SearchKey));
                }

                // 分页查询
                var totalCount = query.Count();
                var contracts = query.OrderByDescending(c => c.CreateDate).Skip((request.PageIndex - 1) * request.PageSize)
                    .Take(request.PageSize)
                    .ToList();

                // 填充产品名称
                foreach (var contract in contracts)
                {
                    var products = Db.Queryable<Db_crm_contract_productinfo>()
                        .LeftJoin<Db_crm_product>((cpi, p) => cpi.ProductId == p.Id)
                        .Where((cpi, p) => cpi.ContractId == contract.Id && cpi.Deleted != true)
                        .Select((cpi, p) => p.ProductName)
                        .ToList();

                    contract.ProductNames = string.Join(",", products);
                }

                return new TestContractListResponse
                {
                    Success = true,
                    Message = "获取测试合同列表成功",
                    Data = new TestContractListData
                    {
                        List = contracts,
                        TotalCount = totalCount,
                        PageIndex = request.PageIndex,
                        PageSize = request.PageSize
                    }
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取测试合同列表异常: {ex}");
                return new TestContractListResponse
                {
                    Success = false,
                    Message = $"获取测试合同列表失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 获取测试合同产品信息
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns>合同产品列表响应</returns>
        public TestContractProductListResponse GetTestContractProductList(TestContractProductListRequest request)
        {
            try
            {
                // 查询真实合同产品数据
                var products = Db.Queryable<Db_crm_contract_productinfo>()
                    .LeftJoin<Db_crm_product>((cpi, p) => cpi.ProductId == p.Id)
                    .Where((cpi, p) => cpi.ContractId == request.ContractId && cpi.Deleted != true)
                    .Select((cpi, p) => new { cpi.Id, p.ProductName, p.ProductType })
                    .ToList()
                    .Select(p => new TestContractProductInfo
                    {
                        Id = p.Id,
                        ProductName = p.ProductName,
                        ProductType = p.ProductType ?? 1,
                        ServiceType = MapProductTypeToServiceType(p.ProductType ?? 1).ToInt()
                    })
                    .ToList();

                return new TestContractProductListResponse
                {
                    Success = true,
                    Message = "获取测试合同产品信息成功",
                    Data = products
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取测试合同产品信息异常: {ex}");
                return new TestContractProductListResponse
                {
                    Success = false,
                    Message = $"获取测试合同产品信息失败: {ex.Message}"
                };
            }
        }
        #endregion
        // ================================
        // 字段比对和存储方法
        // ================================

        /// <summary>
        /// 获取属性值（支持反射）
        /// </summary>
        /// <param name="obj">对象</param>
        /// <param name="propertyName">属性名</param>
        /// <returns>属性值</returns>
        private object GetPropertyValue(object obj, string propertyName)
        {
            if (obj == null || string.IsNullOrEmpty(propertyName))
                return null;

            // 处理嵌套属性，如"User.Address.City"
            if (propertyName.Contains("."))
            {
                var parts = propertyName.Split(new[] { '.' }, 2);
                var value = GetPropertyValue(obj, parts[0]);
                return GetPropertyValue(value, parts[1]);
            }

            // 获取属性信息
            var prop = obj.GetType().GetProperty(propertyName);
            if (prop == null)
                return null;

            // 获取属性值
            return prop.GetValue(obj);
        }

        /// <summary>
        /// 获取字段映射关系（基于前缀规则）
        /// </summary>
        /// <param name="configFieldKey">配置表中的字段Key</param>
        /// <param name="serviceType">服务类型</param>
        /// <returns>申请字段名和在服字段名</returns>
        private (string ApplyField, string ServiceField) GetFieldMapping(string configFieldKey, int serviceType)
        {
            // 所有字段都遵循统一的前缀规则，无需特殊映射

            // 获取服务前缀
            string servicePrefix = serviceType switch
            {
                1 => "Gtis",           // GTIS
                2 => "SalesWits",      // SalesWits
                3 => "GlobalSearch",   // GlobalSearch
                4 => "College",        // College
                5 => "",               // 通用字段，无前缀
                _ => ""
            };

            // 应用前缀规则
            string applyField, serviceField;
            
            if (serviceType == 5 || string.IsNullOrEmpty(servicePrefix))
            {
                // 通用字段，直接使用原字段名
                applyField = configFieldKey;
                serviceField = configFieldKey;
            }
            else if (configFieldKey.StartsWith(servicePrefix) && configFieldKey!="GtisApplCountry")
            {
                // 配置字段有前缀，模型字段去掉前缀
                var fieldWithoutPrefix = configFieldKey.Substring(servicePrefix.Length);
                applyField = fieldWithoutPrefix;
                serviceField = fieldWithoutPrefix;
            }
            else
            {
                // 配置字段无前缀，模型字段也无前缀
                applyField = configFieldKey;
                serviceField = configFieldKey;
            }

            return (applyField, serviceField);
        }

        /// <summary>
        /// 通用字段比对和生成变更记录方法
        /// </summary>
        /// <param name="applyId">申请ID</param>
        /// <param name="currentService">当前在服服务对象</param>
        /// <param name="newService">新申请服务对象</param>
        /// <param name="serviceType">服务类型</param>
        /// <param name="changeReasons">变更原因列表</param>
        /// <param name="userId">用户ID</param>
        /// <returns>变更记录列表</returns>
        public List<Db_crm_service_change_field_record> CompareAndGenerateFieldRecords(
            string applyId,
            object currentService,
            object newService,
            int serviceType,
            List<int> changeReasons,
            string userId)
        {
            if (currentService == null || newService == null)
                return new List<Db_crm_service_change_field_record>();

            var records = new List<Db_crm_service_change_field_record>();

            try
            {
                // 获取字段配置
                var fieldConfigs = Db.Queryable<Db_crm_service_change_reason_field_config>()
                    .Where(x => (x.ServiceType == serviceType || x.ServiceType == 5) &&
                               changeReasons.Contains(x.ChangeReasonEnum) &&
                               x.IsActive == 1 &&
                               x.Deleted != 1)
                    .OrderBy(x => x.DisplayOrder)
                    .ToList()
                    .GroupBy(x => new { x.FieldKey, x.ChangeReasonEnum })
                    .Select(g => g.OrderBy(x => x.ServiceType == 5 ? 1 : 0).First()) // 优先选择具体服务类型，其次选择通用类型
                    .OrderBy(x => x.DisplayOrder)
                    .ToList();

                foreach (var fieldConfig in fieldConfigs)
                {
                    var hasChanged = false;
                    string originValue = "";
                    string changedValue = "";

                    // 根据配置的FieldKey获取对应的申请字段名和在服字段名
                    var (applyFieldName, serviceFieldName) = GetFieldMapping(fieldConfig.FieldKey, serviceType);

                    // 获取字段值
                    var currentValue = GetPropertyValue(currentService, serviceFieldName);
                    var newValue = GetPropertyValue(newService, applyFieldName);

                    // 特殊字段处理：只有当新值不为0时才认为变更
                    var specialFields = new[] { "SalesWitsGiftResourceMonths", "SalesWitsAddCredit", "SalesWitsGiftTokenNum", "SalesWitsGiftEmailNum" };
                    if (specialFields.Contains(fieldConfig.FieldKey))
                    {
                        if (newValue is int newInt && newInt != 0)
                        {
                            hasChanged = true;
                            originValue = currentValue?.ToString() ?? "0";
                            changedValue = newInt.ToString();
                        }
                        else if (newValue is decimal newDecimal && newDecimal != 0)
                        {
                            hasChanged = true;
                            originValue = currentValue?.ToString() ?? "0";
                            changedValue = newDecimal.ToString();
                        }
                    }
                    else
                    {
                        // 根据字段类型进行比较
                        if (currentValue is DateTime currentDate && newValue is DateTime newDate)
                        {
                            if (currentDate.Date != newDate.Date)
                            {
                                hasChanged = true;
                                originValue = currentDate.ToString("yyyy-MM-dd");
                                changedValue = newDate.ToString("yyyy-MM-dd");
                            }
                        }
                        else if (currentValue is bool currentBool && newValue is bool newBool)
                        {
                            if (currentBool != newBool)
                            {
                                hasChanged = true;
                                originValue = currentBool ? "是" : "否";
                                changedValue = newBool ? "是" : "否";
                            }
                        }
                        else if (currentValue is int currentInt && newValue is int newInt)
                        {
                            if (currentInt != newInt)
                            {
                                hasChanged = true;
                                originValue = currentInt.ToString();
                                changedValue = newInt.ToString();
                            }
                        }
                        else if (currentValue is decimal currentDecimal && newValue is decimal newDecimal)
                        {
                            if (currentDecimal != newDecimal)
                            {
                                hasChanged = true;
                                originValue = currentDecimal.ToString();
                                changedValue = newDecimal.ToString();
                            }
                        }
                        else if (currentValue is string currentString && newValue is string newString)
                        {
                            if (!string.Equals(currentString, newString, StringComparison.Ordinal))
                            {
                                hasChanged = true;
                                originValue = currentString ?? "";
                                changedValue = newString ?? "";
                            }
                        }
                        else if (fieldConfig.IsArray == 1 || fieldConfig.FieldType == "list")
                        {
                            // 处理复杂对象字段
                            var (hasListChanged, listOriginValue, listChangedValue) = CompareComplexField(
                                currentValue, newValue, fieldConfig.FieldKey, fieldConfig.ComparisonMethod);
                            
                            if (hasListChanged)
                            {
                                hasChanged = true;
                                originValue = listOriginValue;
                                changedValue = listChangedValue;
                            }
                        }
                        else if (!Equals(currentValue, newValue))
                        {
                            hasChanged = true;
                            originValue = currentValue?.ToString() ?? "";
                            changedValue = newValue?.ToString() ?? "";
                        }
                    }

                    if (hasChanged)
                    {
                        records.Add(new Db_crm_service_change_field_record
                        {
                            ApplyId = applyId,
                            ServiceType = serviceType,
                            ChangeReasonEnum = changeReasons.FirstOrDefault(),
                            FieldKey = fieldConfig.FieldKey,
                            FieldName = fieldConfig.FieldName,
                            IsArray = fieldConfig.IsArray == 1,
                            OriginValue = originValue,
                            ChangedValue = changedValue,
                            CreateDate = DateTime.Now,
                            CreateUser = userId,
                            Deleted = false
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"比较字段变更异常: {ex}");
            }

            return records;
        }

        /// <summary>
        /// 比较复杂对象字段的变更
        /// </summary>
        /// <param name="currentValue">当前值</param>
        /// <param name="newValue">新值</param>
        /// <param name="fieldKey">字段键名</param>
        /// <param name="comparisonMethod">比较方式</param>
        /// <returns>变更信息</returns>
        private (bool hasChanged, string originValue, string changedValue) CompareComplexField(
            object currentValue, object newValue, string fieldKey, string comparisonMethod)
        {
            try
            {
                return comparisonMethod switch
                {
                    "list_diff" => CompareListWithDiff(currentValue, newValue, fieldKey),
                    "json_compare" => CompareWithJsonSerialization(currentValue, newValue, fieldKey),
                    _ => CompareWithJsonSerialization(currentValue, newValue, fieldKey) // 默认使用JSON比较
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"比较复杂对象字段异常，FieldKey: {fieldKey}, 错误: {ex}");
                return (false, "", "");
            }
        }

        /// <summary>
        /// 使用JSON序列化比较（适用于UserList等）
        /// </summary>
        private (bool hasChanged, string originValue, string changedValue) CompareWithJsonSerialization(
            object currentValue, object newValue, string fieldKey = "")
        {
            try
            {
                // 如果是用户列表字段，使用特殊处理
                if (fieldKey == "UserList" || fieldKey == "SalesWitsUserList")
                {
                    return CompareUserListWithIgnoredFields(currentValue, newValue);
                }
                
                // 使用排序的JSON序列化设置，确保字段顺序一致
                var jsonSettings = new JsonSerializerSettings
                {
                    ContractResolver = new OrderedContractResolver(),
                    Formatting = Formatting.None
                };
                
                var currentJson = JsonConvert.SerializeObject(currentValue, jsonSettings);
                var newJson = JsonConvert.SerializeObject(newValue, jsonSettings);
                
                if (currentJson != newJson)
                {
                    return (true, currentJson, newJson);
                }
                
                return (false, "", "");
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"JSON序列化比较异常: {ex}");
                return (false, "", "");
            }
        }

        /// <summary>
        /// 比较用户列表，只比较CompareUserListFields模型中定义的字段
        /// </summary>
        private (bool hasChanged, string originValue, string changedValue) CompareUserListWithIgnoredFields(
            object currentValue, object newValue)
        {
            try
            {
                // 获取CompareUserListFields模型中定义的所有属性名
                var compareFields = typeof(CompareUserListFields).GetProperties()
                    .Select(p => p.Name)
                    .ToArray();
                
                // 创建只包含CompareUserListFields模型中字段的JSON序列化设置
                var jsonSettings = new JsonSerializerSettings
                {
                    ContractResolver = new IncludeAndOrderedContractResolver(compareFields),
                    Formatting = Formatting.None
                };
                
                var currentJson = JsonConvert.SerializeObject(currentValue, jsonSettings);
                var newJson = JsonConvert.SerializeObject(newValue, jsonSettings);
                
                if (currentJson != newJson)
                {
                    return (true, currentJson, newJson);
                }
                
                return (false, "", "");
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"用户列表比较异常: {ex}");
                return (false, "", "");
            }
        }

        /// <summary>
        /// 忽略指定属性的JSON序列化解析器
        /// </summary>
        private class IgnorePropertiesResolver : DefaultContractResolver
        {
            private readonly string[] _ignoredProperties;

            public IgnorePropertiesResolver(string[] ignoredProperties)
            {
                _ignoredProperties = ignoredProperties;
            }

            protected override JsonProperty CreateProperty(MemberInfo member, MemberSerialization memberSerialization)
            {
                var property = base.CreateProperty(member, memberSerialization);
                
                if (_ignoredProperties.Contains(property.PropertyName))
                {
                    property.ShouldSerialize = instance => false;
                }
                
                return property;
            }
        }

        /// <summary>
        /// 按字段名排序的JSON序列化解析器
        /// </summary>
        private class OrderedContractResolver : DefaultContractResolver
        {
            protected override IList<JsonProperty> CreateProperties(Type type, MemberSerialization memberSerialization)
            {
                var properties = base.CreateProperties(type, memberSerialization);
                return properties.OrderBy(p => p.PropertyName).ToList();
            }
        }

        /// <summary>
        /// 忽略指定属性且按字段名排序的JSON序列化解析器
        /// </summary>
        private class IgnoreAndOrderedContractResolver : DefaultContractResolver
        {
            private readonly string[] _ignoredProperties;

            public IgnoreAndOrderedContractResolver(string[] ignoredProperties)
            {
                _ignoredProperties = ignoredProperties;
            }

            protected override IList<JsonProperty> CreateProperties(Type type, MemberSerialization memberSerialization)
            {
                var properties = base.CreateProperties(type, memberSerialization);
                
                // 过滤掉需要忽略的属性
                var filteredProperties = properties.Where(p => !_ignoredProperties.Contains(p.PropertyName)).ToList();
                
                // 按属性名排序
                return filteredProperties.OrderBy(p => p.PropertyName).ToList();
            }
        }

        /// <summary>
        /// 只包含指定属性且按字段名排序的JSON序列化解析器
        /// </summary>
        private class IncludeAndOrderedContractResolver : DefaultContractResolver
        {
            private readonly string[] _includedProperties;

            public IncludeAndOrderedContractResolver(string[] includedProperties)
            {
                _includedProperties = includedProperties;
            }

            protected override IList<JsonProperty> CreateProperties(Type type, MemberSerialization memberSerialization)
            {
                var properties = base.CreateProperties(type, memberSerialization);
                
                // 只包含指定的属性
                var filteredProperties = properties.Where(p => _includedProperties.Contains(p.PropertyName)).ToList();
                
                // 按属性名排序
                return filteredProperties.OrderBy(p => p.PropertyName).ToList();
            }
        }

        /// <summary>
        /// 列表差异比较（适用于GtisApplCountry等）
        /// </summary>
        private (bool hasChanged, string originValue, string changedValue) CompareListWithDiff(
            object currentValue, object newValue, string fieldKey)
        {
            try
            {
                // 根据字段类型进行特定处理
                switch (fieldKey)
                {
                    case "GtisApplCountry":
                    case "GtisApplyCountry":
                        return CompareGtisCountryList(currentValue, newValue);
                    
                    default:
                        // 默认使用JSON比较
                        return CompareWithJsonSerialization(currentValue, newValue, fieldKey);
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"列表差异比较异常，FieldKey: {fieldKey}, 错误: {ex}");
                return (false, "", "");
            }
        }

        /// <summary>
        /// 比较GtisApplCountry的变更
        /// </summary>
        private (bool hasChanged, string originValue, string changedValue) CompareGtisCountryList(
            object currentValue, object newValue)
        {
            try
            {
                List<int> currentCountries = new List<int>();
                List<int> newCountries = new List<int>();
                
                // 类型转换逻辑
                if (currentValue is List<VM_Contract.GtisApplCountry> currentList)
                {
                    currentCountries = currentList.Select(c => c.Sid).ToList();
                }
                else if (currentValue is IEnumerable<VM_Contract.GtisApplCountry> currentEnum)
                {
                    currentCountries = currentEnum.Select(c => c.Sid).ToList();
                }
                
                if (newValue is List<VM_Contract.GtisApplCountry> newList)
                {
                    newCountries = newList.Select(c => c.Sid).ToList();
                }
                else if (newValue is IEnumerable<VM_Contract.GtisApplCountry> newEnum)
                {
                    newCountries = newEnum.Select(c => c.Sid).ToList();
                }
                
                // 计算差异
                var deletedCountries = currentCountries.Except(newCountries).ToList();
                var addedCountries = newCountries.Except(currentCountries).ToList();
                
                if (deletedCountries.Any() || addedCountries.Any())
                {
                    var changeDetails = new JObject();
                    if (deletedCountries.Any())
                    {
                        changeDetails["DelSidList"] = JsonConvert.SerializeObject(deletedCountries);
                    }
                    if (addedCountries.Any())
                    {
                        changeDetails["AddSidList"] = JsonConvert.SerializeObject(addedCountries);
                    }
                    
                    return (true, 
                           JsonConvert.SerializeObject(currentCountries), 
                           JsonConvert.SerializeObject(changeDetails));
                }
                
                return (false, "", "");
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"比较GtisApplCountry异常: {ex}");
                return (false, "", "");
            }
        }

        /// <summary>
        /// 比较GTIS服务字段变更
        /// </summary>
        public List<Db_crm_service_change_field_record> CompareGtisFieldRecords(
            string applyId,
            GtisServiceInfo currentService,
            object newService,
            List<int> changeReasons,
            string userId)
        {
            return CompareAndGenerateFieldRecords(applyId, currentService, newService, 1, changeReasons, userId);
        }

        /// <summary>
        /// 比较GlobalSearch服务字段变更
        /// </summary>
        public List<Db_crm_service_change_field_record> CompareGlobalSearchFieldRecords(
            string applyId,
            GlobalSearchServiceInfo currentService,
            object newService,
            List<int> changeReasons,
            string userId)
        {
            return CompareAndGenerateFieldRecords(applyId, currentService, newService, 3, changeReasons, userId);
        }

        /// <summary>
        /// 比较College服务字段变更
        /// </summary>
        public List<Db_crm_service_change_field_record> CompareCollegeFieldRecords(
            string applyId,
            CollegeServiceInfo currentService,
            object newService,
            List<int> changeReasons,
            string userId)
        {
            return CompareAndGenerateFieldRecords(applyId, currentService, newService, 4, changeReasons, userId);
        }

        /// <summary>
        /// 比较SalesWits服务字段变更
        /// </summary>
        public List<Db_crm_service_change_field_record> CompareSalesWitsFieldRecords(
            string applyId,
            SalesWitsServiceInfo currentService,
            object newService,
            List<int> changeReasons,
            string userId)
        {
            return CompareAndGenerateFieldRecords(applyId, currentService, newService, 2, changeReasons, userId);
        }
    }
}