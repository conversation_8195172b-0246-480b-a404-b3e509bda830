using CRM2_API.BLL;
using CRM2_API.BLL.Common;
using CRM2_API.Common.Cache;
using CRM2_API.DAL.DbCommon;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.System;
using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Drawing.Charts;
using DocumentFormat.OpenXml.ExtendedProperties;
using DocumentFormat.OpenXml.Presentation;
using DocumentFormat.OpenXml.Spreadsheet;
using DocumentFormat.OpenXml.Wordprocessing;
using LgyUtil;
using MimeKit.Utils;
using NPOI.POIFS.Properties;
using NPOI.SS.Formula.Functions;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Database;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using Org.BouncyCastle.Asn1.Cms;
using Org.BouncyCastle.Crypto;
using Org.BouncyCastle.Pqc.Crypto.Lms;
using SixLabors.ImageSharp.ColorSpaces;
using SqlSugar;
using SqlSugar.Extensions;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics.Contracts;
using System.Linq;
using System.Runtime.ConstrainedExecution;
using System.Xml.Linq;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.ControllersViewModel.VM_Collectioninfo;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;
using static CRM2_API.Model.ControllersViewModel.VM_ContractInvoice;
using static Quartz.Logging.OperationName;
using JiebaNet.Segmenter.Common;

namespace CRM2_API.DAL.DbModelOpe.Crm2
{
    /// <summary>
    /// crm_contract表操作
    /// </summary>
    public partial class DbOpe_crm_contract : DbOperateCrm2Ex<Db_crm_contract, DbOpe_crm_contract>
    {

        public void CopyContract(CopyContract_In addContractIn, string contractId, string contractPaperEntityId, string contractElectronicEntityId, string userId, string? OrgDivisionId, string OrgDivisionName, string? OrgBrigadeId, string OrgBrigadeName, string? OrgRegimentId, string OrgRegimentName, string HistoryContractId, decimal? ContractAmountConvert, string CompanyAddressAddress, string CompanyAddressPostalCode, string MergeCompanyAuditId, int? MergeCompanyAuditState, bool? IsMergedCompany)
        {
            Db_crm_contract crm_contract = addContractIn.MappingTo<Db_crm_contract>();
            crm_contract.Id = contractId;
            crm_contract.ContractPaperEntityId = contractPaperEntityId;
            crm_contract.ContractElectronicEntityId = contractElectronicEntityId;
            crm_contract.Issuer = userId;
            crm_contract.UsageStatus = EnumUsageStatus.UnUse.ToInt();
            crm_contract.RecoveryStatus = EnumRecoveryStatus.Unrecycled.ToInt();
            crm_contract.IsSecret = false;
            crm_contract.IsUrgeRegistration = EnumUrgeRegistration.NoReminder.ToInt();
            crm_contract.OrgDivisionId = OrgDivisionId;
            crm_contract.OrgDivisionName = OrgDivisionName;
            crm_contract.OrgBrigadeId = OrgBrigadeId;
            crm_contract.OrgBrigadeName = OrgBrigadeName;
            crm_contract.OrgRegimentId = OrgRegimentId;
            crm_contract.OrgRegimentName = OrgRegimentName;
            crm_contract.HistoryContractId = HistoryContractId;
            crm_contract.IsNotShowReceiptRegister = false;
            crm_contract.StampReviewStatus = EnumStampReviewStatus.NotUploaded.ToInt();
            crm_contract.OtherStampReviewStatus = EnumOtherStampReviewStatus.NotUploaded.ToInt();
            crm_contract.Deleted = false;
            crm_contract.CreateUser = userId;
            crm_contract.CreateDate = DateTime.Now;
            crm_contract.ContractAmountConvert = ContractAmountConvert;
            crm_contract.CompanyAddressAddress = CompanyAddressAddress;
            crm_contract.CompanyAddressPostalCode = CompanyAddressPostalCode;
            crm_contract.MergeCompanyAuditId = MergeCompanyAuditId;
            crm_contract.MergeCompanyAuditState = MergeCompanyAuditState;
            crm_contract.IsMergedCompany = IsMergedCompany;
            Insert(crm_contract);
        }

        public void AddContract(AddContract_In addContractIn, string contractId, string contractPaperEntityId, string contractElectronicEntityId, string userId, string? OrgDivisionId, string OrgDivisionName, string? OrgBrigadeId, string OrgBrigadeName, string? OrgRegimentId, string OrgRegimentName, decimal? ContractAmountConvert, string CompanyAddressAddress, string CompanyAddressPostalCode)
        {
            Db_crm_contract crm_contract = addContractIn.MappingTo<Db_crm_contract>();
            crm_contract.Id = contractId;
            crm_contract.ContractPaperEntityId = contractPaperEntityId;
            crm_contract.ContractElectronicEntityId = contractElectronicEntityId;
            crm_contract.Issuer = userId;
            crm_contract.UsageStatus = EnumUsageStatus.UnUse.ToInt();
            crm_contract.RecoveryStatus = EnumRecoveryStatus.Unrecycled.ToInt();
            crm_contract.IsSecret = false;
            crm_contract.IsUrgeRegistration = EnumUrgeRegistration.NoReminder.ToInt();
            crm_contract.OrgDivisionId = OrgDivisionId;
            crm_contract.OrgDivisionName = OrgDivisionName;
            crm_contract.OrgBrigadeId = OrgBrigadeId;
            crm_contract.OrgBrigadeName = OrgBrigadeName;
            crm_contract.OrgRegimentId = OrgRegimentId;
            crm_contract.OrgRegimentName = OrgRegimentName;
            crm_contract.IsNotShowReceiptRegister = false;
            crm_contract.StampReviewStatus = EnumStampReviewStatus.NotUploaded.ToInt();
            crm_contract.OtherStampReviewStatus = EnumOtherStampReviewStatus.NotUploaded.ToInt();
            crm_contract.Deleted = false;
            crm_contract.CreateUser = userId;
            crm_contract.CreateDate = DateTime.Now;
            crm_contract.ContractAmountConvert = ContractAmountConvert;
            crm_contract.CompanyAddressAddress = CompanyAddressAddress;
            crm_contract.CompanyAddressPostalCode = CompanyAddressPostalCode;
            crm_contract.IsMergedCompany = false;
            crm_contract.ParentContractId = addContractIn.ParentContractId;
            Insert(crm_contract);
        }

        public Db_crm_contract GetContractById(string id, bool dataOwner = false)
        {
            return Queryable
                .LeftJoinIF<Db_v_customer_subcompany_private_user>(dataOwner, ((r, cspu) => r.FirstParty == cspu.Id))
                .Where((r) => r.Id == id && r.Deleted == false)
                .WhereIF(dataOwner, DataOwner.BusinessData("r.Issuer", "r.Id", "cspu.CompanyCurrentUser"))
                .First();
        }

        public Db_crm_contract GetContractByContractNum(string contractNum, bool dataOwner = false)
        {
            return Queryable
                .LeftJoinIF<Db_v_customer_subcompany_private_user>(dataOwner, ((r, cspu) => r.FirstParty == cspu.Id))
                .Where(r => r.ContractNum == contractNum && r.Deleted == false)
                .WhereIF(dataOwner, DataOwner.BusinessData("r.Issuer", "r.Id", "cspu.CompanyCurrentUser"))
                .First();
        }

        public List<Db_crm_contract> GetContractByIds(List<string> ids, bool dataOwner = false)
        {
            return Queryable
                .Where(r => ids.Contains(r.Id) && r.Deleted == false)
                //.WhereIF(dataOwner, DataOwner.BusinessData<Db_crm_contract>(r => r.CreateUser, r => ids.Contains(r.Id)))
                .ToList();
        }

        public Db_crm_contract GetContractByContractAudit(string id)
        {
            return Queryable.LeftJoin<Db_crm_contract_audit>((e, r) => e.Id == r.ContractId).Where((e, r) => r.Id == id && r.Deleted == false && e.Deleted == false && r.IsHistory == false).Select<Db_crm_contract>((e, r) => e).First();
        }

        public Db_crm_contract GetContractByContractReviewAudit(string id)
        {
            return Queryable.LeftJoin<Db_crm_contract_initialaudit>((e, r) => e.Id == r.ContractId).Where((e, r) => r.Id == id && r.Deleted == false && e.Deleted == false && r.IsHistory == false).Select<Db_crm_contract>((e, r) => e).First();
        }

        public Db_crm_contract GetContractByContractDivisionAudit(string id)
        {
            return Queryable.LeftJoin<Db_crm_contract_divisionaudit>((e, r) => e.Id == r.ContractId).Where((e, r) => r.Id == id && r.Deleted == false && e.Deleted == false && r.IsHistory == false).Select<Db_crm_contract>((e, r) => e).First();
        }

        public Db_crm_contract GetContractByContractBrigadeAudit(string id)
        {
            return Queryable.LeftJoin<Db_crm_contract_brigadeaudit>((e, r) => e.Id == r.ContractId).Where((e, r) => r.Id == id && r.Deleted == false && e.Deleted == false && r.IsHistory == false).Select<Db_crm_contract>((e, r) => e).First();
        }

        public Db_crm_contract GetContractByContractRegimentAudit(string id)
        {
            return Queryable.LeftJoin<Db_crm_contract_regimentaudit>((e, r) => e.Id == r.ContractId).Where((e, r) => r.Id == id && r.Deleted == false && e.Deleted == false && r.IsHistory == false).Select<Db_crm_contract>((e, r) => e).First();
        }

        public Db_crm_contract GetContractByContractStampreViewAudit(string id)
        {
            return Queryable.LeftJoin<Db_crm_contract_stampreview>((e, r) => e.Id == r.ContractId).Where((e, r) => r.Id == id && r.Deleted == false && e.Deleted == false && r.IsHistory == false).Select<Db_crm_contract>((e, r) => e).First();
        }

        public Db_crm_contract GetContractByContractOtherStampreViewAudit(string id)
        {
            return Queryable.LeftJoin<Db_crm_contract_otherstampreview>((e, r) => e.Id == r.ContractId).Where((e, r) => r.Id == id && r.Deleted == false && e.Deleted == false && r.IsHistory == false).Select<Db_crm_contract>((e, r) => e).First();
        }

        public Db_crm_contract GetContractByContractProductInfoId(string id, bool dataOwner = false)
        {
            //240912 合同、发票、到账权限更改
            return Queryable
                .LeftJoin<Db_crm_contract_productinfo>((e, r) => e.Id == r.ContractId)
                .LeftJoinIF<Db_v_customer_subcompany_private_user>(dataOwner, ((e, r, cspu) => e.FirstParty == cspu.Id))
                .Where((e, r) => r.Id == id && r.Deleted == false && e.Deleted == false)
                .WhereIF(dataOwner, DataOwner.BusinessData("e.Issuer", "e.Id", "cspu.CompanyCurrentUser"))
                .Select<Db_crm_contract>((e, r) => e)
                .First();
        }

        /// <summary>
        /// 根据合同产品系列Id获取合同信息
        /// </summary>
        /// <param name="seriesId"></param>
        /// <param name="dataOwner"></param>
        /// <returns></returns>
        public Db_crm_contract GetContractByContractProductInfoSeriesId(string seriesId, bool dataOwner = false)
        {
            return Queryable
                .LeftJoin<Db_crm_contract_productinfo>((e, r) => e.Id == r.ContractId)
                .LeftJoinIF<Db_v_customer_subcompany_private_user>(dataOwner, ((e, r, cspu) => e.FirstParty == cspu.Id))
                .Where((e, r) => r.SeriesId == seriesId && r.Deleted == false && e.Deleted == false)
                .WhereIF(dataOwner, DataOwner.BusinessData("e.Issuer", "e.Id", "cspu.CompanyCurrentUser"))
                .Select<Db_crm_contract>((e, r) => e)
                .First();
        }

        public void UpdateContract(UpdateContract_In updateContractIn)
        {
            //Db_crm_contract crm_contract = updateContractIn.MappingTo<Db_crm_contract>();
            //var dt = new Dictionary<string, object>();
            //dt.Add("UpdateUser", userId);
            //dt.Add("UpdateDate", DateTime.Now);
            //Db_crm_contract pi = updateContractIn.MappingTo<Db_crm_contract>();
            //pi.UpdateUser = userId;
            //pi.UpdateDate = DateTime.Now;
            //Type type = updateContractIn.GetType();//获取类型
            //Type typeDB = crm_contract.GetType();//获取类型
            //PropertyInfo[] properties = type.GetProperties();
            ////PropertyInfo[] propertiesDB = typeDB.GetProperties();
            //string tableName = ((SugarTable)(typeDB.GetCustomAttributes(true)[2])).TableName;
            //for (int i = 0; i < properties.Length; i++){
            //    string name = properties[i].Name;
            //    object? value = properties[i].GetValue(updateContractIn);
            //    if (typeDB.GetProperty(name) != null)
            //    {
            //        dt.Add(name, value);
            //    }
            //}
            //Db.Updateable(dt).AS(tableName).WhereColumns("Id").ExecuteCommand();
            UpdateData<UpdateContract_In>(updateContractIn);
        }

        public void UpdateContractStatus(string id, int contractStatus)
        {
            UpdateData(r => new Db_crm_contract() { ContractStatus = contractStatus }, id);
        }

        public void UpdateContractStatusToCancelByAutoVoid(string id)
        {
            int contractStatus = EnumContractStatus.Cancel.ToInt();
            UpdateData(r => new Db_crm_contract() { ContractStatus = contractStatus, IsAutoVoid = true, AutoVoidDate = DateTime.Now }, id);
        }

        public ContractBasicInfo_Out GetContractBasicInfoById(string id, bool dataOwner = false)
        {
            //return Db.Queryable<Db_crm_contract, Db_crm_customer, Db_crm_customer_subcompany>
            //    ((r, c, cs) => new object[] { JoinType.Left, r.CustomerId == c.Id, JoinType.Left, c.Id == cs.CustomerId && cs.IsMain == 1 })
            //    .Select<Contract_Out>().First();
            //var selector = new List<SelectModel>() {
            //    new SelectModel(){ AsName = "CompanyName",FiledName = "cs.CompanyName"}
            //};
            var list = Queryable
                .LeftJoin<Db_crm_customer_subcompany>((r, css) => r.FirstParty == css.Id)
                .LeftJoin<Db_crm_customer_subcompany>((r, css, cs) => css.CustomerId == cs.CustomerId && cs.IsMain == 1 && cs.Deleted == 0)
                .LeftJoin<Db_v_userwithorg>((r, css, cs, u) => r.Issuer == u.Id)
                .LeftJoin<Db_crm_collectingcompany>((r, css, cs, u, co) => r.SecondParty == co.Id)
                .LeftJoin<Db_sys_country>((r, css, cs, u, co, cou) => r.Country == cou.Id)
                .LeftJoin<Db_sys_province>((r, css, cs, u, co, cou, pr) => r.Province == pr.Id)
                .LeftJoin<Db_sys_city>((r, css, cs, u, co, cou, pr, ci) => r.City == ci.Id)
                .LeftJoin<Db_v_productserviceinfostatus>((r, css, cs, u, co, cou, pr, ci, vp) => r.Id == vp.ContractId)
                //.LeftJoin<Db_crm_customer>((r, css,cs, u, co, cou, pr, ci, vp, cu) => r.CustomerId == cu.Id)
                //.LeftJoin<Db_crm_customer>((r, css,cs, u, co, cou, pr, ci, vp, cu, cuu) => css.CustomerId == cuu.Id)
                .LeftJoin<Db_crm_customer>((r, css, cs, u, co, cou, pr, ci, vp, cu) => css.CustomerId == cu.Id)
                .LeftJoin<Db_crm_contract_audit>((r, css, cs, u, co, cou, pr, ci, vp, cu, ca) => r.Id == ca.ContractId && ca.IsHistory == false && ca.Deleted == false)
                .LeftJoin<Db_v_customer_subcompany_private_user>((r, css, cs, u, co, cou, pr, ci, vp, cu, ca, cspu) => cspu.Id == r.FirstParty)
                .Where((r, css, cs, u, co, cou, pr, ci, vp, cu, ca, cspu) => r.Id == id && r.Deleted == false)
                .WhereIF(dataOwner, DataOwner.BusinessData("r.Issuer", "r.Id", "cspu.CompanyCurrentUser"))
                .Select((r, css, cs, u, co, cou, pr, ci, vp, cu, ca, cspu) => new ContractBasicInfo_Out
                {
                    Id = r.Id.SelectAll(),
                    CustomerName = cs.CompanyName,
                    FirstPartyName = css.CompanyName,
                    IssuerName = u.Name,
                    SecondPartyName = co.CollectingCompanyName,
                    CountryName = cou.Name,
                    ProvinceName = pr.Name,
                    CityName = ci.Name,
                    ProductServiceInfoStatus = vp.ProductServiceInfoStatus,
                    CustomerNum = cu.CustomerNum,
                    AuditType = ca.AuditType.Value,
                    CreditType = css.CreditType,
                    TrackingStage = cu.TrackingStage,
                    TemplateType = co.TemplateType,
                    ChineseTemplateCurrency = co.ChineseTemplateCurrency,
                    EnglishTemplateCurrency = co.EnglishTemplateCurrency,
                    IsBothChineseAndEnglish = co.IsBothChineseAndEnglish,
                    ExactCustomerId = css.CustomerId
                    //ParentContractId = r.ParentContractId,
                })
                .MergeTable()
                .LeftJoin<Db_crm_mergecompany_relationship>((o, relation) => relation.MergeCompanyAuditId == o.MergeCompanyAuditId)
                .LeftJoin<Db_crm_customer_subcompany>((o, relation, relationCompany) => relation.SubCustomerMainCompanyId == relationCompany.Id)
                .Select((o, relation, relationCompany) => new ContractBasicInfo_Out
                {
                    Id = o.Id.SelectAll(),
                    MergeCompanyName = relationCompany.CompanyName,
                    MergeRelationState = relation.State
                })
                .Mapper(it =>
                {
                    it.CustomerId = it.ExactCustomerId;
                    it.MergeRelationStateName = it.MergeRelationState == null ? "" : ((EnumCustomerMergeAuditState)it.MergeRelationState).GetEnumDescription();
                    it.SalesCountryName = LocalCache.LC_Address.CountryAndAreaCache.Find(c => c.Id == it.SalesCountry)?.Name ?? "";
                })
                .ToList();
            if (list.Count == 0)
                throw new ApiException("合同数据不存在，请重新核对");
            Db.ThenMapper(list, item =>
            {
                item.ContractFile = Db.Queryable<Db_crm_contract_attachfile>()
                .Where(r => r.Deleted == false)
                .SetContext(scl => scl.ContractId, () => item.Id, item)
                .OrderByDescending(r => r.CreateDate)
                .Select(r => new BM_FileInfo { Id = r.Id, FileName = r.FileName, FilePath = r.FilePath, CreateDate = r.CreateDate.ToString() }).ToList();

                item.ContractSpecialAttachFile = Db.Queryable<Db_crm_contract_special_attachfile>()
                .Where(r => r.Deleted == false)
                .SetContext(scl => scl.ContractId, () => item.Id, item)
                .OrderByDescending(r => r.CreateDate)
                .Select(r => new BM_FileInfo { Id = r.Id, FileName = r.FileName, FilePath = r.FilePath, CreateDate = r.CreateDate.ToString() }).ToList();

                item.ElectronicContractAttachFile = Db.Queryable<Db_crm_contract_electroniccontract_attachfile>()
                .Where(r => r.Deleted == false)
                .SetContext(scl => scl.ContractId, () => item.Id, item)
                .OrderByDescending(r => r.CreateDate)
                .Select(r => new BM_FileInfo { Id = r.Id, FileName = r.FileName, FilePath = r.FilePath, CreateDate = r.CreateDate.ToString() }).ToList();

                item.DBOriginalContractAttachFile = Db.Queryable<Db_crm_contract_dboriginalcontract_attachfile>()
                .Where(r => r.Deleted == false)
                .SetContext(scl => scl.ContractId, () => item.Id, item)
                .OrderByDescending(r => r.CreateDate)
                .Select(r => new BM_FileInfo { Id = r.Id, FileName = r.FileName, FilePath = r.FilePath, CreateDate = r.CreateDate.ToString() }).ToList();

                item.SealedContractAttachFile = Db.Queryable<Db_crm_contract_sealedcontract_attachfile>()
                .Where(r => r.Deleted == false)
                .SetContext(scl => scl.ContractId, () => item.Id, item)
                .OrderByDescending(r => r.CreateDate)
                .Select(r => new BM_FileInfo { Id = r.Id, FileName = r.FileName, FilePath = r.FilePath, CreateDate = r.CreateDate.ToString() }).ToList();

                item.OtherContractAttachFile = Db.Queryable<Db_crm_contract_othercontract_attachfile>()
                .Where(r => r.Deleted == false)
                .SetContext(scl => scl.ContractId, () => item.Id, item)
                .OrderByDescending(r => r.CreateDate)
                .Select(r => new BM_FileInfo { Id = r.Id, FileName = r.FileName, FilePath = r.FilePath, CreateDate = r.CreateDate.ToString() }).ToList();

                item.ContractTemplateList = Db.Queryable<Db_crm_contract_contracttemplate>()
                .Where(r => r.Deleted == false)
                .OrderBy(r => r.OrderNum)
                .SetContext(scl => scl.ContractId, () => item.Id, item)
                .Select(r => new ContractTemplateList { Id = r.Id, ContractId = r.ContractId, ContractTemplateId = r.ContractTemplateId, ContractDescription = r.ContractDescription }).ToList();

                item.ContractTermTemplate = Db.Queryable<Db_crm_contractterms>()
                .LeftJoin<Db_crm_contractterms_template>((r, c) => r.Term_Template_Id == c.Id)
                .Where((r, c) => r.Deleted == false)
                .Select((r, c) => new
                {
                    Id = r.Id,
                    Term_Template_Id = r.Term_Template_Id,
                    Term_Contract_Id = r.Term_Contract_Id,
                    Sort_Index = r.Sort_Index,
                    Term_Template_Status = c.Term_Template_Status,
                    Term_Status = r.Term_Status,
                    Content = c.Content,
                    Applicable_Areas = c.Applicable_Areas,
                    Applicable_Years = c.Applicable_Years,
                    AuditDate = c.AuditDate,
                    AuditUser = c.AuditUser,
                    Audit_Flow_Type = c.Audit_Flow_Type,
                    Beijing_Audit_Status = c.Beijing_Audit_Status,
                    EndDate = c.EndDate,
                    Group_Sort_Index = c.Sort_Index,
                    Last_Audit_Id = c.Last_Audit_Id,
                    Marketing_Audit_Status = c.Marketing_Audit_Status,
                    One_Time = c.One_Time,
                    ProductsRuleDescription = c.ProductsRuleDescription,
                    Remark = c.Remark,
                    Source_Contract_Id = c.Source_Contract_Id,
                    Specified_Customers = c.Specified_Customers,
                    StartDate = c.StartDate,
                    Term_Audit_OrgType = c.Term_Audit_OrgType,
                    Term_Audit_Process = c.Term_Audit_Process,
                    Term_Template_Level = c.Term_Template_Level,
                })
                .SetContext(r => r.Term_Contract_Id, () => item.Id, item)
                .Select(r => new ContractTermTemplateList
                {
                    Id = r.Id,
                    Term_Template_Id = r.Term_Template_Id,
                    Term_Contract_Id = r.Term_Contract_Id,
                    Sort_Index = r.Sort_Index,
                    Term_Template_Status = r.Term_Template_Status,
                    Term_Status = r.Term_Status,
                    Content = r.Content,
                    Applicable_Areas = r.Applicable_Areas,
                    Applicable_Years = r.Applicable_Years,
                    AuditDate = r.AuditDate,
                    AuditUser = r.AuditUser,
                    Audit_Flow_Type = r.Audit_Flow_Type,
                    Beijing_Audit_Status = r.Beijing_Audit_Status,
                    EndDate = r.EndDate,
                    Group_Sort_Index = r.Group_Sort_Index,
                    Last_Audit_Id = r.Last_Audit_Id,
                    Marketing_Audit_Status = r.Marketing_Audit_Status,
                    One_Time = r.One_Time,
                    ProductsRuleDescription = r.ProductsRuleDescription,
                    Remark = r.Remark,
                    Source_Contract_Id = r.Source_Contract_Id,
                    Specified_Customers = r.Specified_Customers,
                    StartDate = r.StartDate,
                    Term_Audit_OrgType = r.Term_Audit_OrgType,
                    Term_Audit_Process = r.Term_Audit_Process,
                    Term_Template_Level = r.Term_Template_Level,
                }).OrderBy(r => r.Sort_Index).ToList();

                var AccountTerminationDate = Db.Queryable<Db_v_contract_serviceinfo>()
                .SetContext(scl => scl.ContractId, () => item.Id, item)
                .Select(r => r.ServiceCycleEnd).Max();//SqlFunc.AggregateMax(r.c) ).ToList();
                item.AccountTerminationDate = AccountTerminationDate == null ? "" : AccountTerminationDate.Value.ToString("yyyy-MM-dd");


                var InvoiceStateList = Db.Queryable<Db_v_contract_invoice_state>()
                .SetContext(scl => scl.ContractId, () => item.Id, item)
                .Select(r => (int)r.DisplayStatus).ToList();
                if (InvoiceStateList != null && InvoiceStateList.Count > 0)
                    item.IsInvoice = InvoiceStateList.Max();

                // var SalesCountryNames =  Db.Queryable<Db_sys_country>()
                // .SetContext(scl => scl.Id, () => item.SalesCountry, item).ToList();
                // item.SalesCountryName = SalesCountryNames.Count > 0 ? SalesCountryNames.First().Name : "";

                var CompanyAddressNames = Db.Queryable<Db_sys_companyaddress>()
                .SetContext(scl => scl.Id, () => item.CompanyAddressId, item).ToList();
                item.CompanyAddressName = CompanyAddressNames.Count > 0 ? CompanyAddressNames.First().Name : "";

                var FirstPartySubCompanys = Db.Queryable<Db_v_subcompany_related>()
                .LeftJoin<Db_v_subcompany_related>((r, rr) => r.CustomerId == rr.CustomerId && rr.Deleted == 0)
                .Where((r, rr) => r.Deleted == 0 && r.Id != rr.Id && rr.Id == item.FirstParty)
                .Select(r => r.CompanyName).ToList();
                item.FirstPartySubCompany = FirstPartySubCompanys;

                var FirstPartyRelateds = Db.Queryable<Db_v_subcompany_related>()
                .Where((r) => r.Deleted == 0)
                .SetContext(r => r.Id, () => item.FirstParty, item)
                .Select(r => r.Relateds).ToList();
                item.FirstPartyRelated = FirstPartyRelateds;

                var RelationshipDescription = Db.Queryable<Db_crm_mergecompany_audit>()
                .SetContext(scl => scl.Id, () => item.MergeCompanyAuditId, item).ToList();
                item.RelationshipDescription = RelationshipDescription.Count > 0 ? RelationshipDescription.First().RelationshipDescription : "";

                item.MergeCompanyAttachFile = Db.Queryable<Db_crm_mergecompany_attachfile>()
                .Where(r => r.Deleted == false)
                .SetContext(scl => scl.AuditId, () => item.MergeCompanyAuditId, item)
                .OrderByDescending(r => r.CreateDate)
                .Select(r => new BM_FileInfo { Id = r.Id, FileName = r.FileName, FilePath = r.FilePath, CreateDate = r.CreateDate.ToString() }).ToList();

                item.ModifyContractTemplateAddRow = Db.Queryable<Db_crm_contract_contracttemplate_addrow>()
                .Where(r => r.Deleted == false)
                .SetContext(scl => scl.ContractId, () => item.Id, item)
                .OrderBy(r => r.TemplateType)
                .Select(r => new ContractTemplateAddRow_Out { Id = r.Id, ContractId = r.ContractId, TemplateType = r.TemplateType, Currency = r.Currency, ProductTable = r.ProductTable, Item = r.Item }).ToList();

                var RegimentAuditList = Db.Queryable<Db_crm_contract_regimentaudit>()
                .Where(r => r.Deleted == false && r.IsHistory == false)
                .SetContext(scl => scl.ContractId, () => item.Id, item).ToList();
                item.RegimentAuditId = RegimentAuditList.Count > 0 ? RegimentAuditList.First().Id : "";

                var BrigadeAuditList = Db.Queryable<Db_crm_contract_brigadeaudit>()
                .Where(r => r.Deleted == false && r.IsHistory == false)
                .SetContext(scl => scl.ContractId, () => item.Id, item).ToList();
                item.BrigadeAuditId = BrigadeAuditList.Count > 0 ? BrigadeAuditList.First().Id : "";

                var DivisionAuditList = Db.Queryable<Db_crm_contract_divisionaudit>()
                .Where(r => r.Deleted == false && r.IsHistory == false)
                .SetContext(scl => scl.ContractId, () => item.Id, item).ToList();
                item.DivisionAuditId = DivisionAuditList.Count > 0 ? DivisionAuditList.First().Id : "";

                var firstPartyGtisProduct = CheckFirstPartyHaveGtisOrVip(item.FirstParty);
                if (firstPartyGtisProduct != null && firstPartyGtisProduct.Count > 0 && firstPartyGtisProduct.Any(e => e.ServiceState == (int)EnumContractServiceState.VALID))
                    item.CustomerGtisState = EnumCustomerGtisState.Valid;
                else if (firstPartyGtisProduct != null && firstPartyGtisProduct.Count > 0 && firstPartyGtisProduct.Any(e => e.ServiceState == (int)EnumContractServiceState.OUT))
                    item.CustomerGtisState = EnumCustomerGtisState.Out;
                else if (firstPartyGtisProduct == null || firstPartyGtisProduct.Count == 0)
                {//当前甲方公司的合同中不存在gtis或vip产品，继续检查当前客户的其他公司是否存在gtis或vip产品
                    var otherCompanyList = DbOpe_crm_customer_subcompany.Instance.GetCustomerOtherCompany(item.CustomerId, item.FirstParty);
                    if (otherCompanyList != null && otherCompanyList.Count > 0)
                    {
                        var customerGtisProduct = DbOpe_crm_contract.Instance.CheckFirstPartyHaveGtisOrVip(otherCompanyList);
                        if (customerGtisProduct != null && customerGtisProduct.Count > 0 && customerGtisProduct.Any(e => e.ServiceState == (int)EnumContractServiceState.VALID))
                            item.CustomerGtisState = EnumCustomerGtisState.HaveValidGtisInSameCustomer;
                        else if (customerGtisProduct != null && customerGtisProduct.Count > 0 && customerGtisProduct.Any(e => e.ServiceState == (int)EnumContractServiceState.OUT))
                            item.CustomerGtisState = EnumCustomerGtisState.HaveOutGtisInSameCustomer;
                    }
                    else
                        item.CustomerGtisState = EnumCustomerGtisState.NewCustomer;
                }
                else
                    item.CustomerGtisState = EnumCustomerGtisState.NewCustomer;

                if (item.ParentContractId.IsNotNullOrEmpty())
                {
                    item.ParentContractFileList = Db.Queryable<Db_crm_contract>().Where(c => c.Deleted == false)
                                                      .Where(c => c.Id == item.ParentContractId || c.ParentContractId == item.ParentContractId)
                                                      .Where(c => c.Id != item.Id)
                                                      .Select(c => new ParentContractFile
                                                      {
                                                          ContractId = c.Id,
                                                          ContractType = c.ContractType.Value,

                                                      }).Mapper(file =>
                                                      {
                                                          file.ContractFile = Db.Queryable<Db_crm_contract_attachfile>()
                                                                                .Where(r => r.Deleted == false)
                                                                                .Where(r => r.ContractId == file.ContractId)
                                                                                //.SetContext(scl => scl.ContractId, () => file.ContractId, file)
                                                                                .OrderByDescending(r => r.CreateDate)
                                                                                .Select(r => new BM_FileInfo { Id = r.Id, FileName = r.FileName, FilePath = r.FilePath, CreateDate = r.CreateDate.ToString() }).ToList();

                                                          file.ContractSpecialAttachFile = Db.Queryable<Db_crm_contract_special_attachfile>()
                                                                                              .Where(r => r.Deleted == false)
                                                                                              .Where(r => r.ContractId == file.ContractId)
                                                                                              .OrderByDescending(r => r.CreateDate)
                                                                                              .Select(r => new BM_FileInfo { Id = r.Id, FileName = r.FileName, FilePath = r.FilePath, CreateDate = r.CreateDate.ToString() }).ToList();

                                                          file.DBOriginalContractAttachFile = Db.Queryable<Db_crm_contract_dboriginalcontract_attachfile>()
                                                                                                  .Where(r => r.Deleted == false)
                                                                                                  .Where(r => r.ContractId == file.ContractId)
                                                                                                  .OrderByDescending(r => r.CreateDate)
                                                                                                  .Select(r => new BM_FileInfo { Id = r.Id, FileName = r.FileName, FilePath = r.FilePath, CreateDate = r.CreateDate.ToString() }).ToList();

                                                          file.SealedContractAttachFile = Db.Queryable<Db_crm_contract_sealedcontract_attachfile>()
                                                                                          .Where(r => r.Deleted == false)
                                                                                          .Where(r => r.ContractId == file.ContractId)
                                                                                          .OrderByDescending(r => r.CreateDate)
                                                                                          .Select(r => new BM_FileInfo { Id = r.Id, FileName = r.FileName, FilePath = r.FilePath, CreateDate = r.CreateDate.ToString() }).ToList();

                                                          file.ElectronicContractAttachFile = Db.Queryable<Db_crm_contract_electroniccontract_attachfile>()
                                                                                                  .Where(r => r.Deleted == false)
                                                                                                  .Where(r => r.ContractId == file.ContractId)
                                                                                                  .OrderByDescending(r => r.CreateDate)
                                                                                                  .Select(r => new BM_FileInfo { Id = r.Id, FileName = r.FileName, FilePath = r.FilePath, CreateDate = r.CreateDate.ToString() }).ToList();
                                                          file.SignContractAttachFile = file.ElectronicContractAttachFile.Union(file.SealedContractAttachFile).ToList();
                                                      }).ToList();
                }

                item.AddItemContractFileList = Db.Queryable<Db_crm_contract>().Where(c => c.Deleted == false)
                                                     .Where(c => c.ParentContractId == item.Id)
                                                     .Where(c => c.Id != item.Id)
                                                     .Select(c => new ParentContractFile
                                                     {
                                                         ContractId = c.Id,
                                                         ContractType = c.ContractType.Value,

                                                     }).Mapper(file =>
                                                     {
                                                         file.ContractFile = Db.Queryable<Db_crm_contract_attachfile>()
                                                                               .Where(r => r.Deleted == false)
                                                                               .Where(r => r.ContractId == file.ContractId)
                                                                               //.SetContext(scl => scl.ContractId, () => file.ContractId, file)
                                                                               .OrderByDescending(r => r.CreateDate)
                                                                               .Select(r => new BM_FileInfo { Id = r.Id, FileName = r.FileName, FilePath = r.FilePath, CreateDate = r.CreateDate.ToString() }).ToList();

                                                         file.ContractSpecialAttachFile = Db.Queryable<Db_crm_contract_special_attachfile>()
                                                                                             .Where(r => r.Deleted == false)
                                                                                             .Where(r => r.ContractId == file.ContractId)
                                                                                             .OrderByDescending(r => r.CreateDate)
                                                                                             .Select(r => new BM_FileInfo { Id = r.Id, FileName = r.FileName, FilePath = r.FilePath, CreateDate = r.CreateDate.ToString() }).ToList();

                                                         file.DBOriginalContractAttachFile = Db.Queryable<Db_crm_contract_dboriginalcontract_attachfile>()
                                                                                                 .Where(r => r.Deleted == false)
                                                                                                 .Where(r => r.ContractId == file.ContractId)
                                                                                                 .OrderByDescending(r => r.CreateDate)
                                                                                                 .Select(r => new BM_FileInfo { Id = r.Id, FileName = r.FileName, FilePath = r.FilePath, CreateDate = r.CreateDate.ToString() }).ToList();

                                                         file.SealedContractAttachFile = Db.Queryable<Db_crm_contract_sealedcontract_attachfile>()
                                                                                         .Where(r => r.Deleted == false)
                                                                                         .Where(r => r.ContractId == file.ContractId)
                                                                                         .OrderByDescending(r => r.CreateDate)
                                                                                         .Select(r => new BM_FileInfo { Id = r.Id, FileName = r.FileName, FilePath = r.FilePath, CreateDate = r.CreateDate.ToString() }).ToList();

                                                         file.ElectronicContractAttachFile = Db.Queryable<Db_crm_contract_electroniccontract_attachfile>()
                                                                                                 .Where(r => r.Deleted == false)
                                                                                                 .Where(r => r.ContractId == file.ContractId)
                                                                                                 .OrderByDescending(r => r.CreateDate)
                                                                                                 .Select(r => new BM_FileInfo { Id = r.Id, FileName = r.FileName, FilePath = r.FilePath, CreateDate = r.CreateDate.ToString() }).ToList();
                                                         file.SignContractAttachFile = file.ElectronicContractAttachFile.Union(file.SealedContractAttachFile).ToList();
                                                     }).ToList();


            });
            return list.First();
            //return Queryable
            //    .LeftJoin<Db_crm_customer_subcompany>((r, cs) => r.CustomerId == cs.CustomerId && cs.IsMain == 1 && cs.Deleted == 0)
            //    .LeftJoin<Db_crm_customer_subcompany>((r, cs, css) => r.FirstParty == css.Id && css.Deleted == 0)
            //    .Where(r => r.Id == id && r.Deleted == false)
            //    .Select<dynamic>((r, cs, css) => new
            //    {
            //        Id = r.Id.SelectAll(),
            //        CustomerName = cs.CompanyName,
            //        FirstPartyName = css.CompanyName
            //    }).MergeTable().Select<Contract_Out>().First();
            //return Queryable
            //    .LeftJoin<Db_crm_customer_subcompany>((r, cs) => r.CustomerId == cs.CustomerId && cs.IsMain == 1 && cs.Deleted == 0)
            //    .LeftJoin<Db_crm_customer_subcompany>((r, cs, css) => r.FirstParty == css.Id && css.Deleted == 0)
            //    .Where(r => r.Id == id && r.Deleted == false)
            //    .Select<dynamic>((r, cs, css) => new
            //    {
            //        Id = r.Id.SelectAll(),
            //        CustomerName = cs.CompanyName,
            //        FirstPartyName = css.CompanyName
            //    }).MergeTable().Select<Contract_Out>().First();
        }

        public ContractLoadInfo_Out GetContractLoadInfoById(string id, bool dataOwner = false)
        {
            return Queryable
                .LeftJoin<Db_crm_customer_subcompany>((r, cs) => r.CustomerId == cs.CustomerId && cs.IsMain == 1 && cs.Deleted == 0)
                .LeftJoin<Db_crm_customer_subcompany>((r, cs, css) => r.FirstParty == css.Id)
                .LeftJoin<Db_sys_user>((r, cs, css, u) => r.Issuer == u.Id)
                .LeftJoin<Db_crm_collectingcompany>((r, cs, css, u, co) => r.SecondParty == co.Id)
                .LeftJoin<Db_sys_country>((r, cs, css, u, co, cou) => r.Country == cou.Id)
                .LeftJoin<Db_sys_province>((r, cs, css, u, co, cou, pr) => r.Province == pr.Id)
                .LeftJoin<Db_sys_city>((r, cs, css, u, co, cou, pr, ci) => r.City == ci.Id)
                .LeftJoin<Db_crm_contract_audit>((r, cs, css, u, co, cou, pr, ci, ca) => r.Id == ca.ContractId && ca.IsHistory == false && ca.Deleted == false)
                .LeftJoin<Db_v_customer_subcompany_private_user>((r, cs, css, u, co, cou, pr, ci, ca, cspu) => cspu.Id == r.FirstParty)
                .Where((r, cs, css, u, co, cou, pr, ci, ca, cspu) => r.Id == id && r.Deleted == false)
                .WhereIF(dataOwner, DataOwner.BusinessData("r.Issuer", "r.Id", "cspu.CompanyCurrentUser"))
                .Select((r, cs, css, u, co, cou, pr, ci, ca, cspu) => new ContractLoadInfo_Out
                {
                    Id = r.Id.SelectAll(),
                    CustomerName = cs.CompanyName,
                    FirstPartyName = css.CompanyName,
                    IssuerName = u.Name,
                    SecondPartyName = co.CollectingCompanyName,
                    CountryName = cou.Name,
                    ProvinceName = pr.Name,
                    CityName = ci.Name,
                    AuditType = ca.AuditType.Value
                })
                .First();
        }

        public int GetIsReceiptByContractId(string ContractId)
        {
            return Queryable
                .LeftJoin<Db_v_contract_achievementreceiptregisterinfo>((r, crr) => r.Id == crr.ContractId)
                .Where((r, crr) => r.Deleted == false && r.Id == ContractId)
                //.Select(r => r.ContractType).ToInt();
                .Select((r, crr) =>
                     crr.IsReceipt == null ? 3 : crr.IsReceipt.Value
                ).ToInt();
        }

        /// <summary>
        /// 根据查询条件获取销售合同列表。
        /// 合同名称：支持模糊搜索
        /// 签约公司名称：支持模糊搜索
        /// 客户编码：支持精准搜索，只在确认业绩完成后生成
        /// 签约产品：自动获取当前已启用产品列表
        /// 出单人：自动获取合同申请人的姓名
        /// 到账情况：可筛选项全部到账、部分到账、未到账，默认未到账
        /// 支付方式：可选银行、现金、银行+现金
        /// 收款公司：可选项：信息技术、数据技术、科技发展、国际资讯、GLOBALWITS、青岛数据、济南数据、南京数据
        /// 签约类型：可选项：新增合同、合同续约、新增项目
        /// 合同状态：可选项：待审核、审核中、通过、拒绝、待确认、草稿、作废
        /// 币种：可选项：人民币CNY、美元USD
        /// 合同类型：可选项：电子合同、纸质合同
        /// 服务情况：可选项：全部开通、部分开通、待开通、服务变更、申请服务、服务过期
        /// 原件回收：可选项：已回收、未回收
        /// 签约日期：时间段搜索，最小时间单位日
        /// 创建时间：创建时间，时间格式：YYYY-MM-DD
        /// 根据用户数据权限返回相关数据。
        /// 返回结果按创建时间降序排列。
        /// </summary>
        /// <param name="searchContractListIn"></param>
        /// <param name="userID"></param>
        /// <param name="total"></param>
        /// <returns></returns>
        //public List<SearchContractList_Out> SearchContractList(SearchContractList_In searchContractListIn, string userID, ref int total)
        //{
        //    bool searchContractStatus = false;
        //    if (searchContractListIn.ContractStatus != null && searchContractListIn.ContractStatus.Count != 0)
        //    {
        //        searchContractStatus = true;
        //    }
        //    List<int?> ContractStatus = new List<int?>();

        //    bool automaticReviewStatus = false;
        //    bool manualReviewStatus = false;

        //    if (searchContractListIn.ContractStatus != null)
        //    {
        //        if (searchContractListIn.ContractStatus.Contains(3))
        //        {
        //            manualReviewStatus = true;
        //            searchContractListIn.ContractStatus.Remove(3);
        //        }
        //        if (searchContractListIn.ContractStatus.Contains(8))
        //        {
        //            automaticReviewStatus = true;
        //            searchContractListIn.ContractStatus.Remove(8);
        //        }
        //    }
        //    ContractStatus = searchContractListIn.ContractStatus;

        //    int automaticReview = EnumAuditType.AutomaticReview.ToInt();
        //    int manualReview = EnumAuditType.ManualReview.ToInt();
        //    int pass = EnumContractStatus.Pass.ToInt();
        //    int inReview = EnumContractChangeAuditState.InReview.ToInt();
        //    var dt = DateTime.Now;
        //    DateTime startWeek = dt.AddDays(1 - Convert.ToInt32(dt.DayOfWeek.ToString("d")));
        //    DateTime endWeek = startWeek.AddDays(6);
        //    return Queryable
        //        .LeftJoin<Db_crm_customer_subcompany>((r, cs) => r.FirstParty == cs.Id)
        //        .LeftJoin<Db_crm_contract_paymentinfo>((r, cs, p) => r.Id == p.ContractId && p.Deleted == false)
        //        .LeftJoin<Db_sys_user>((r, cs, p, u) => r.Issuer == u.Id)
        //        //.LeftJoin<Db_v_productserviceinfostatus_new>((r, cs, p, u, vp) => r.Id == vp.ContractId)
        //        .LeftJoin<Db_v_productserviceinfo>((r, cs, p, u, vp) => r.Id == vp.ContractId)
        //        .LeftJoin<Db_v_contractisinvoice>((r, cs, p, u, vp, vc) => r.Id == vc.contractId)
        //        .LeftJoin<Db_crm_contract_change_appl>((r, cs, p, u, vp, vc, cca) => r.Id == cca.ContractId && cca.Deleted == false && cca.State == inReview)
        //        .LeftJoin<Db_v_contract_achievementreceiptregisterinfo>((r, cs, p, u, vp, vc, cca, crr) => r.Id == crr.ContractId)
        //        .LeftJoin<Db_crm_contract_audit>((r, cs, p, u, vp, vc, cca, crr, ca) => r.Id == ca.ContractId && ca.IsHistory == false && ca.Deleted == false)
        //        .LeftJoin<Db_crm_collectingcompany>((r, cs, p, u, vp, vc, cca, crr, ca, col) => r.SecondParty == col.Id && col.Deleted == false)
        //        .LeftJoin<Db_crm_contract_stampreview>((r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => r.Id == csv.ContractId && csv.IsHistory == false && csv.Deleted == false)
        //        .Where((r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => r.Deleted == false && r.CreateUser == userID)
        //        .WhereIF(!string.IsNullOrEmpty(searchContractListIn.ContractName), (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => r.ContractName.Contains(searchContractListIn.ContractName))
        //        .WhereIF(!string.IsNullOrEmpty(searchContractListIn.FirstPartyName), (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => cs.CompanyName.Contains(searchContractListIn.FirstPartyName))
        //        .WhereIF(!string.IsNullOrEmpty(searchContractListIn.ContractNum), (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => r.ContractNum == searchContractListIn.ContractNum)
        //        .WhereIF(searchContractListIn.Product != null && searchContractListIn.Product.Count > 0, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => SqlFunc.Subqueryable<Db_crm_contract_productinfo>().Where(s => (searchContractListIn.Product.Contains(s.ProductId) || searchContractListIn.Product.Contains(s.ParentProductId)) && s.ContractId == r.Id && s.Deleted == false).Any())
        //        .WhereIF(searchContractListIn.IssuerName != null && searchContractListIn.IssuerName.Count > 0, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => searchContractListIn.IssuerName.Contains(r.Issuer))
        //        .WhereIF(searchContractListIn.IsReceipt != null && searchContractListIn.IsReceipt != 0, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => SqlFunc.IsNull(crr.IsReceipt, 3) == searchContractListIn.IsReceipt)
        //        .WhereIF(searchContractListIn.PaymentMethod != null && searchContractListIn.PaymentMethod != 0, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => p.PaymentMethod == searchContractListIn.PaymentMethod)
        //        .WhereIF(!string.IsNullOrEmpty(searchContractListIn.CollectingCompany), (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => p.CollectingCompany == searchContractListIn.CollectingCompany)
        //        .WhereIF(searchContractListIn.ContractType != null && searchContractListIn.ContractType != 0, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => r.ContractType == searchContractListIn.ContractType)
        //        //.WhereIF(searchContractListIn.ContractStatus != null && searchContractListIn.ContractStatus != 0 && searchContractListIn.ContractStatus != 8 && searchContractListIn.ContractStatus != 3, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => r.ContractStatus == searchContractListIn.ContractStatus)
        //        //.WhereIF(searchContractListIn.ContractStatus != null && searchContractListIn.ContractStatus != 0 && searchContractListIn.ContractStatus == 8, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => r.ContractStatus == pass && ca.AuditType == automaticReview)
        //        //.WhereIF(searchContractListIn.ContractStatus != null && searchContractListIn.ContractStatus != 0 && searchContractListIn.ContractStatus == 3, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => r.ContractStatus == pass && ca.AuditType == manualReview)

        //        .WhereIF(searchContractStatus, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => searchContractListIn.ContractStatus.Contains(r.ContractStatus) || (automaticReviewStatus && r.ContractStatus == pass && ca.AuditType == automaticReview) || (manualReviewStatus && r.ContractStatus == pass && ca.AuditType == manualReview))

        //        .WhereIF(searchContractListIn.Currency != null && searchContractListIn.Currency != 0, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => r.Currency == searchContractListIn.Currency)
        //        .WhereIF(searchContractListIn.ContractMethod != null && searchContractListIn.ContractMethod != 0, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => r.ContractMethod == searchContractListIn.ContractMethod)
        //        //.WhereIF(searchContractListIn.ProductServiceInfoStatus != null && searchContractListIn.ProductServiceInfoStatus != 0, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => r.ProductServiceInfoStatus == searchContractListIn.ProductServiceInfoStatus)
        //        .WhereIF(searchContractListIn.ProductServiceInfoStatus != null && searchContractListIn.ProductServiceInfoStatus != 0, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => vp.ProductServiceInfoStatus == searchContractListIn.ProductServiceInfoStatus)
        //        .WhereIF(searchContractListIn.RecoveryStatus != null && searchContractListIn.RecoveryStatus != 0, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => r.UsageStatus == searchContractListIn.RecoveryStatus)
        //        .WhereIF(searchContractListIn.SigningDateStart != null && searchContractListIn.SigningDateEnd != null, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => SqlFunc.ToDateShort(r.SigningDate) >= SqlFunc.ToDateShort(searchContractListIn.SigningDateStart) && SqlFunc.ToDateShort(r.SigningDate) <= SqlFunc.ToDateShort(searchContractListIn.SigningDateEnd))
        //        .WhereIF(searchContractListIn.CreateDateStart != null && searchContractListIn.CreateDateEnd != null, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => SqlFunc.ToDateShort(r.CreateDate) >= SqlFunc.ToDateShort(searchContractListIn.CreateDateStart) && SqlFunc.ToDateShort(r.CreateDate) <= SqlFunc.ToDateShort(searchContractListIn.CreateDateEnd))
        //        .WhereIF(searchContractListIn.enumQueryListType == EnumQueryListType.Today, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => SqlFunc.DateIsSame(r.CreateDate, DateTime.Today))
        //        .WhereIF(searchContractListIn.enumQueryListType == EnumQueryListType.Week, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => SqlFunc.ToDateShort(r.CreateDate) >= SqlFunc.ToDateShort(startWeek) && SqlFunc.ToDateShort(r.CreateDate) <= SqlFunc.ToDateShort(endWeek))
        //        .OrderBy((r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => csv.Status == null ? false : (csv.Status == 4 ? true : false), OrderByType.Desc)
        //        .GroupBy((r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => new
        //        {
        //            r.Id,

        //            r.ContractNum,

        //            r.ContractNo,
        //            r.ContractName,
        //            r.FirstParty,
        //            r.ContractType,
        //            r.ContractMethod,
        //            ca.AuditType,
        //            r.ContractStatus,
        //            r.SigningDate,
        //            r.Currency,
        //            r.ContractAmount,
        //            r.FCContractAmount,
        //            r.ContractAmountConvert,
        //            crr.IsReceipt,
        //            vc.IsInvoice,
        //            r.RecoveryStatus,
        //            r.UsageStatus,
        //            r.RecoveryTime,
        //            u.Name,
        //            cs.CompanyName,
        //            r.OrgDivisionName,
        //            r.OrgBrigadeName,
        //            r.OrgRegimentName,
        //            r.CreateDate,
        //            r.IsUrgeRegistration,
        //            //cca.Id,
        //            col.TemplateType,
        //            col.ChineseTemplateCurrency,
        //            col.EnglishTemplateCurrency,
        //            col.IsBothChineseAndEnglish,
        //            r.StampreViewStatus
        //        })
        //        .Select((r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => new SearchContractList_Out
        //        {
        //            Id = r.Id.SelectAll(),
        //            FirstPartyName = cs.CompanyName,
        //            //ProductName
        //            ProductServiceInfoStatus = vp.ProductServiceInfoStatus,
        //            IsReceipt = crr.IsReceipt == null ? 3 : crr.IsReceipt.Value,
        //            IsInvoice = vc.IsInvoice,
        //            //IsInvoiceName
        //            IssuerName = u.Name,
        //            CreateDate = r.CreateDate.Value.ToString("yyyy-MM-dd HH:mm"),
        //            IsContractChangeInReview = !SqlFunc.IsNullOrEmpty(cca.Id),
        //            AuditType = ca.AuditType.Value,
        //            TemplateType = col.TemplateType,
        //            ChineseTemplateCurrency = col.ChineseTemplateCurrency,
        //            EnglishTemplateCurrency = col.EnglishTemplateCurrency,
        //            IsBothChineseAndEnglish = col.IsBothChineseAndEnglish,
        //            StampreViewAuditId = csv.Id,
        //            StampreViewStatusRefuse = csv.Status == null ? false : (csv.Status == 4 ? true : false)
        //        })
        //        .OrderByDescending(r => r.CreateDate)
        //        .ToPageList(searchContractListIn.PageNumber, searchContractListIn.PageSize, ref total);
        //}

        public List<SearchContractList_Out> SearchContractList(SearchContractList_In searchContractListIn, string userID, ref int total)
        {
            bool searchContractStatus = false;
            if (searchContractListIn.ContractStatus != null && searchContractListIn.ContractStatus.Count != 0)
            {
                searchContractStatus = true;
            }
            List<int?> ContractStatus = new List<int?>();

            bool automaticReviewStatus = false;
            bool manualReviewStatus = false;

            if (searchContractListIn.ContractStatus != null)
            {
                if (searchContractListIn.ContractStatus.Contains(200))
                {
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.InitialAuditPassSubmitOrgRegimentAudit.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.InitialAuditPassSubmitOrgBrigadeAudit.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.InitialAuditPassSubmitOrgDivisionAudit.ToInt());
                    //searchContractListIn.ContractStatus.Add(EnumContractStatus.OrgRegimentAuditPass.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.OrgRegimentAuditSubmitOrgBrigadeAudit.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.OrgRegimentAuditSubmitOrgDivisionAudit.ToInt());
                    //searchContractListIn.ContractStatus.Add(EnumContractStatus.OrgBrigadeAuditPass.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.OrgBrigadeAuditSubmitOrgDivisionAudit.ToInt());
                    //searchContractListIn.ContractStatus.Add(EnumContractStatus.OrgDivisionAuditPass.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.SubmitOrgRegimentAudit.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.SubmitOrgBrigadeAudit.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.SubmitOrgDivisionAudit.ToInt());
                    //searchContractListIn.ContractStatus.Add(EnumContractStatus.SubmitOrgRegimentAuditPass.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.SubmitOrgRegimentAuditSubmitOrgBrigadeAudit.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.SubmitOrgRegimentAuditSubmitOrgDivisionAudit.ToInt());
                    //searchContractListIn.ContractStatus.Add(EnumContractStatus.SubmitOrgBrigadeAuditPass.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.SubmitOrgBrigadeAuditSubmitOrgDivisionAudit.ToInt());
                    //searchContractListIn.ContractStatus.Add(EnumContractStatus.SubmitOrgDivisionAuditPass.ToInt());
                }
                if (searchContractListIn.ContractStatus.Contains(13))
                {
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.OrgRegimentAuditRefuse.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.OrgBrigadeAuditRefuse.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.OrgDivisionAuditRefuse.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.SubmitOrgRegimentAuditRefuse.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.SubmitOrgBrigadeAuditRefuse.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.SubmitOrgDivisionAuditRefuse.ToInt());
                }
                if (searchContractListIn.ContractStatus.Contains(12))
                {
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.OrgRegimentAuditPass.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.OrgBrigadeAuditPass.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.OrgDivisionAuditPass.ToInt());
                }
                if (searchContractListIn.ContractStatus.Contains(1))
                {
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.SubmitOrgRegimentAuditPass.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.SubmitOrgBrigadeAuditPass.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.SubmitOrgDivisionAuditPass.ToInt());
                }


                if (searchContractListIn.ContractStatus.Contains(3))
                {
                    manualReviewStatus = true;
                    searchContractListIn.ContractStatus.Remove(3);
                }
                if (searchContractListIn.ContractStatus.Contains(8))
                {
                    automaticReviewStatus = true;
                    searchContractListIn.ContractStatus.Remove(8);
                }
            }
            ContractStatus = searchContractListIn.ContractStatus;

            bool IsSearchCancl = true;
            if (!searchContractStatus)
            {
                if (
                (!string.IsNullOrEmpty(searchContractListIn.CustomerId)
                || !string.IsNullOrEmpty(searchContractListIn.ContractName)
                || !string.IsNullOrEmpty(searchContractListIn.FirstPartyName)
                || !string.IsNullOrEmpty(searchContractListIn.ContractNum)
                ))
                {
                    IsSearchCancl = false;
                }
            }
            else
            {
                IsSearchCancl = false;
            }

            #region 处理首页跳转过来的时间条件
            DateSpanParams indexP = null;
            if (searchContractListIn.EnumIndexRedirectType == EnumIndexRedirectType.Span_SignContract)
            {
                indexP = BLL_Statistics.Instance.TransUserIndexStatisticsDate(new UserIndexStatisticsDate()
                {
                    EnumStatisticsDateSpanSelect = searchContractListIn.EnumStatisticsDateSpanSelect,
                    SelectYear = searchContractListIn.SelectYear
                });
            }
            #endregion


            int automaticReview = EnumAuditType.AutomaticReview.ToInt();
            int manualReview = EnumAuditType.ManualReview.ToInt();
            int pass = EnumContractStatus.Pass.ToInt();
            int inReview = EnumContractChangeAuditState.InReview.ToInt();
            var dt = DateTime.Now;
            DateTime startWeek = dt.AddDays(1 - Convert.ToInt32(dt.DayOfWeek.ToString("d")));
            DateTime endWeek = startWeek.AddDays(6);
            var list = Queryable
                .LeftJoin<Db_crm_customer_subcompany>((r, cs) => r.FirstParty == cs.Id)
                .LeftJoin<Db_crm_contract_paymentinfo>((r, cs, p) => r.Id == p.ContractId && p.Deleted == false)
                .LeftJoin<Db_v_userwithorg>((r, cs, p, u) => r.Issuer == u.Id)
                .LeftJoin<Db_v_productserviceinfostatus_new>((r, cs, p, u, vp) => r.Id == vp.ContractId)
                //.LeftJoin<Db_v_contractisinvoice>((r, cs, p, u, vp, vc) => r.Id == vc.contractId)
                .LeftJoin<Db_v_contract_invoice_state>((r, cs, p, u, vp, vc) => r.Id == vc.ContractId)
                .LeftJoin<Db_crm_contract_change_appl>((r, cs, p, u, vp, vc, cca) => r.Id == cca.ContractId && cca.Deleted == false && cca.State == inReview)
                .LeftJoin<Db_v_contract_achievementreceiptregisterinfo>((r, cs, p, u, vp, vc, cca, crr) => r.Id == crr.ContractId)
                .LeftJoin<Db_crm_contract_audit>((r, cs, p, u, vp, vc, cca, crr, ca) => r.Id == ca.ContractId && ca.IsHistory == false && ca.Deleted == false)
                .LeftJoin<Db_crm_collectingcompany>((r, cs, p, u, vp, vc, cca, crr, ca, col) => r.SecondParty == col.Id && col.Deleted == false)
                .LeftJoin<Db_crm_contract_stampreview>((r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => r.Id == csv.ContractId && csv.IsHistory == false && csv.Deleted == false)
                //20240911 更改权限判断，拥有客户权限的，同时拥有合同/发票/到账权限
                .LeftJoin<Db_v_customer_subcompany_private_user>(((r, cs, p, u, vp, vc, cca, crr, ca, col, csv, cspu) => r.FirstParty == cspu.Id))
                .Where((r, cs, p, u, vp, vc, cca, crr, ca, col, csv, cspu) => r.Deleted == false)
                .Where(DataOwner.BusinessData("r.Issuer", "", "cspu.CompanyCurrentUser", true))
                .WhereIF(!string.IsNullOrEmpty(searchContractListIn.CustomerId), (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => cs.CustomerId == searchContractListIn.CustomerId)
                .WhereIF(!string.IsNullOrEmpty(searchContractListIn.ContractName), (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => r.ContractName.Contains(searchContractListIn.ContractName))
                .WhereIF(!string.IsNullOrEmpty(searchContractListIn.FirstPartyName), (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => cs.CompanyName.Contains(searchContractListIn.FirstPartyName))
                .WhereIF(!string.IsNullOrEmpty(searchContractListIn.ContractNum), (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => r.ContractNum == searchContractListIn.ContractNum)
                .WhereIF(searchContractListIn.Product != null && searchContractListIn.Product.Count > 0, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => SqlFunc.Subqueryable<Db_crm_contract_productinfo>().Where(s => (searchContractListIn.Product.Contains(s.ProductId) || searchContractListIn.Product.Contains(s.ParentProductId)) && s.ContractId == r.Id && s.Deleted == false).Any())
                .WhereIF(searchContractListIn.IssuerName != null && searchContractListIn.IssuerName.Count > 0, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => searchContractListIn.IssuerName.Contains(r.Issuer))
                .WhereIF(searchContractListIn.IsReceipt != null && searchContractListIn.IsReceipt != 0, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => SqlFunc.IsNull(crr.IsReceipt, 3) == searchContractListIn.IsReceipt)
                .WhereIF(searchContractListIn.PaymentMethod != null && searchContractListIn.PaymentMethod != 0, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => p.PaymentMethod == searchContractListIn.PaymentMethod)
                .WhereIF(!string.IsNullOrEmpty(searchContractListIn.CollectingCompany), (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => p.CollectingCompany == searchContractListIn.CollectingCompany)
                .WhereIF(searchContractListIn.ContractType != null && searchContractListIn.ContractType != 0, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => r.ContractType == searchContractListIn.ContractType)
                //.WhereIF(searchContractListIn.ContractStatus != null && searchContractListIn.ContractStatus != 0 && searchContractListIn.ContractStatus != 8 && searchContractListIn.ContractStatus != 3, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => r.ContractStatus == searchContractListIn.ContractStatus)
                //.WhereIF(searchContractListIn.ContractStatus != null && searchContractListIn.ContractStatus != 0 && searchContractListIn.ContractStatus == 8, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => r.ContractStatus == pass && ca.AuditType == automaticReview)
                //.WhereIF(searchContractListIn.ContractStatus != null && searchContractListIn.ContractStatus != 0 && searchContractListIn.ContractStatus == 3, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => r.ContractStatus == pass && ca.AuditType == manualReview)

                .WhereIF(searchContractStatus, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => searchContractListIn.ContractStatus.Contains(r.ContractStatus) || (automaticReviewStatus && r.ContractStatus == pass && ca.AuditType == automaticReview) || (manualReviewStatus && r.ContractStatus == pass && ca.AuditType == manualReview))
                .WhereIF(!string.IsNullOrEmpty(searchContractListIn.PaymentCompanyName), (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => SqlFunc.Subqueryable<Db_crm_contract_paymentinfo>().LeftJoin<Db_crm_customer_subcompany>((cp, cs) => cp.PaymentCompany == cs.Id).Where((cp, cs) => cs.CompanyName.Contains(searchContractListIn.PaymentCompanyName) && cp.ContractId == r.Id && cp.Deleted == false).Any())
                .WhereIF(!string.IsNullOrEmpty(searchContractListIn.IsBehalfPaymentCompanyName), (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => SqlFunc.Subqueryable<Db_crm_contract_paymentinfo>().LeftJoin<Db_crm_customer_subcompany>((cp, cs) => cp.PaymentCompany == cs.Id).Where((cp, cs) => cs.CompanyName.Contains(searchContractListIn.IsBehalfPaymentCompanyName) && cp.ContractId == r.Id && cp.IsBehalfPayment == true && cp.Deleted == false).Any())

                .WhereIF(searchContractListIn.Currency != null && searchContractListIn.Currency != 0, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => r.Currency == searchContractListIn.Currency)
                .WhereIF(searchContractListIn.ContractMethod != null && searchContractListIn.ContractMethod != 0, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => r.ContractMethod == searchContractListIn.ContractMethod)
                //.WhereIF(searchContractListIn.ProductServiceInfoStatus != null && searchContractListIn.ProductServiceInfoStatus != 0, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => r.ProductServiceInfoStatus == searchContractListIn.ProductServiceInfoStatus)
                .WhereIF(searchContractListIn.ProductServiceInfoStatus != null && searchContractListIn.ProductServiceInfoStatus != 0, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => vp.ProductServiceInfoStatus == searchContractListIn.ProductServiceInfoStatus)
                .WhereIF(searchContractListIn.RecoveryStatus != null && searchContractListIn.RecoveryStatus != 0, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => r.UsageStatus == searchContractListIn.RecoveryStatus)
                .WhereIF(searchContractListIn.SigningDateStart != null && searchContractListIn.SigningDateEnd != null, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => SqlFunc.ToDateShort(r.SigningDate) >= SqlFunc.ToDateShort(searchContractListIn.SigningDateStart) && SqlFunc.ToDateShort(r.SigningDate) <= SqlFunc.ToDateShort(searchContractListIn.SigningDateEnd))
                .WhereIF(searchContractListIn.CreateDateStart != null && searchContractListIn.CreateDateEnd != null, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => SqlFunc.ToDateShort(r.CreateDate) >= SqlFunc.ToDateShort(searchContractListIn.CreateDateStart) && SqlFunc.ToDateShort(r.CreateDate) <= SqlFunc.ToDateShort(searchContractListIn.CreateDateEnd))
                .WhereIF(searchContractListIn.enumQueryListType == EnumQueryListType.Today, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => SqlFunc.DateIsSame(r.CreateDate, DateTime.Today))
                .WhereIF(searchContractListIn.enumQueryListType == EnumQueryListType.Week, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => SqlFunc.ToDateShort(r.CreateDate) >= SqlFunc.ToDateShort(startWeek) && SqlFunc.ToDateShort(r.CreateDate) <= SqlFunc.ToDateShort(endWeek))
                .WhereIF(searchContractListIn.IsBehalfPayment != null, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => p.IsBehalfPayment == searchContractListIn.IsBehalfPayment)
                //首页跳转
                .WhereIF(searchContractListIn.EnumIndexRedirectType == EnumIndexRedirectType.Span_SignContract, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) =>
                    r.SigningDate != null
                    && indexP.DateStart != null && SqlFunc.ToDateShort(indexP.DateStart.Value) <= SqlFunc.ToDateShort(r.SigningDate)
                    && indexP.DateEnd != null && SqlFunc.ToDateShort(indexP.DateEnd.Value) >= SqlFunc.ToDateShort(r.SigningDate)
                    && r.ContractStatus == (int)EnumContractStatus.Pass
                )
                //首页跳转
                .WhereIF(searchContractListIn.EnumIndexRedirectType == EnumIndexRedirectType.Total_SignContract, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) =>
                    r.ContractStatus == (int)EnumContractStatus.Pass
                )
                .WhereIF(searchContractListIn.Id.IsNotNullOrEmpty(), (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => r.Id == searchContractListIn.Id)

                .WhereIF(IsSearchCancl, (r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => r.ContractStatus != (int)EnumContractStatus.Cancel)
                .OrderBy((r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => csv.Status == null ? false : (csv.Status == 4 ? true : false), OrderByType.Desc)
                .Select((r, cs, p, u, vp, vc, cca, crr, ca, col, csv) => new SearchContractList_Out
                {
                    Id = r.Id.SelectAll(),
                    FirstPartyName = cs.CompanyName,
                    //ProductName
                    ProductServiceInfoStatus = vp.ProductServiceInfoStatus,
                    IsReceipt = crr.IsReceipt == null ? 3 : crr.IsReceipt.Value,
                    IsInvoice = (int)vc.DisplayStatus,
                    //IsInvoiceName
                    IssuerName = u.Name,
                    CreateDate = r.CreateDate.Value.ToString("yyyy-MM-dd HH:mm"),
                    IsContractChangeInReview = !SqlFunc.IsNullOrEmpty(cca.Id),
                    AuditType = ca.AuditType.Value,
                    TemplateType = col.TemplateType,
                    ChineseTemplateCurrency = col.ChineseTemplateCurrency,
                    EnglishTemplateCurrency = col.EnglishTemplateCurrency,
                    IsBothChineseAndEnglish = col.IsBothChineseAndEnglish,
                    StampreViewAuditId = csv.Id,
                    StampreViewStatusRefuse = csv.Status == null ? false : (csv.Status == 4 ? true : false),
                    IsBehalfPayment = p.IsBehalfPayment,
                    CollectingCompany = p.CollectingCompany,
                    CollectingCompanyName = col.CollectingCompanyName
                })
                .OrderByDescending(r => r.CreateDate)
                .ToPageList(searchContractListIn.PageNumber, searchContractListIn.PageSize, ref total);

            Db.ThenMapper(list, item =>
            {
                var CompanyName = Db.Queryable<Db_crm_contract_paymentinfo>()
                .LeftJoin<Db_crm_customer_subcompany>((r, c) => r.PaymentCompany == c.Id && c.Deleted == 0)
                .Where((r, c) => r.IsBehalfPayment == true && r.Deleted == false)
                .Select((r, c) => new { ContractId = r.ContractId, IsBehalfPaymentCompanyName = c.CompanyName })
                .SetContext(r => r.ContractId, () => item.Id, item);

                item.IsBehalfPaymentCompanyName = CompanyName.Count == 0 ? "" : CompanyName.First().IsBehalfPaymentCompanyName;

                var PaymentCompanyNameList = Db.Queryable<Db_crm_contract_paymentinfo>()
                .LeftJoin<Db_crm_customer_subcompany>((r, c) => r.PaymentCompany == c.Id && c.Deleted == 0)
                .Where((r, c) => r.Deleted == false)
                .Select((r, c) => new { ContractId = r.ContractId, PaymentCompanyName = c.CompanyName })
                .SetContext(r => r.ContractId, () => item.Id, item);

                item.PaymentCompanyName = PaymentCompanyNameList.Count == 0 ? "" : PaymentCompanyNameList.First().PaymentCompanyName;

                var GrantCouponNum = Db.Queryable<Db_crm_customer_coupon>()
                .LeftJoin<Db_crm_customer_coupon_detail>((r, c) => r.Id == c.CouponId)
                .Where((r, c) => r.Deleted == false && c.Deleted == false && c.CouponType == 0)
                .Select((r, c) => new { CompanyId = r.CompanyId, CouponType = c.CouponType })
                .SetContext(r => r.CompanyId, () => item.FirstParty, item);

                item.GrantCouponNum = GrantCouponNum.Count;

            });
            return list;
        }


        public List<CustomerContractList_Out> SearchCustomerContractList(string customerId, string userID)
        {
            //验证超级权限
            bool superRole = BLL_Role.Instance.CheckSuperUser();
            var dt = DateTime.Now;
            DateTime startWeek = dt.AddDays(1 - Convert.ToInt32(dt.DayOfWeek.ToString("d")));
            DateTime endWeek = startWeek.AddDays(6);
            var dtDay = DateTime.Parse(dt.ToString("yyyy-MM-dd"));
            var list = Queryable
                .LeftJoin<Db_crm_customer_subcompany>((r, cs) => r.FirstParty == cs.Id)
                .LeftJoin<Db_crm_contract_paymentinfo>((r, cs, p) => r.Id == p.ContractId && p.Deleted == false)
                .LeftJoin<Db_v_userwithorg>((r, cs, p, u) => r.Issuer == u.Id)
                .LeftJoin<Db_v_productserviceinfostatus>((r, cs, p, u, vp) => r.Id == vp.ContractId)
                .LeftJoin<Db_v_contract_invoice_state>((r, cs, p, u, vp, vc) => r.Id == vc.ContractId)
                //20240911 更改权限判断，拥有客户权限的，同时拥有合同/发票/到账权限
                .LeftJoinIF<Db_v_customer_subcompany_private_user>(!superRole, ((r, cs, p, u, vp, vc, cspu) => r.FirstParty == cspu.Id))
                .WhereIF(!superRole, DataOwner.BusinessData("r.Issuer", "r.Id", "cspu.CompanyCurrentUser", true))
                .Where((r, cs, p, u, vp, vc, cspu) => r.Deleted == false)
                //.WhereIF(!superRole, (r, cs, p, u, vp, vc) => r.CreateUser == userID)
                .Where((r, cs, p, u, vp, vc, cspu) => cs.CustomerId == customerId)
                .OrderByDescending((r, cs, p, u, vp, vc, cspu) => r.CreateDate)
                .Select((r, cs, p, u, vp, vc, cspu) => new CustomerContractList_Out
                {
                    Id = r.Id,
                    FirstPartyName = cs.CompanyName,
                    //ProductName
                    ProductServiceInfoStatus = vp.ProductServiceInfoStatus,
                    //IsReceipt = p.IsReceipt.Value,
                    //IsInvoice = vc.IsInvoice,
                    //IsInvoiceName
                    IssuerName = u.UserWithOrgFullName,
                    SigningDate = r.SigningDate.Value.ToString("yyyy-MM-dd")
                }, true)
                .ToList();
            Db.ThenMapper(list, item =>
            {
                item.CustomerProductInfos = Db.Queryable<Db_crm_contract_productinfo>()
                    .LeftJoin<Db_crm_product>((r, p) => r.ProductId == p.Id)// && p.Deleted == false)
                                                                            //.LeftJoin<Db_sys_product_month_year>((r, p, pmy) => r.OpeningMonths == pmy.Month && pmy.Deleted == false)
                    .LeftJoin<Db_v_product_contract_service_info>((r, p, vp) => r.Id == vp.Id)
                    .Where((r, p, vp) => r.Deleted == false)
                    .Select((r, p, vp) => new CustomerProductInfo
                    {
                        Id = r.Id.SelectAll(),
                        ContractId = r.ContractId,
                        ApplState = vp.ApplState,
                        ProductName = r.ProductName,
                        ProductType = (int)p.ProductType,
                        ProductServiceInfoApplState = vp.ApplState,
                        ServiceCycle = r.OpeningYears,//pmy.Year,//p.ServiceCycle,
                        ServiceCycleStart = p.ServiceCycleStart,
                        ServiceCycleEnd = p.ServiceCycleEnd,
                        ServiceCycleEndDt = vp.ServiceCycleEnd,
                        OpenServiceCycle = (vp.ServiceCycleStart == null || vp.ServiceCycleEnd == null) ? "--" : vp.ServiceCycleStart.Value.ToString("yyyy-MM-dd") + "至" + vp.ServiceCycleEnd.Value.ToString("yyyy-MM-dd"),
                    })
                    .Mapper(r =>
                    {
                        r.NearlyEnd = (r.ServiceCycleEndDt == null || r.ApplState != (int)EnumProcessStatus.Pass) ? false : ((r.ServiceCycleEndDt.Value - dtDay).TotalDays <= 15 && (r.ServiceCycleEndDt.Value - dtDay).TotalDays > 0);
                    })
                .SetContext(r => r.ContractId, () => item.Id, item)
                .ToList();
            });
            return list;
        }

        public bool exceptWords(string keyWord)
        {
            bool result = true;
            if (string.IsNullOrEmpty(keyWord) || keyWord.Trim().Length <= 1)
            {
                throw new ApiException("关键词太短,请重新输入");
            }
            ChineseAnalyzerCls chineseAnalyzerCls = new ChineseAnalyzerCls();
            var inputKeyWord = keyWord.Trim().Replace("'", "");
            var inputKeyWordDM = chineseAnalyzerCls.ReplaceKeyword(inputKeyWord);
            if (inputKeyWord.Length < 1)
            {
                throw new ApiException("关键词过短,请再详细描述！");
            }
            var exceptWords = DbOpe_crm_tianyancha_othernames.Instance.Query_crm_tianyancha_othernames().Select(o => o.Names).ToList();
            exceptWords = exceptWords.Concat(LocalCache.LC_Address.CountryAndAreaCache.Select(c => c.Name)).ToList();
            exceptWords = exceptWords.Concat(LocalCache.LC_Address.CountryAndAreaCache.Select(c => c.NameEN)).ToList();
            exceptWords = exceptWords.Concat(LocalCache.LC_Address.ProvinceCache.Select(c => c.Name)).ToList();
            exceptWords = exceptWords.Concat(LocalCache.LC_Address.CityCache.Select(c => c.Name)).ToList();
            //if (exceptWords.Where((t) => t.Contains(inputKeyWord)).Count() > 0)
            if (exceptWords.Contains(inputKeyWord))
            {
                throw new ApiException("非关键词,请再详细描述！");
            }
            return result;
        }

        /// <summary>
        /// 根据查询条件获取销售合同列表。
        /// 合同名称：支持模糊搜索
        /// 签约公司名称：支持模糊搜索
        /// 客户编码：支持精准搜索，只在确认业绩完成后生成
        /// 签约产品：自动获取当前已启用产品列表
        /// 出单人：自动获取合同申请人的姓名
        /// 到账情况：可筛选项全部到账、部分到账、未到账，默认未到账
        /// 支付方式：可选银行、现金、银行+现金
        /// 收款公司：可选项：信息技术、数据技术、科技发展、国际资讯、GLOBALWITS、青岛数据、济南数据、南京数据
        /// 签约类型：可选项：新增合同、合同续约、新增项目
        /// 合同状态：可选项：待审核、审核中、通过、拒绝、待确认、草稿、作废
        /// 币种：可选项：人民币CNY、美元USD
        /// 合同类型：可选项：电子合同、纸质合同
        /// 服务情况：可选项：全部开通、部分开通、待开通、服务变更、申请服务、服务过期
        /// 原件回收：可选项：已回收、未回收
        /// 签约日期：时间段搜索，最小时间单位日
        /// 创建时间：创建时间，时间格式：YYYY-MM-DD
        /// 根据用户数据权限返回相关数据。
        /// 返回结果按创建时间降序排列。
        /// </summary>
        /// <param name="searchContractListIn"></param>
        /// <param name="total"></param>
        /// <param name="dataOwner"></param>
        /// <param name="dataPermissionOwner"></param>
        /// <returns></returns>
        public List<SearchContractList_Out> SearchContractAuditList(SearchContractList_In searchContractListIn, ref int total, bool dataOwner = false, bool dataPermissionOwner = false)
        {
            //临时
            bool ContractNameOrFirstPartyName = false;
            List<FormInterfaceName> list = DbOpe_sys_form.Instance.GetFormIdsByUserId(UserId);
            List<int?> state = new List<int?>();
            List<FormInterfaceName> ContractNameOrFirstPartyNameList = list.Where(r => r.ControllerName == "Contract" && (r.MethodName == "InitialAuditContract" || r.MethodName == "ReviewAuditContract")).ToList();
            if (ContractNameOrFirstPartyNameList.Count() > 0)
            {
                if (!string.IsNullOrEmpty(searchContractListIn.ContractName))
                {
                    ContractNameOrFirstPartyName = exceptWords(searchContractListIn.ContractName);
                }
                if (!string.IsNullOrEmpty(searchContractListIn.FirstPartyName))
                {
                    ContractNameOrFirstPartyName = exceptWords(searchContractListIn.FirstPartyName);
                }
            }

            List<FormInterfaceName> InitialAuditPermissionList = list.Where(r => r.ControllerName == "Contract" && r.MethodName == "InitialAuditContract").ToList();
            if (InitialAuditPermissionList.Count() > 0)
            {
                //state.Add(EnumContractStatus.Pass.ToInt());
                //state.Add(EnumContractStatus.ToBeConfirmed.ToInt());
                //state.Add(EnumContractStatus.Draft.ToInt());
                state.Add(EnumContractStatus.Submit.ToInt());
                //state.Add(EnumContractStatus.Pass.ToInt());
                state.Add(EnumContractStatus.Refuse.ToInt());
                //state.Add(EnumContractStatus.ToBeConfirmed.ToInt());
                //state.Add(EnumContractStatus.Draft.ToInt());
                state.Add(EnumContractStatus.Cancel.ToInt());
                //state.Add(EnumContractStatus.AutoPass.ToInt());
                state.Add(EnumContractStatus.SuperSubAccountSubmit.ToInt());
                state.Add(EnumContractStatus.SuperSubAccountPass.ToInt());
                state.Add(EnumContractStatus.SuperSubAccountRefuse.ToInt());
                state.Add(EnumContractStatus.InitialAuditPass.ToInt());
                state.Add(EnumContractStatus.InitialAuditRefuse.ToInt());

                state.Add(EnumContractStatus.SubmitOrgRegimentAuditPass.ToInt());
                state.Add(EnumContractStatus.SubmitOrgBrigadeAuditPass.ToInt());
                state.Add(EnumContractStatus.SubmitOrgDivisionAuditPass.ToInt());

                state.Add(EnumContractStatus.InitialAuditPassSubmitOrgRegimentAudit.ToInt());
                state.Add(EnumContractStatus.InitialAuditPassSubmitOrgBrigadeAudit.ToInt());
                state.Add(EnumContractStatus.InitialAuditPassSubmitOrgDivisionAudit.ToInt());
            }
            List<FormInterfaceName> ReviewAuditPermissionList = list.Where(r => r.ControllerName == "Contract" && r.MethodName == "ReviewAuditContract").ToList();
            if (ReviewAuditPermissionList.Count() > 0)
            {
                //state.Add(EnumContractStatus.Submit.ToInt());
                //state.Add(EnumContractStatus.Refuse.ToInt());
                //state.Add(EnumContractStatus.SuperSubAccountSubmit.ToInt());
                //state.Add(EnumContractStatus.SuperSubAccountPass.ToInt());
                //state.Add(EnumContractStatus.SuperSubAccountRefuse.ToInt());
                //state.Add(EnumContractStatus.Submit.ToInt());
                state.Add(EnumContractStatus.Pass.ToInt());
                state.Add(EnumContractStatus.Refuse.ToInt());
                state.Add(EnumContractStatus.ToBeConfirmed.ToInt());
                //state.Add(EnumContractStatus.Draft.ToInt());
                state.Add(EnumContractStatus.Cancel.ToInt());
                state.Add(EnumContractStatus.AutoPass.ToInt());
                //state.Add(EnumContractStatus.SuperSubAccountSubmit.ToInt());
                //state.Add(EnumContractStatus.SuperSubAccountPass.ToInt());
                //state.Add(EnumContractStatus.SuperSubAccountRefuse.ToInt());
                state.Add(EnumContractStatus.InitialAuditPass.ToInt());
                //state.Add(EnumContractStatus.InitialAuditRefuse.ToInt());

                state.Add(EnumContractStatus.OrgRegimentAuditPass.ToInt());
                state.Add(EnumContractStatus.OrgBrigadeAuditPass.ToInt());
                state.Add(EnumContractStatus.OrgDivisionAuditPass.ToInt());

                //state.Add(EnumContractStatus.SubmitOrgRegimentAuditPass.ToInt());
                //state.Add(EnumContractStatus.SubmitOrgBrigadeAuditPass.ToInt());
                //state.Add(EnumContractStatus.SubmitOrgDivisionAuditPass.ToInt());
            }

            List<FormInterfaceName> TeamAuditAuditPermissionList = list.Where(r => r.ControllerName == "Contract" && r.MethodName == "TeamAuditContract").ToList();
            if (TeamAuditAuditPermissionList.Count() > 0)
            {
                ////state.Add(EnumContractStatus.Submit.ToInt());
                ////state.Add(EnumContractStatus.Refuse.ToInt());
                ////state.Add(EnumContractStatus.SuperSubAccountSubmit.ToInt());
                ////state.Add(EnumContractStatus.SuperSubAccountPass.ToInt());
                ////state.Add(EnumContractStatus.SuperSubAccountRefuse.ToInt());
                ////state.Add(EnumContractStatus.Submit.ToInt());
                //state.Add(EnumContractStatus.Pass.ToInt());
                //state.Add(EnumContractStatus.Refuse.ToInt());
                //state.Add(EnumContractStatus.ToBeConfirmed.ToInt());
                ////state.Add(EnumContractStatus.Draft.ToInt());
                //state.Add(EnumContractStatus.Cancel.ToInt());
                //state.Add(EnumContractStatus.AutoPass.ToInt());
                ////state.Add(EnumContractStatus.SuperSubAccountSubmit.ToInt());
                ////state.Add(EnumContractStatus.SuperSubAccountPass.ToInt());
                ////state.Add(EnumContractStatus.SuperSubAccountRefuse.ToInt());
                //state.Add(EnumContractStatus.InitialAuditPass.ToInt());
                ////state.Add(EnumContractStatus.InitialAuditRefuse.ToInt());
                ///

                //state.Add(EnumContractStatus.InitialAuditPassSubmitOrgRegimentAudit.ToInt());
                //state.Add(EnumContractStatus.InitialAuditPassSubmitOrgBrigadeAudit.ToInt());
                //state.Add(EnumContractStatus.InitialAuditPassSubmitOrgDivisionAudit.ToInt());
                //state.Add(EnumContractStatus.OrgRegimentAuditPass.ToInt());
                //state.Add(EnumContractStatus.OrgRegimentAuditRefuse.ToInt());
                //state.Add(EnumContractStatus.OrgRegimentAuditSubmitOrgBrigadeAudit.ToInt());
                //state.Add(EnumContractStatus.OrgRegimentAuditSubmitOrgDivisionAudit.ToInt());
                //state.Add(EnumContractStatus.OrgBrigadeAuditPass.ToInt());
                //state.Add(EnumContractStatus.OrgBrigadeAuditRefuse.ToInt());
                //state.Add(EnumContractStatus.OrgBrigadeAuditSubmitOrgDivisionAudit.ToInt());
                //state.Add(EnumContractStatus.OrgDivisionAuditPass.ToInt());
                //state.Add(EnumContractStatus.OrgDivisionAuditRefuse.ToInt());

                //state.Add(EnumContractStatus.SubmitOrgRegimentAudit.ToInt());
                //state.Add(EnumContractStatus.SubmitOrgBrigadeAudit.ToInt());
                //state.Add(EnumContractStatus.SubmitOrgDivisionAudit.ToInt());
                //state.Add(EnumContractStatus.SubmitOrgRegimentAuditPass.ToInt());
                //state.Add(EnumContractStatus.SubmitOrgRegimentAuditRefuse.ToInt());
                //state.Add(EnumContractStatus.SubmitOrgRegimentAuditSubmitOrgBrigadeAudit.ToInt());
                //state.Add(EnumContractStatus.SubmitOrgRegimentAuditSubmitOrgDivisionAudit.ToInt());
                //state.Add(EnumContractStatus.SubmitOrgBrigadeAuditPass.ToInt());
                //state.Add(EnumContractStatus.SubmitOrgBrigadeAuditRefuse.ToInt());
                //state.Add(EnumContractStatus.SubmitOrgBrigadeAuditSubmitOrgDivisionAudit.ToInt());
                //state.Add(EnumContractStatus.SubmitOrgDivisionAuditPass.ToInt());
                //state.Add(EnumContractStatus.SubmitOrgDivisionAuditRefuse.ToInt());




                //获取当前用户的管理层级
                var user = DbOpe_sys_user.Instance.GetUserById(UserId);
                if (user == null)
                {
                    throw new ApiException("未获取到用户信息");
                }
                if (user.UserType != EnumUserType.Manager)
                {
                    throw new ApiException("当前用户并非管理者，没有审核权限");
                }
                var org = DbOpe_sys_organization.Instance.GetDataById(user.OrganizationId);
                if (org == null)
                {
                    throw new ApiException("未获取到用户所属组织信息");
                }

                if (org.OrgType == EnumOrgType.Squadron)
                {
                    state.Add(EnumContractStatus.InitialAuditPassSubmitOrgRegimentAudit.ToInt());
                    //state.Add(EnumContractStatus.OrgRegimentAuditPass.ToInt());
                    //state.Add(EnumContractStatus.OrgRegimentAuditRefuse.ToInt());
                    //state.Add(EnumContractStatus.OrgRegimentAuditSubmitOrgBrigadeAudit.ToInt());
                    //state.Add(EnumContractStatus.OrgRegimentAuditSubmitOrgDivisionAudit.ToInt());

                    state.Add(EnumContractStatus.SubmitOrgRegimentAudit.ToInt());
                    //state.Add(EnumContractStatus.SubmitOrgRegimentAuditPass.ToInt());
                    //state.Add(EnumContractStatus.SubmitOrgRegimentAuditRefuse.ToInt());
                    //state.Add(EnumContractStatus.SubmitOrgRegimentAuditSubmitOrgBrigadeAudit.ToInt());
                    //state.Add(EnumContractStatus.SubmitOrgRegimentAuditSubmitOrgDivisionAudit.ToInt());
                }
                else if (org.OrgType == EnumOrgType.Battalion)
                {
                    state.Add(EnumContractStatus.InitialAuditPassSubmitOrgBrigadeAudit.ToInt());
                    state.Add(EnumContractStatus.OrgRegimentAuditSubmitOrgBrigadeAudit.ToInt());
                    //state.Add(EnumContractStatus.OrgBrigadeAuditPass.ToInt());
                    //state.Add(EnumContractStatus.OrgBrigadeAuditRefuse.ToInt());
                    //state.Add(EnumContractStatus.OrgBrigadeAuditSubmitOrgDivisionAudit.ToInt());

                    state.Add(EnumContractStatus.SubmitOrgBrigadeAudit.ToInt());
                    state.Add(EnumContractStatus.SubmitOrgRegimentAuditSubmitOrgBrigadeAudit.ToInt());
                    //state.Add(EnumContractStatus.SubmitOrgBrigadeAuditPass.ToInt());
                    //state.Add(EnumContractStatus.SubmitOrgBrigadeAuditRefuse.ToInt());
                    //state.Add(EnumContractStatus.SubmitOrgBrigadeAuditSubmitOrgDivisionAudit.ToInt());
                }
                else if (org.OrgType == EnumOrgType.BattleTeam)
                {
                    state.Add(EnumContractStatus.InitialAuditPassSubmitOrgDivisionAudit.ToInt());
                    state.Add(EnumContractStatus.OrgRegimentAuditSubmitOrgDivisionAudit.ToInt());
                    state.Add(EnumContractStatus.OrgBrigadeAuditSubmitOrgDivisionAudit.ToInt());
                    //state.Add(EnumContractStatus.OrgDivisionAuditPass.ToInt());
                    //state.Add(EnumContractStatus.OrgDivisionAuditRefuse.ToInt());

                    state.Add(EnumContractStatus.SubmitOrgDivisionAudit.ToInt());
                    state.Add(EnumContractStatus.SubmitOrgRegimentAuditSubmitOrgDivisionAudit.ToInt());
                    state.Add(EnumContractStatus.SubmitOrgBrigadeAuditSubmitOrgDivisionAudit.ToInt());
                    //state.Add(EnumContractStatus.SubmitOrgDivisionAuditPass.ToInt());
                    //state.Add(EnumContractStatus.SubmitOrgDivisionAuditRefuse.ToInt());
                }
            }

            state = state.Distinct().ToList();

            bool searchContractStatus = false;
            if (searchContractListIn.ContractStatus != null && searchContractListIn.ContractStatus.Count != 0)
            {
                searchContractStatus = true;
            }
            List<int?> ContractStatus = new List<int?>();

            bool automaticReviewStatus = false;
            bool manualReviewStatus = false;

            if (searchContractListIn.ContractStatus != null)
            {
                if (searchContractListIn.ContractStatus.Contains(200))
                {
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.InitialAuditPassSubmitOrgRegimentAudit.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.InitialAuditPassSubmitOrgBrigadeAudit.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.InitialAuditPassSubmitOrgDivisionAudit.ToInt());
                    //searchContractListIn.ContractStatus.Add(EnumContractStatus.OrgRegimentAuditPass.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.OrgRegimentAuditSubmitOrgBrigadeAudit.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.OrgRegimentAuditSubmitOrgDivisionAudit.ToInt());
                    //searchContractListIn.ContractStatus.Add(EnumContractStatus.OrgBrigadeAuditPass.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.OrgBrigadeAuditSubmitOrgDivisionAudit.ToInt());
                    //searchContractListIn.ContractStatus.Add(EnumContractStatus.OrgDivisionAuditPass.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.SubmitOrgRegimentAudit.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.SubmitOrgBrigadeAudit.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.SubmitOrgDivisionAudit.ToInt());
                    //searchContractListIn.ContractStatus.Add(EnumContractStatus.SubmitOrgRegimentAuditPass.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.SubmitOrgRegimentAuditSubmitOrgBrigadeAudit.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.SubmitOrgRegimentAuditSubmitOrgDivisionAudit.ToInt());
                    //searchContractListIn.ContractStatus.Add(EnumContractStatus.SubmitOrgBrigadeAuditPass.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.SubmitOrgBrigadeAuditSubmitOrgDivisionAudit.ToInt());
                    //searchContractListIn.ContractStatus.Add(EnumContractStatus.SubmitOrgDivisionAuditPass.ToInt());
                }
                if (searchContractListIn.ContractStatus.Contains(13))
                {
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.OrgRegimentAuditRefuse.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.OrgBrigadeAuditRefuse.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.OrgDivisionAuditRefuse.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.SubmitOrgRegimentAuditRefuse.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.SubmitOrgBrigadeAuditRefuse.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.SubmitOrgDivisionAuditRefuse.ToInt());
                }
                if (searchContractListIn.ContractStatus.Contains(12))
                {
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.OrgRegimentAuditPass.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.OrgBrigadeAuditPass.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.OrgDivisionAuditPass.ToInt());
                }
                if (searchContractListIn.ContractStatus.Contains(1))
                {
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.SubmitOrgRegimentAuditPass.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.SubmitOrgBrigadeAuditPass.ToInt());
                    searchContractListIn.ContractStatus.Add(EnumContractStatus.SubmitOrgDivisionAuditPass.ToInt());
                }

                if (searchContractListIn.ContractStatus.Contains(3))
                {
                    manualReviewStatus = true;
                    searchContractListIn.ContractStatus.Remove(3);
                }
                if (searchContractListIn.ContractStatus.Contains(8))
                {
                    automaticReviewStatus = true;
                    searchContractListIn.ContractStatus.Remove(8);
                }
            }
            ContractStatus = searchContractListIn.ContractStatus;

            int automaticReview = EnumAuditType.AutomaticReview.ToInt();
            int manualReview = EnumAuditType.ManualReview.ToInt();
            int pass = EnumContractStatus.Pass.ToInt();
            int inReview = EnumContractChangeAuditState.InReview.ToInt();
            var dt = DateTime.Now;
            DateTime startWeek = dt.AddDays(1 - Convert.ToInt32(dt.DayOfWeek.ToString("d")));
            DateTime endWeek = startWeek.AddDays(6);
            var listresult = Queryable
                .LeftJoin<Db_crm_customer_subcompany>((r, cs) => r.FirstParty == cs.Id)
                .LeftJoin<Db_crm_contract_paymentinfo>((r, cs, p) => r.Id == p.ContractId && p.Deleted == false)
                .LeftJoin<Db_v_userwithorg>((r, cs, p, u) => r.Issuer == u.Id)
                .LeftJoin<Db_v_productserviceinfostatus_new>((r, cs, p, u, vp) => r.Id == vp.ContractId)
                .LeftJoin<Db_crm_contract_audit>((r, cs, p, u, vp, ca) => r.Id == ca.ContractId && ca.IsHistory == false && ca.Deleted == false)
                .LeftJoin<Db_v_contract_invoice_state>((r, cs, p, u, vp, ca, vc) => r.Id == vc.ContractId)
                .LeftJoin<Db_crm_contract_change_appl>((r, cs, p, u, vp, ca, vc, cca) => r.Id == cca.ContractId && cca.Deleted == false && cca.State == inReview)
                .LeftJoin<Db_v_contract_achievementreceiptregisterinfo>((r, cs, p, u, vp, ca, vc, cca, crr) => r.Id == crr.ContractId)
                .LeftJoin<Db_crm_contract_stampreview>((r, cs, p, u, vp, ca, vc, cca, crr, csv) => r.Id == csv.ContractId && csv.IsHistory == false && csv.Deleted == false)
                .LeftJoin<Db_crm_contract_initialaudit>((r, cs, p, u, vp, ca, vc, cca, crr, csv, cii) => r.Id == cii.ContractId && cii.IsHistory == false && cii.Deleted == false)
                .LeftJoin<Db_v_customer_subcompany_private_user>((r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cspu) => cspu.Id == r.FirstParty)
                .Where((r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cspu) => r.Deleted == false && (ca.Deleted == false || csv.Deleted == false || cii.Deleted == false ||
                SqlFunc.Subqueryable<Db_crm_contract_regimentaudit>().Where(s => s.ContractId == r.Id && s.Deleted == false && s.IsHistory == false).Any() ||
                SqlFunc.Subqueryable<Db_crm_contract_brigadeaudit>().Where(s => s.ContractId == r.Id && s.Deleted == false && s.IsHistory == false).Any() ||
                SqlFunc.Subqueryable<Db_crm_contract_divisionaudit>().Where(s => s.ContractId == r.Id && s.Deleted == false && s.IsHistory == false).Any()
                ))
                .WhereIF(dataOwner, DataOwner.BusinessData("r.Issuer", "r.Id", "cspu.CompanyCurrentUser"))
                //.WhereIF(dataPermissionOwner && !ContractNameOrFirstPartyName, DataPermission.GetDataPermission<Db_crm_contract>(EnumDataSourceRelationShip.NoAssociated, r => r.Id, null))
                .WhereIF(!ContractNameOrFirstPartyName, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cspu) => state.Contains(r.ContractStatus))
                .WhereIF(!string.IsNullOrEmpty(searchContractListIn.ContractName), (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cspu) => r.ContractName.Contains(searchContractListIn.ContractName))
                .WhereIF(!string.IsNullOrEmpty(searchContractListIn.FirstPartyName), (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cspu) => cs.CompanyName.Contains(searchContractListIn.FirstPartyName))
                .WhereIF(!string.IsNullOrEmpty(searchContractListIn.ContractNum), (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cspu) => r.ContractNum == searchContractListIn.ContractNum)
                .WhereIF(searchContractListIn.Product != null && searchContractListIn.Product.Count > 0, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cspu) => SqlFunc.Subqueryable<Db_crm_contract_productinfo>().Where(s => (searchContractListIn.Product.Contains(s.ProductId) || searchContractListIn.Product.Contains(s.ParentProductId)) && s.ContractId == r.Id && s.Deleted == false).Any())
                .WhereIF(searchContractListIn.IssuerName != null && searchContractListIn.IssuerName.Count > 0, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cspu) => searchContractListIn.IssuerName.Contains(r.Issuer))
                .WhereIF(searchContractListIn.IsReceipt != null && searchContractListIn.IsReceipt != 0, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cspu) => SqlFunc.IsNull(crr.IsReceipt, 3) == searchContractListIn.IsReceipt)
                .WhereIF(searchContractListIn.PaymentMethod != null && searchContractListIn.PaymentMethod != 0, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cspu) => p.PaymentMethod == searchContractListIn.PaymentMethod)
                .WhereIF(!string.IsNullOrEmpty(searchContractListIn.CollectingCompany), (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cspu) => p.CollectingCompany == searchContractListIn.CollectingCompany)
                .WhereIF(searchContractListIn.ContractType != null && searchContractListIn.ContractType != 0, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cspu) => r.ContractType == searchContractListIn.ContractType)
                //.WhereIF(searchContractListIn.ContractStatus != null && searchContractListIn.ContractStatus != 0 && searchContractListIn.ContractStatus != 8 && searchContractListIn.ContractStatus != 3, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii) => r.ContractStatus == searchContractListIn.ContractStatus)
                //.WhereIF(searchContractListIn.ContractStatus != null && searchContractListIn.ContractStatus != 0 && searchContractListIn.ContractStatus == 8, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii) => r.ContractStatus == pass && ca.AuditType == automaticReview)
                //.WhereIF(searchContractListIn.ContractStatus != null && searchContractListIn.ContractStatus != 0 && searchContractListIn.ContractStatus == 3, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii) => r.ContractStatus == pass && ca.AuditType == manualReview)

                .WhereIF(searchContractStatus, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cspu) => searchContractListIn.ContractStatus.Contains(r.ContractStatus) || (automaticReviewStatus && r.ContractStatus == pass && ca.AuditType == automaticReview) || (manualReviewStatus && r.ContractStatus == pass && ca.AuditType == manualReview))
                .WhereIF(!string.IsNullOrEmpty(searchContractListIn.PaymentCompanyName), (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cspu) => SqlFunc.Subqueryable<Db_crm_contract_paymentinfo>().LeftJoin<Db_crm_customer_subcompany>((cp, cs) => cp.PaymentCompany == cs.Id).Where((cp, cs) => cs.CompanyName.Contains(searchContractListIn.PaymentCompanyName) && cp.ContractId == r.Id && cp.Deleted == false).Any())
                .WhereIF(!string.IsNullOrEmpty(searchContractListIn.IsBehalfPaymentCompanyName), (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cspu) => SqlFunc.Subqueryable<Db_crm_contract_paymentinfo>().LeftJoin<Db_crm_customer_subcompany>((cp, cs) => cp.PaymentCompany == cs.Id).Where((cp, cs) => cs.CompanyName.Contains(searchContractListIn.IsBehalfPaymentCompanyName) && cp.ContractId == r.Id && cp.IsBehalfPayment == true && cp.Deleted == false).Any())

                .WhereIF(searchContractListIn.Currency != null && searchContractListIn.Currency != 0, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cspu) => r.Currency == searchContractListIn.Currency)
                .WhereIF(searchContractListIn.ContractMethod != null && searchContractListIn.ContractMethod != 0, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cspu) => r.ContractMethod == searchContractListIn.ContractMethod)
                .WhereIF(searchContractListIn.ProductServiceInfoStatus != null && searchContractListIn.ProductServiceInfoStatus != 0, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cspu) => vp.ProductServiceInfoStatus == searchContractListIn.ProductServiceInfoStatus)
                .WhereIF(searchContractListIn.RecoveryStatus != null && searchContractListIn.RecoveryStatus != 0, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cspu) => r.UsageStatus == searchContractListIn.RecoveryStatus)
                .WhereIF(searchContractListIn.SigningDateStart != null && searchContractListIn.SigningDateEnd != null, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cspu) => SqlFunc.ToDateShort(r.SigningDate) >= SqlFunc.ToDateShort(searchContractListIn.SigningDateStart) && SqlFunc.ToDateShort(r.SigningDate) <= SqlFunc.ToDateShort(searchContractListIn.SigningDateEnd))
                .WhereIF(searchContractListIn.CreateDateStart != null && searchContractListIn.CreateDateEnd != null, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cspu) => SqlFunc.ToDateShort(r.CreateDate) >= SqlFunc.ToDateShort(searchContractListIn.CreateDateStart) && SqlFunc.ToDateShort(r.CreateDate) <= SqlFunc.ToDateShort(searchContractListIn.CreateDateEnd))
                .WhereIF(searchContractListIn.enumQueryListType == EnumQueryListType.Today, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cspu) => SqlFunc.DateIsSame(r.CreateDate, DateTime.Today))
                .WhereIF(searchContractListIn.enumQueryListType == EnumQueryListType.Week, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cspu) => SqlFunc.ToDateShort(r.CreateDate) >= SqlFunc.ToDateShort(startWeek) && SqlFunc.ToDateShort(r.CreateDate) <= SqlFunc.ToDateShort(endWeek))
                .WhereIF(!string.IsNullOrEmpty(searchContractListIn.AuditRemark), (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cspu) => cii.AuditRemark.Contains(searchContractListIn.AuditRemark))
                .WhereIF(searchContractListIn.IsBehalfPayment != null, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cspu) => p.IsBehalfPayment == searchContractListIn.IsBehalfPayment)
                .WhereIF(searchContractListIn.SalesCountry != null, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cspu) => r.SalesCountry == searchContractListIn.SalesCountry)
                .WhereIF(searchContractListIn.Country != null, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cspu) => r.Country == searchContractListIn.Country)
                .WhereIF(searchContractListIn.Province != null, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cspu) => r.Province == searchContractListIn.Province)
                .WhereIF(searchContractListIn.City != null, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cspu) => r.City == searchContractListIn.City)
                .WhereIF(!string.IsNullOrEmpty(searchContractListIn.Address), (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cspu) => r.Address.Contains(searchContractListIn.Address))
                //2025年6月5日 修改 增加条件
                .WhereIF(searchContractListIn.IsSecret.IsNotNull(), (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cspu) => r.IsSecret == searchContractListIn.IsSecret.Value)
                .Select((r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cspu) => new SearchContractList_Out
                {
                    Id = r.Id.SelectAll(),
                    FirstPartyName = cs.CompanyName,
                    //ProductName
                    ProductServiceInfoStatus = vp.ProductServiceInfoStatus,
                    IsReceipt = crr.IsReceipt == null ? 3 : crr.IsReceipt.Value,
                    IsInvoice = (int)vc.DisplayStatus,
                    //IsInvoiceName
                    IssuerName = u.Name,
                    CreateDate = r.CreateDate.Value.ToString("yyyy-MM-dd HH:mm"),
                    ContractAuditId = ca.Id,
                    IsContractChangeInReview = !SqlFunc.IsNullOrEmpty(cca.Id),
                    AuditType = ca.AuditType.Value,
                    StampreViewAuditId = csv.Id,
                    ContractInitialAuditId = cii.Id,
                    IsBehalfPayment = p.IsBehalfPayment,
                    IsSecret = r.IsSecret.Value,
                })
                //.OrderByDescending((r) => r.CreateDate)
                .OrderByPropertyName(searchContractListIn.SortField, searchContractListIn.IsDESC ? OrderByType.Desc : OrderByType.Asc)
                .OrderByIF(StringUtil.IsNullOrEmpty(searchContractListIn.SortField), (r) => r.CreateDate, OrderByType.Desc)
                .ToPageList(searchContractListIn.PageNumber, searchContractListIn.PageSize, ref total);

            Db.ThenMapper(listresult, item =>
            {
                var CompanyName = Db.Queryable<Db_crm_contract_paymentinfo>()
                .LeftJoin<Db_crm_customer_subcompany>((r, c) => r.PaymentCompany == c.Id && c.Deleted == 0)
                .Where((r, c) => r.IsBehalfPayment == true && r.Deleted == false)
                .Select((r, c) => new { ContractId = r.ContractId, IsBehalfPaymentCompanyName = c.CompanyName })
                .SetContext(r => r.ContractId, () => item.Id, item);

                item.IsBehalfPaymentCompanyName = CompanyName.Count == 0 ? "" : CompanyName.First().IsBehalfPaymentCompanyName;

                var PaymentCompanyNameList = Db.Queryable<Db_crm_contract_paymentinfo>()
               .LeftJoin<Db_crm_customer_subcompany>((r, c) => r.PaymentCompany == c.Id && c.Deleted == 0)
               .Where((r, c) => r.Deleted == false)
               .Select((r, c) => new { ContractId = r.ContractId, PaymentCompanyName = c.CompanyName })
               .SetContext(r => r.ContractId, () => item.Id, item);

                item.PaymentCompanyName = PaymentCompanyNameList.Count == 0 ? "" : PaymentCompanyNameList.First().PaymentCompanyName;

                var GrantCouponNum = Db.Queryable<Db_crm_customer_coupon>()
                .LeftJoin<Db_crm_customer_coupon_detail>((r, c) => r.Id == c.CouponId)
                .Where((r, c) => r.Deleted == false && c.Deleted == false && c.CouponType == 0)
                .Select((r, c) => new { CompanyId = r.CompanyId, CouponType = c.CouponType })
                .SetContext(r => r.CompanyId, () => item.FirstParty, item);

                item.GrantCouponNum = GrantCouponNum.Count;
            });
            return listresult;
        }

        /// <summary>
        /// 根据查询条件获取销售合同列表。
        /// 合同名称：支持模糊搜索
        /// 签约公司名称：支持模糊搜索
        /// 客户编码：支持精准搜索，只在确认业绩完成后生成
        /// 签约产品：自动获取当前已启用产品列表
        /// 出单人：自动获取合同申请人的姓名
        /// 到账情况：可筛选项全部到账、部分到账、未到账，默认未到账
        /// 支付方式：可选银行、现金、银行+现金
        /// 收款公司：可选项：信息技术、数据技术、科技发展、国际资讯、GLOBALWITS、青岛数据、济南数据、南京数据
        /// 签约类型：可选项：新增合同、合同续约、新增项目
        /// 合同状态：可选项：待审核、审核中、通过、拒绝、待确认、草稿、作废
        /// 币种：可选项：人民币CNY、美元USD
        /// 合同类型：可选项：电子合同、纸质合同
        /// 服务情况：可选项：全部开通、部分开通、待开通、服务变更、申请服务、服务过期
        /// 原件回收：可选项：已回收、未回收
        /// 签约日期：时间段搜索，最小时间单位日
        /// 创建时间：创建时间，时间格式：YYYY-MM-DD
        /// 根据用户数据权限返回相关数据。
        /// 返回结果按创建时间降序排列。
        /// </summary>
        /// <param name="searchContractListIn"></param>
        /// <param name="total"></param>
        /// <param name="dataOwner"></param>
        /// <returns></returns>
        public List<SearchContractStampreViewList_Out> SearchContractStampreViewList(SearchContractStampreViewList_In searchContractListIn, ref int total, bool dataOwner = false)
        {
            List<int?> ContractStatus = new List<int?>();
            if (searchContractListIn.ContractStatus != null)
            {
                if (searchContractListIn.ContractStatus == 200)
                {
                    ContractStatus.Add(EnumContractStatus.InitialAuditPassSubmitOrgRegimentAudit.ToInt());
                    ContractStatus.Add(EnumContractStatus.InitialAuditPassSubmitOrgBrigadeAudit.ToInt());
                    ContractStatus.Add(EnumContractStatus.InitialAuditPassSubmitOrgDivisionAudit.ToInt());
                    //searchContractListIn.ContractStatus.Add(EnumContractStatus.OrgRegimentAuditPass.ToInt());
                    ContractStatus.Add(EnumContractStatus.OrgRegimentAuditSubmitOrgBrigadeAudit.ToInt());
                    ContractStatus.Add(EnumContractStatus.OrgRegimentAuditSubmitOrgDivisionAudit.ToInt());
                    //searchContractListIn.ContractStatus.Add(EnumContractStatus.OrgBrigadeAuditPass.ToInt());
                    ContractStatus.Add(EnumContractStatus.OrgBrigadeAuditSubmitOrgDivisionAudit.ToInt());
                    //searchContractListIn.ContractStatus.Add(EnumContractStatus.OrgDivisionAuditPass.ToInt());
                    ContractStatus.Add(EnumContractStatus.SubmitOrgRegimentAudit.ToInt());
                    ContractStatus.Add(EnumContractStatus.SubmitOrgBrigadeAudit.ToInt());
                    ContractStatus.Add(EnumContractStatus.SubmitOrgDivisionAudit.ToInt());
                    //searchContractListIn.ContractStatus.Add(EnumContractStatus.SubmitOrgRegimentAuditPass.ToInt());
                    ContractStatus.Add(EnumContractStatus.SubmitOrgRegimentAuditSubmitOrgBrigadeAudit.ToInt());
                    ContractStatus.Add(EnumContractStatus.SubmitOrgRegimentAuditSubmitOrgDivisionAudit.ToInt());
                    //searchContractListIn.ContractStatus.Add(EnumContractStatus.SubmitOrgBrigadeAuditPass.ToInt());
                    ContractStatus.Add(EnumContractStatus.SubmitOrgBrigadeAuditSubmitOrgDivisionAudit.ToInt());
                    //searchContractListIn.ContractStatus.Add(EnumContractStatus.SubmitOrgDivisionAuditPass.ToInt());
                }
                if (searchContractListIn.ContractStatus == 13)
                {
                    ContractStatus.Add(EnumContractStatus.OrgRegimentAuditRefuse.ToInt());
                    ContractStatus.Add(EnumContractStatus.OrgBrigadeAuditRefuse.ToInt());
                    ContractStatus.Add(EnumContractStatus.OrgDivisionAuditRefuse.ToInt());
                    ContractStatus.Add(EnumContractStatus.SubmitOrgRegimentAuditRefuse.ToInt());
                    ContractStatus.Add(EnumContractStatus.SubmitOrgBrigadeAuditRefuse.ToInt());
                    ContractStatus.Add(EnumContractStatus.SubmitOrgDivisionAuditRefuse.ToInt());
                }
                if (searchContractListIn.ContractStatus == 12)
                {
                    ContractStatus.Add(EnumContractStatus.OrgRegimentAuditPass.ToInt());
                    ContractStatus.Add(EnumContractStatus.OrgBrigadeAuditPass.ToInt());
                    ContractStatus.Add(EnumContractStatus.OrgDivisionAuditPass.ToInt());
                }
                if (searchContractListIn.ContractStatus == 1)
                {
                    ContractStatus.Add(EnumContractStatus.SubmitOrgRegimentAuditPass.ToInt());
                    ContractStatus.Add(EnumContractStatus.SubmitOrgBrigadeAuditPass.ToInt());
                    ContractStatus.Add(EnumContractStatus.SubmitOrgDivisionAuditPass.ToInt());
                }
            }
            int AchievementState = EnumAchievementState.Confirmed.ToInt();
            int automaticReview = EnumAuditType.AutomaticReview.ToInt();
            int manualReview = EnumAuditType.ManualReview.ToInt();
            int pass = EnumContractStatus.Pass.ToInt();
            int inReview = EnumContractChangeAuditState.InReview.ToInt();
            var dt = DateTime.Now;
            DateTime startWeek = dt.AddDays(1 - Convert.ToInt32(dt.DayOfWeek.ToString("d")));
            DateTime endWeek = startWeek.AddDays(6);
            var list = Queryable
                .LeftJoin<Db_crm_customer_subcompany>((r, cs) => r.FirstParty == cs.Id)
                .LeftJoin<Db_crm_contract_paymentinfo>((r, cs, p) => r.Id == p.ContractId && p.Deleted == false)
                .LeftJoin<Db_v_userwithorg>((r, cs, p, u) => r.Issuer == u.Id)
                .LeftJoin<Db_v_productserviceinfostatus_new>((r, cs, p, u, vp) => r.Id == vp.ContractId)
                .LeftJoin<Db_crm_contract_audit>((r, cs, p, u, vp, ca) => r.Id == ca.ContractId && ca.IsHistory == false && ca.Deleted == false)
                .LeftJoin<Db_v_contract_invoice_state>((r, cs, p, u, vp, ca, vc) => r.Id == vc.ContractId)
                .LeftJoin<Db_crm_contract_change_appl>((r, cs, p, u, vp, ca, vc, cca) => r.Id == cca.ContractId && cca.Deleted == false && cca.State == inReview)
                .LeftJoin<Db_v_contract_achievementreceiptregisterinfo>((r, cs, p, u, vp, ca, vc, cca, crr) => r.Id == crr.ContractId)
                .LeftJoin<Db_crm_contract_stampreview>((r, cs, p, u, vp, ca, vc, cca, crr, csv) => r.Id == csv.ContractId && csv.IsHistory == false && csv.Deleted == false)
                .LeftJoin<Db_crm_contract_initialaudit>((r, cs, p, u, vp, ca, vc, cca, crr, csv, cii) => r.Id == cii.ContractId && cii.IsHistory == false && cii.Deleted == false)
                .LeftJoin<Db_v_contract_stampre>((r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => r.Id == cst.ContractId)
                //.Where((r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => r.Deleted == false && r.ContractStatus == pass && (ca.Deleted == false || csv.Deleted == false || cii.Deleted == false) && (cst.ElectronicContractAttachfileId != null || cst.SealedContractAttachfileId != null))
                .Where((r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => r.Deleted == false && r.ContractStatus == pass && (ca.Deleted == false || csv.Deleted == false || cii.Deleted == false))
                .WhereIF(dataOwner, DataOwner.BusinessData("r.Issuer", "r.Id"))
                .WhereIF(searchContractListIn.StampReviewStatus != null && searchContractListIn.StampReviewStatus != 0, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => r.StampReviewStatus == searchContractListIn.StampReviewStatus)
                //.WhereIF(searchContractListIn.StampReviewStatus != null && searchContractListIn.StampReviewStatus != 0, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => csv.Status == searchContractListIn.StampReviewStatus)
                .WhereIF(!string.IsNullOrEmpty(searchContractListIn.ContractName), (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => r.ContractName.Contains(searchContractListIn.ContractName))
                .WhereIF(!string.IsNullOrEmpty(searchContractListIn.FirstPartyName), (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => cs.CompanyName.Contains(searchContractListIn.FirstPartyName))
                .WhereIF(!string.IsNullOrEmpty(searchContractListIn.ContractNum), (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => r.ContractNum == searchContractListIn.ContractNum)
                .WhereIF(searchContractListIn.Product != null && searchContractListIn.Product.Count > 0, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => SqlFunc.Subqueryable<Db_crm_contract_productinfo>().Where(s => (searchContractListIn.Product.Contains(s.ProductId) || searchContractListIn.Product.Contains(s.ParentProductId)) && s.ContractId == r.Id && s.Deleted == false).Any())
                .WhereIF(searchContractListIn.IssuerName != null && searchContractListIn.IssuerName.Count > 0, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => searchContractListIn.IssuerName.Contains(r.Issuer))
                .WhereIF(searchContractListIn.IsReceipt != null && searchContractListIn.IsReceipt != 0, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => SqlFunc.IsNull(crr.IsReceipt, 3) == searchContractListIn.IsReceipt)
                .WhereIF(searchContractListIn.PaymentMethod != null && searchContractListIn.PaymentMethod != 0, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => p.PaymentMethod == searchContractListIn.PaymentMethod)
                .WhereIF(!string.IsNullOrEmpty(searchContractListIn.CollectingCompany), (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => p.CollectingCompany == searchContractListIn.CollectingCompany)
                .WhereIF(searchContractListIn.ContractType != null && searchContractListIn.ContractType != 0, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => r.ContractType == searchContractListIn.ContractType)
                //.WhereIF(searchContractListIn.ContractStatus != null && searchContractListIn.ContractStatus != 0 && searchContractListIn.ContractStatus != 8, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => r.ContractStatus == searchContractListIn.ContractStatus)
                //.WhereIF(searchContractListIn.ContractStatus != null && searchContractListIn.ContractStatus != 0 && searchContractListIn.ContractStatus == 8, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => r.ContractStatus == pass && ca.AuditType == automaticReview)
                .WhereIF(searchContractListIn.ContractStatus != null && searchContractListIn.ContractStatus != 0 && searchContractListIn.ContractStatus != 8 && searchContractListIn.ContractStatus != 3 && searchContractListIn.ContractStatus != 200 && searchContractListIn.ContractStatus != 13 && searchContractListIn.ContractStatus != 12 && searchContractListIn.ContractStatus != 1, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => r.ContractStatus == searchContractListIn.ContractStatus)
                .WhereIF(searchContractListIn.ContractStatus != null && searchContractListIn.ContractStatus != 0 && searchContractListIn.ContractStatus == 8, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => r.ContractStatus == pass && ca.AuditType == automaticReview)
                .WhereIF(searchContractListIn.ContractStatus != null && searchContractListIn.ContractStatus != 0 && searchContractListIn.ContractStatus == 3, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => r.ContractStatus == pass && ca.AuditType == manualReview)

                .WhereIF(searchContractListIn.ContractStatus != null && searchContractListIn.ContractStatus != 0 && (searchContractListIn.ContractStatus == 200 || searchContractListIn.ContractStatus == 13 || searchContractListIn.ContractStatus == 12 || searchContractListIn.ContractStatus == 1), (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => ContractStatus.Contains(r.ContractStatus))

                .WhereIF(searchContractListIn.Currency != null && searchContractListIn.Currency != 0, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => r.Currency == searchContractListIn.Currency)
                .WhereIF(searchContractListIn.ContractMethod != null && searchContractListIn.ContractMethod != 0, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => r.ContractMethod == searchContractListIn.ContractMethod)
                .WhereIF(searchContractListIn.ProductServiceInfoStatus != null && searchContractListIn.ProductServiceInfoStatus != 0, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => vp.ProductServiceInfoStatus == searchContractListIn.ProductServiceInfoStatus)
                .WhereIF(searchContractListIn.RecoveryStatus != null && searchContractListIn.RecoveryStatus != 0, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => r.UsageStatus == searchContractListIn.RecoveryStatus)
                .WhereIF(searchContractListIn.SigningDateStart != null && searchContractListIn.SigningDateEnd != null, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => SqlFunc.ToDateShort(r.SigningDate) >= SqlFunc.ToDateShort(searchContractListIn.SigningDateStart) && SqlFunc.ToDateShort(r.SigningDate) <= SqlFunc.ToDateShort(searchContractListIn.SigningDateEnd))
                .WhereIF(searchContractListIn.CreateDateStart != null && searchContractListIn.CreateDateEnd != null, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => SqlFunc.ToDateShort(r.CreateDate) >= SqlFunc.ToDateShort(searchContractListIn.CreateDateStart) && SqlFunc.ToDateShort(r.CreateDate) <= SqlFunc.ToDateShort(searchContractListIn.CreateDateEnd))
                .WhereIF(searchContractListIn.enumQueryListType == EnumQueryListType.Today, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => SqlFunc.DateIsSame(r.CreateDate, DateTime.Today))
                .WhereIF(searchContractListIn.enumQueryListType == EnumQueryListType.Week, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => SqlFunc.ToDateShort(r.CreateDate) >= SqlFunc.ToDateShort(startWeek) && SqlFunc.ToDateShort(r.CreateDate) <= SqlFunc.ToDateShort(endWeek))
                .WhereIF(searchContractListIn.ArrivalDateStart != null && searchContractListIn.ArrivalDateEnd != null, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => SqlFunc.Subqueryable<Db_crm_contract_receiptregister>().LeftJoin<Db_crm_contract_receipt_details>((e, f) => e.Id == f.ContractReceiptRegisterId && f.Deleted == false).LeftJoin<Db_crm_collectioninfo>((e, f, c) => f.CollectionInfoId == c.Id && c.Deleted == false).Where((e, f, c) => e.ContractId == r.Id && e.Deleted == false && e.AchievementState == AchievementState && SqlFunc.ToDateShort(c.ArrivalDate) >= SqlFunc.ToDateShort(searchContractListIn.ArrivalDateStart) && SqlFunc.ToDateShort(c.ArrivalDate) <= SqlFunc.ToDateShort(searchContractListIn.ArrivalDateEnd)).Any())
                .WhereIF(searchContractListIn.IsSettlementExchange != null && searchContractListIn.IsSettlementExchange != 0, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => SqlFunc.Subqueryable<Db_crm_contract_receiptregister>().Where(s => s.IsSettlementExchange == searchContractListIn.IsSettlementExchange && s.ContractId == r.Id && s.Deleted == false).Any())
                .WhereIF(searchContractListIn.IsHaveContractNum != null && searchContractListIn.IsHaveContractNum == true, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => r.ContractNum != null)
                .WhereIF(searchContractListIn.IsHaveContractNum != null && searchContractListIn.IsHaveContractNum == false, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => r.ContractNum == null)
                .WhereIF(searchContractListIn.IsBehalfPayment != null, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => p.IsBehalfPayment == searchContractListIn.IsBehalfPayment)
                .WhereIF(!string.IsNullOrEmpty(searchContractListIn.PaymentCompanyName), (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => SqlFunc.Subqueryable<Db_crm_contract_paymentinfo>().LeftJoin<Db_crm_customer_subcompany>((cp, cs) => cp.PaymentCompany == cs.Id).Where((cp, cs) => cs.CompanyName.Contains(searchContractListIn.PaymentCompanyName) && cp.ContractId == r.Id && cp.Deleted == false).Any())
                .WhereIF(!string.IsNullOrEmpty(searchContractListIn.IsBehalfPaymentCompanyName), (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => SqlFunc.Subqueryable<Db_crm_contract_paymentinfo>().LeftJoin<Db_crm_customer_subcompany>((cp, cs) => cp.PaymentCompany == cs.Id).Where((cp, cs) => cs.CompanyName.Contains(searchContractListIn.IsBehalfPaymentCompanyName) && cp.ContractId == r.Id && cp.IsBehalfPayment == true && cp.Deleted == false).Any())
                .Select((r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => new SearchContractStampreViewList_Out
                {
                    //Id = r.Id.SelectAll(),
                    Id = r.Id,
                    FirstPartyName = cs.CompanyName,
                    //ProductName
                    ProductServiceInfoStatus = vp.ProductServiceInfoStatus,
                    IsReceipt = crr.IsReceipt == null ? 3 : crr.IsReceipt.Value,
                    IsInvoice = (int)vc.DisplayStatus,
                    //IsInvoiceName
                    IssuerName = u.Name,
                    CreateDate = r.CreateDate.Value.ToString("yyyy-MM-dd HH:mm"),
                    ContractAuditId = ca.Id,
                    IsContractChangeInReview = !SqlFunc.IsNullOrEmpty(cca.Id),
                    AuditType = ca.AuditType.Value,
                    StampreViewAuditId = csv.Id,
                    ContractInitialAuditId = cii.Id,
                    IsBehalfPayment = p.IsBehalfPayment
                }, true)
                .MergeTable()
                .LeftJoin<Db_v_customer_subcompany_private_user>((mit, cspu) => cspu.Id == mit.FirstParty)
                .WhereIF(dataOwner, DataOwner.BusinessData("mit.Issuer", "mit.Id", "cspu.CompanyCurrentUser"))
                .Select(mit => mit)
                .MergeTable()
                //.OrderByDescending((r) => r.CreateDate)
                .OrderByPropertyName(searchContractListIn.SortField, searchContractListIn.IsDESC ? OrderByType.Desc : OrderByType.Asc)
                .OrderByIF(StringUtil.IsNullOrEmpty(searchContractListIn.SortField), it => it.CreateDate, OrderByType.Desc)
                .ToPageList(searchContractListIn.PageNumber, searchContractListIn.PageSize, ref total);

            Db.ThenMapper(list, item =>
            {
                var ReceiptregisterArrival = Db.Queryable<Db_crm_contract_receiptregister>()
                .LeftJoin<Db_crm_contract_receipt_details>((r, crr) => r.Id == crr.ContractReceiptRegisterId && crr.Deleted == false)
                .LeftJoin<Db_crm_collectioninfo>((r, crr, c) => crr.CollectionInfoId == c.Id && c.Deleted == false && r.Deleted == false)
                .Where((r, crr, c) => r.AchievementState == AchievementState && crr.Deleted == false && r.Deleted == false && c.Deleted == false)
                .GroupBy((r, crr, c) => new { r.ContractId })
                .Select((r, crr, c) => new { ContractId = r.ContractId, IsSettlementExchange = SqlFunc.AggregateMin(r.IsSettlementExchange), ArrivalDate = SqlFunc.AggregateMax(c.ArrivalDate) })
                .SetContext(r => r.ContractId, () => item.Id, item);

                item.IsSettlementExchange = ReceiptregisterArrival.Count == 0 ? null : ReceiptregisterArrival.First().IsSettlementExchange;
                item.ArrivalDate = ReceiptregisterArrival.Count == 0 ? null : ReceiptregisterArrival.First().ArrivalDate.Value.ToString("yyyy-MM-dd");

                var CompanyName = Db.Queryable<Db_crm_contract_paymentinfo>()
                .LeftJoin<Db_crm_customer_subcompany>((r, c) => r.PaymentCompany == c.Id && c.Deleted == 0)
                .Where((r, c) => r.IsBehalfPayment == true && r.Deleted == false)
                .Select((r, c) => new { ContractId = r.ContractId, IsBehalfPaymentCompanyName = c.CompanyName })
                .SetContext(r => r.ContractId, () => item.Id, item);

                item.IsBehalfPaymentCompanyName = CompanyName.Count == 0 ? "" : CompanyName.First().IsBehalfPaymentCompanyName;

                var GrantCouponNum = Db.Queryable<Db_crm_customer_coupon>()
                .LeftJoin<Db_crm_customer_coupon_detail>((r, c) => r.Id == c.CouponId)
                .Where((r, c) => r.Deleted == false && c.Deleted == false && c.CouponType == 0)
                .Select((r, c) => new { CompanyId = r.CompanyId, CouponType = c.CouponType })
                .SetContext(r => r.CompanyId, () => item.FirstParty, item);

                item.GrantCouponNum = GrantCouponNum.Count;
            });
            return list;
        }

        /// <summary>
        /// 根据查询条件获取销售合同列表。
        /// 合同名称：支持模糊搜索
        /// 签约公司名称：支持模糊搜索
        /// 客户编码：支持精准搜索，只在确认业绩完成后生成
        /// 签约产品：自动获取当前已启用产品列表
        /// 出单人：自动获取合同申请人的姓名
        /// 到账情况：可筛选项全部到账、部分到账、未到账，默认未到账
        /// 支付方式：可选银行、现金、银行+现金
        /// 收款公司：可选项：信息技术、数据技术、科技发展、国际资讯、GLOBALWITS、青岛数据、济南数据、南京数据
        /// 签约类型：可选项：新增合同、合同续约、新增项目
        /// 合同状态：可选项：待审核、审核中、通过、拒绝、待确认、草稿、作废
        /// 币种：可选项：人民币CNY、美元USD
        /// 合同类型：可选项：电子合同、纸质合同
        /// 服务情况：可选项：全部开通、部分开通、待开通、服务变更、申请服务、服务过期
        /// 原件回收：可选项：已回收、未回收
        /// 签约日期：时间段搜索，最小时间单位日
        /// 创建时间：创建时间，时间格式：YYYY-MM-DD
        /// 根据用户数据权限返回相关数据。
        /// 返回结果按创建时间降序排列。
        /// </summary>
        /// <param name="searchContractListIn"></param>
        /// <param name="total"></param>
        /// <param name="dataOwner"></param>
        /// <returns></returns>
        public List<SearchContractOtherStampreViewList_Out> SearchContractOtherStampreViewList(SearchContractOtherStampreViewList_In searchContractListIn, ref int total, bool dataOwner = false)
        {
            List<int?> ContractStatus = new List<int?>();
            if (searchContractListIn.ContractStatus != null)
            {
                if (searchContractListIn.ContractStatus == 200)
                {
                    ContractStatus.Add(EnumContractStatus.InitialAuditPassSubmitOrgRegimentAudit.ToInt());
                    ContractStatus.Add(EnumContractStatus.InitialAuditPassSubmitOrgBrigadeAudit.ToInt());
                    ContractStatus.Add(EnumContractStatus.InitialAuditPassSubmitOrgDivisionAudit.ToInt());
                    //searchContractListIn.ContractStatus.Add(EnumContractStatus.OrgRegimentAuditPass.ToInt());
                    ContractStatus.Add(EnumContractStatus.OrgRegimentAuditSubmitOrgBrigadeAudit.ToInt());
                    ContractStatus.Add(EnumContractStatus.OrgRegimentAuditSubmitOrgDivisionAudit.ToInt());
                    //searchContractListIn.ContractStatus.Add(EnumContractStatus.OrgBrigadeAuditPass.ToInt());
                    ContractStatus.Add(EnumContractStatus.OrgBrigadeAuditSubmitOrgDivisionAudit.ToInt());
                    //searchContractListIn.ContractStatus.Add(EnumContractStatus.OrgDivisionAuditPass.ToInt());
                    ContractStatus.Add(EnumContractStatus.SubmitOrgRegimentAudit.ToInt());
                    ContractStatus.Add(EnumContractStatus.SubmitOrgBrigadeAudit.ToInt());
                    ContractStatus.Add(EnumContractStatus.SubmitOrgDivisionAudit.ToInt());
                    //searchContractListIn.ContractStatus.Add(EnumContractStatus.SubmitOrgRegimentAuditPass.ToInt());
                    ContractStatus.Add(EnumContractStatus.SubmitOrgRegimentAuditSubmitOrgBrigadeAudit.ToInt());
                    ContractStatus.Add(EnumContractStatus.SubmitOrgRegimentAuditSubmitOrgDivisionAudit.ToInt());
                    //searchContractListIn.ContractStatus.Add(EnumContractStatus.SubmitOrgBrigadeAuditPass.ToInt());
                    ContractStatus.Add(EnumContractStatus.SubmitOrgBrigadeAuditSubmitOrgDivisionAudit.ToInt());
                    //searchContractListIn.ContractStatus.Add(EnumContractStatus.SubmitOrgDivisionAuditPass.ToInt());
                }
                if (searchContractListIn.ContractStatus == 13)
                {
                    ContractStatus.Add(EnumContractStatus.OrgRegimentAuditRefuse.ToInt());
                    ContractStatus.Add(EnumContractStatus.OrgBrigadeAuditRefuse.ToInt());
                    ContractStatus.Add(EnumContractStatus.OrgDivisionAuditRefuse.ToInt());
                    ContractStatus.Add(EnumContractStatus.SubmitOrgRegimentAuditRefuse.ToInt());
                    ContractStatus.Add(EnumContractStatus.SubmitOrgBrigadeAuditRefuse.ToInt());
                    ContractStatus.Add(EnumContractStatus.SubmitOrgDivisionAuditRefuse.ToInt());
                }
                if (searchContractListIn.ContractStatus == 12)
                {
                    ContractStatus.Add(EnumContractStatus.OrgRegimentAuditPass.ToInt());
                    ContractStatus.Add(EnumContractStatus.OrgBrigadeAuditPass.ToInt());
                    ContractStatus.Add(EnumContractStatus.OrgDivisionAuditPass.ToInt());
                }
                if (searchContractListIn.ContractStatus == 1)
                {
                    ContractStatus.Add(EnumContractStatus.SubmitOrgRegimentAuditPass.ToInt());
                    ContractStatus.Add(EnumContractStatus.SubmitOrgBrigadeAuditPass.ToInt());
                    ContractStatus.Add(EnumContractStatus.SubmitOrgDivisionAuditPass.ToInt());
                }
            }
            int AchievementState = EnumAchievementState.Confirmed.ToInt();
            int automaticReview = EnumAuditType.AutomaticReview.ToInt();
            int manualReview = EnumAuditType.ManualReview.ToInt();
            int pass = EnumContractStatus.Pass.ToInt();
            int Approved = EnumStampReviewStatus.Approved.ToInt();
            int inReview = EnumContractChangeAuditState.InReview.ToInt();
            var dt = DateTime.Now;
            DateTime startWeek = dt.AddDays(1 - Convert.ToInt32(dt.DayOfWeek.ToString("d")));
            DateTime endWeek = startWeek.AddDays(6);
            var list = Queryable
                .LeftJoin<Db_crm_customer_subcompany>((r, cs) => r.FirstParty == cs.Id)
                .LeftJoin<Db_crm_contract_paymentinfo>((r, cs, p) => r.Id == p.ContractId && p.Deleted == false)
                .LeftJoin<Db_v_userwithorg>((r, cs, p, u) => r.Issuer == u.Id)
                .LeftJoin<Db_v_productserviceinfostatus_new>((r, cs, p, u, vp) => r.Id == vp.ContractId)
                .LeftJoin<Db_crm_contract_audit>((r, cs, p, u, vp, ca) => r.Id == ca.ContractId && ca.IsHistory == false && ca.Deleted == false)
                .LeftJoin<Db_v_contract_invoice_state>((r, cs, p, u, vp, ca, vc) => r.Id == vc.ContractId)
                .LeftJoin<Db_crm_contract_change_appl>((r, cs, p, u, vp, ca, vc, cca) => r.Id == cca.ContractId && cca.Deleted == false && cca.State == inReview)
                .LeftJoin<Db_v_contract_achievementreceiptregisterinfo>((r, cs, p, u, vp, ca, vc, cca, crr) => r.Id == crr.ContractId)
                .LeftJoin<Db_crm_contract_otherstampreview>((r, cs, p, u, vp, ca, vc, cca, crr, csv) => r.Id == csv.ContractId && csv.IsHistory == false && csv.Deleted == false)
                .LeftJoin<Db_crm_contract_initialaudit>((r, cs, p, u, vp, ca, vc, cca, crr, csv, cii) => r.Id == cii.ContractId && cii.IsHistory == false && cii.Deleted == false)
                //.LeftJoin<Db_v_contract_stampre>((r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => r.Id == cst.ContractId)
                //.Where((r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => r.Deleted == false && r.ContractStatus == pass && (ca.Deleted == false || csv.Deleted == false || cii.Deleted == false) && (cst.ElectronicContractAttachfileId != null || cst.SealedContractAttachfileId != null))
                .Where((r, cs, p, u, vp, ca, vc, cca, crr, csv, cii) => r.Deleted == false && r.ContractStatus == pass && r.StampReviewStatus == Approved && (ca.Deleted == false || csv.Deleted == false || cii.Deleted == false))
                .WhereIF(dataOwner, DataOwner.BusinessData("r.Issuer", "r.Id"))
                .WhereIF(searchContractListIn.OtherStampReviewStatus != null && searchContractListIn.OtherStampReviewStatus != 0, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii) => r.OtherStampReviewStatus == searchContractListIn.OtherStampReviewStatus)
                //.WhereIF(searchContractListIn.StampReviewStatus != null && searchContractListIn.StampReviewStatus != 0, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => csv.Status == searchContractListIn.StampReviewStatus)
                .WhereIF(!string.IsNullOrEmpty(searchContractListIn.ContractName), (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii) => r.ContractName.Contains(searchContractListIn.ContractName))
                .WhereIF(!string.IsNullOrEmpty(searchContractListIn.FirstPartyName), (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii) => cs.CompanyName.Contains(searchContractListIn.FirstPartyName))
                .WhereIF(!string.IsNullOrEmpty(searchContractListIn.ContractNum), (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii) => r.ContractNum == searchContractListIn.ContractNum)
                .WhereIF(searchContractListIn.Product != null && searchContractListIn.Product.Count > 0, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii) => SqlFunc.Subqueryable<Db_crm_contract_productinfo>().Where(s => (searchContractListIn.Product.Contains(s.ProductId) || searchContractListIn.Product.Contains(s.ParentProductId)) && s.ContractId == r.Id && s.Deleted == false).Any())
                .WhereIF(searchContractListIn.IssuerName != null && searchContractListIn.IssuerName.Count > 0, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii) => searchContractListIn.IssuerName.Contains(r.Issuer))
                .WhereIF(searchContractListIn.IsReceipt != null && searchContractListIn.IsReceipt != 0, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii) => SqlFunc.IsNull(crr.IsReceipt, 3) == searchContractListIn.IsReceipt)
                .WhereIF(searchContractListIn.PaymentMethod != null && searchContractListIn.PaymentMethod != 0, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii) => p.PaymentMethod == searchContractListIn.PaymentMethod)
                .WhereIF(!string.IsNullOrEmpty(searchContractListIn.CollectingCompany), (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii) => p.CollectingCompany == searchContractListIn.CollectingCompany)
                .WhereIF(searchContractListIn.ContractType != null && searchContractListIn.ContractType != 0, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii) => r.ContractType == searchContractListIn.ContractType)
                //.WhereIF(searchContractListIn.ContractStatus != null && searchContractListIn.ContractStatus != 0 && searchContractListIn.ContractStatus != 8, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => r.ContractStatus == searchContractListIn.ContractStatus)
                //.WhereIF(searchContractListIn.ContractStatus != null && searchContractListIn.ContractStatus != 0 && searchContractListIn.ContractStatus == 8, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii, cst) => r.ContractStatus == pass && ca.AuditType == automaticReview)
                .WhereIF(searchContractListIn.ContractStatus != null && searchContractListIn.ContractStatus != 0 && searchContractListIn.ContractStatus != 8 && searchContractListIn.ContractStatus != 3 && searchContractListIn.ContractStatus != 200 && searchContractListIn.ContractStatus != 13 && searchContractListIn.ContractStatus != 12 && searchContractListIn.ContractStatus != 1, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii) => r.ContractStatus == searchContractListIn.ContractStatus)
                .WhereIF(searchContractListIn.ContractStatus != null && searchContractListIn.ContractStatus != 0 && searchContractListIn.ContractStatus == 8, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii) => r.ContractStatus == pass && ca.AuditType == automaticReview)
                .WhereIF(searchContractListIn.ContractStatus != null && searchContractListIn.ContractStatus != 0 && searchContractListIn.ContractStatus == 3, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii) => r.ContractStatus == pass && ca.AuditType == manualReview)

                .WhereIF(searchContractListIn.ContractStatus != null && searchContractListIn.ContractStatus != 0 && (searchContractListIn.ContractStatus == 200 || searchContractListIn.ContractStatus == 13 || searchContractListIn.ContractStatus == 12 || searchContractListIn.ContractStatus == 1), (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii) => ContractStatus.Contains(r.ContractStatus))

                .WhereIF(searchContractListIn.Currency != null && searchContractListIn.Currency != 0, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii) => r.Currency == searchContractListIn.Currency)
                .WhereIF(searchContractListIn.ContractMethod != null && searchContractListIn.ContractMethod != 0, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii) => r.ContractMethod == searchContractListIn.ContractMethod)
                .WhereIF(searchContractListIn.ProductServiceInfoStatus != null && searchContractListIn.ProductServiceInfoStatus != 0, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii) => vp.ProductServiceInfoStatus == searchContractListIn.ProductServiceInfoStatus)
                .WhereIF(searchContractListIn.RecoveryStatus != null && searchContractListIn.RecoveryStatus != 0, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii) => r.UsageStatus == searchContractListIn.RecoveryStatus)
                .WhereIF(searchContractListIn.SigningDateStart != null && searchContractListIn.SigningDateEnd != null, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii) => SqlFunc.ToDateShort(r.SigningDate) >= SqlFunc.ToDateShort(searchContractListIn.SigningDateStart) && SqlFunc.ToDateShort(r.SigningDate) <= SqlFunc.ToDateShort(searchContractListIn.SigningDateEnd))
                .WhereIF(searchContractListIn.CreateDateStart != null && searchContractListIn.CreateDateEnd != null, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii) => SqlFunc.ToDateShort(r.CreateDate) >= SqlFunc.ToDateShort(searchContractListIn.CreateDateStart) && SqlFunc.ToDateShort(r.CreateDate) <= SqlFunc.ToDateShort(searchContractListIn.CreateDateEnd))
                .WhereIF(searchContractListIn.enumQueryListType == EnumQueryListType.Today, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii) => SqlFunc.DateIsSame(r.CreateDate, DateTime.Today))
                .WhereIF(searchContractListIn.enumQueryListType == EnumQueryListType.Week, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii) => SqlFunc.ToDateShort(r.CreateDate) >= SqlFunc.ToDateShort(startWeek) && SqlFunc.ToDateShort(r.CreateDate) <= SqlFunc.ToDateShort(endWeek))
                .WhereIF(searchContractListIn.ArrivalDateStart != null && searchContractListIn.ArrivalDateEnd != null, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii) => SqlFunc.Subqueryable<Db_crm_contract_receiptregister>().LeftJoin<Db_crm_contract_receipt_details>((e, f) => e.Id == f.ContractReceiptRegisterId && f.Deleted == false).LeftJoin<Db_crm_collectioninfo>((e, f, c) => f.CollectionInfoId == c.Id && c.Deleted == false).Where((e, f, c) => e.ContractId == r.Id && e.Deleted == false && e.AchievementState == AchievementState && SqlFunc.ToDateShort(c.ArrivalDate) >= SqlFunc.ToDateShort(searchContractListIn.ArrivalDateStart) && SqlFunc.ToDateShort(c.ArrivalDate) <= SqlFunc.ToDateShort(searchContractListIn.ArrivalDateEnd)).Any())
                .WhereIF(searchContractListIn.IsSettlementExchange != null && searchContractListIn.IsSettlementExchange != 0, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii) => SqlFunc.Subqueryable<Db_crm_contract_receiptregister>().Where(s => s.IsSettlementExchange == searchContractListIn.IsSettlementExchange && s.ContractId == r.Id && s.Deleted == false).Any())
                .WhereIF(searchContractListIn.IsHaveContractNum != null && searchContractListIn.IsHaveContractNum == true, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii) => r.ContractNum != null)
                .WhereIF(searchContractListIn.IsHaveContractNum != null && searchContractListIn.IsHaveContractNum == false, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii) => r.ContractNum == null)
                .WhereIF(searchContractListIn.IsBehalfPayment != null, (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii) => p.IsBehalfPayment == searchContractListIn.IsBehalfPayment)
                .WhereIF(!string.IsNullOrEmpty(searchContractListIn.PaymentCompanyName), (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii) => SqlFunc.Subqueryable<Db_crm_contract_paymentinfo>().LeftJoin<Db_crm_customer_subcompany>((cp, cs) => cp.PaymentCompany == cs.Id).Where((cp, cs) => cs.CompanyName.Contains(searchContractListIn.PaymentCompanyName) && cp.ContractId == r.Id && cp.Deleted == false).Any())
                .WhereIF(!string.IsNullOrEmpty(searchContractListIn.IsBehalfPaymentCompanyName), (r, cs, p, u, vp, ca, vc, cca, crr, csv, cii) => SqlFunc.Subqueryable<Db_crm_contract_paymentinfo>().LeftJoin<Db_crm_customer_subcompany>((cp, cs) => cp.PaymentCompany == cs.Id).Where((cp, cs) => cs.CompanyName.Contains(searchContractListIn.IsBehalfPaymentCompanyName) && cp.ContractId == r.Id && cp.IsBehalfPayment == true && cp.Deleted == false).Any())
                .Select((r, cs, p, u, vp, ca, vc, cca, crr, csv, cii) => new SearchContractOtherStampreViewList_Out
                {
                    //Id = r.Id.SelectAll(),
                    Id = r.Id,
                    FirstPartyName = cs.CompanyName,
                    //ProductName
                    ProductServiceInfoStatus = vp.ProductServiceInfoStatus,
                    IsReceipt = crr.IsReceipt == null ? 3 : crr.IsReceipt.Value,
                    IsInvoice = (int)vc.DisplayStatus,
                    //IsInvoiceName
                    IssuerName = u.Name,
                    CreateDate = r.CreateDate.Value.ToString("yyyy-MM-dd HH:mm"),
                    ContractAuditId = ca.Id,
                    IsContractChangeInReview = !SqlFunc.IsNullOrEmpty(cca.Id),
                    AuditType = ca.AuditType.Value,
                    StampreViewAuditId = csv.Id,
                    ContractInitialAuditId = cii.Id,
                    IsBehalfPayment = p.IsBehalfPayment
                }, true)
                .MergeTable()
                .LeftJoin<Db_v_customer_subcompany_private_user>((mit, cspu) => cspu.Id == mit.FirstParty)
                .WhereIF(dataOwner, DataOwner.BusinessData("mit.Issuer", "mit.Id", "cspu.CompanyCurrentUser"))
                .Select(mit => mit)
                .MergeTable()
                //.OrderByDescending((r) => r.CreateDate)
                .OrderByPropertyName(searchContractListIn.SortField, searchContractListIn.IsDESC ? OrderByType.Desc : OrderByType.Asc)
                .OrderByIF(StringUtil.IsNullOrEmpty(searchContractListIn.SortField), it => it.CreateDate, OrderByType.Desc)
                .ToPageList(searchContractListIn.PageNumber, searchContractListIn.PageSize, ref total);

            Db.ThenMapper(list, item =>
            {
                var ReceiptregisterArrival = Db.Queryable<Db_crm_contract_receiptregister>()
                .LeftJoin<Db_crm_contract_receipt_details>((r, crr) => r.Id == crr.ContractReceiptRegisterId && crr.Deleted == false)
                .LeftJoin<Db_crm_collectioninfo>((r, crr, c) => crr.CollectionInfoId == c.Id && c.Deleted == false && r.Deleted == false)
                .Where((r, crr, c) => r.AchievementState == AchievementState && crr.Deleted == false && r.Deleted == false && c.Deleted == false)
                .GroupBy((r, crr, c) => new { r.ContractId })
                .Select((r, crr, c) => new { ContractId = r.ContractId, IsSettlementExchange = SqlFunc.AggregateMin(r.IsSettlementExchange), ArrivalDate = SqlFunc.AggregateMax(c.ArrivalDate) })
                .SetContext(r => r.ContractId, () => item.Id, item);

                item.IsSettlementExchange = ReceiptregisterArrival.Count == 0 ? null : ReceiptregisterArrival.First().IsSettlementExchange;
                item.ArrivalDate = ReceiptregisterArrival.Count == 0 ? null : ReceiptregisterArrival.First().ArrivalDate.Value.ToString("yyyy-MM-dd");

                var CompanyName = Db.Queryable<Db_crm_contract_paymentinfo>()
                .LeftJoin<Db_crm_customer_subcompany>((r, c) => r.PaymentCompany == c.Id && c.Deleted == 0)
                .Where((r, c) => r.IsBehalfPayment == true && r.Deleted == false)
                .Select((r, c) => new { ContractId = r.ContractId, IsBehalfPaymentCompanyName = c.CompanyName })
                .SetContext(r => r.ContractId, () => item.Id, item);

                item.IsBehalfPaymentCompanyName = CompanyName.Count == 0 ? "" : CompanyName.First().IsBehalfPaymentCompanyName;

                var GrantCouponNum = Db.Queryable<Db_crm_customer_coupon>()
                .LeftJoin<Db_crm_customer_coupon_detail>((r, c) => r.Id == c.CouponId)
                .Where((r, c) => r.Deleted == false && c.Deleted == false && c.CouponType == 0)
                .Select((r, c) => new { CompanyId = r.CompanyId, CouponType = c.CouponType })
                .SetContext(r => r.CompanyId, () => item.FirstParty, item);

                item.GrantCouponNum = GrantCouponNum.Count;
            });
            return list;
        }

        /// <summary>
        /// 根据合同Id获取到账登记的合同信息
        /// </summary>
        /// <param name="contractId"></param>
        /// <param name="dataOwner"></param>
        public ContractSimple_Out GetContractInfoByContractId(string contractId, bool dataOwner = false)
        {
            return Queryable
                .LeftJoin<Db_crm_customer_subcompany>((r, cs) => r.CustomerId == cs.CustomerId && cs.IsMain == 1 && cs.Deleted == 0)
                .LeftJoin<Db_crm_customer_subcompany>((r, cs, css) => r.FirstParty == css.Id)
                .LeftJoin<Db_sys_user>((r, cs, css, u) => r.Issuer == u.Id)
                .LeftJoin<Db_crm_collectingcompany>((r, cs, css, u, co) => r.SecondParty == co.Id)
                .LeftJoin<Db_v_customer_subcompany_private_user>((r, cs, css, u, co, cspu) => cspu.Id == r.FirstParty)
                .Where(r => r.Id == contractId && r.Deleted == false)
                .WhereIF(dataOwner, DataOwner.BusinessData("r.Issuer", "r.Id", "cspu.CompanyCurrentUser"))
                .Select((r, cs, css, u, co, cspu) => new ContractSimple_Out
                {
                    Id = r.Id.SelectAll(),
                    CustomerName = cs.CompanyName,
                    FirstPartyName = css.CompanyName,
                    IssuerName = u.Name,
                    SecondPartyName = co.CollectingCompanyName
                }, true)
                .First();
        }

        public ContractInfoAndReceipt_Out GetContractAndReceiptByContractId(string id, bool dataOwner = false)
        {
            //int Confirmed = EnumAutoMatchingState.Confirmed.ToInt();
            int AchievementState = EnumAchievementState.Confirmed.ToInt();
            var list = Queryable
                .LeftJoin<Db_sys_user>((c, su) => c.Issuer == su.Id)
                .LeftJoin<Db_crm_customer_subcompany>((c, su, ccs) => c.FirstParty == ccs.Id)
                .LeftJoin<Db_crm_contract_receiptregister>((c, su, ccs, crr) => c.Id == crr.ContractId && crr.AchievementState == AchievementState && crr.Deleted == false)
                .LeftJoin<Db_crm_contract_receipt_details>((c, su, ccs, crr, crd) => crr.Id == crd.ContractReceiptRegisterId && crd.Deleted == false)
                //.LeftJoin<Db_crm_contract_collectioninfo_automatching>((c, su, ccs, cca) => c.Id == cca.ContractId && cca.Deleted == false && cca.State == Confirmed)
                .LeftJoin<Db_crm_collectioninfo>((c, su, ccs, crr, crd, col) => crd.CollectionInfoId == col.Id && col.Deleted == false)
                .LeftJoin<Db_crm_collectingcompany>((c, su, ccs, crr, crd, col, co) => c.SecondParty == co.Id)
                .LeftJoin<Db_sys_country>((c, su, ccs, crr, crd, col, co, cou) => c.Country == cou.Id)
                .LeftJoin<Db_sys_province>((c, su, ccs, crr, crd, col, co, cou, pr) => c.Province == pr.Id)
                .LeftJoin<Db_sys_city>((c, su, ccs, crr, crd, col, co, cou, pr, ci) => c.City == ci.Id)
                .LeftJoin<Db_v_customer_subcompany_private_user>((c, su, ccs, crr, crd, col, co, cou, pr, ci, cspu) => cspu.Id == c.FirstParty)
                .LeftJoin<Db_v_productserviceinfostatus>((c, su, ccs, crr, crd, col, co, cou, pr, ci, cspu, vp) => c.Id == vp.ContractId)
                //.LeftJoin<Db_crm_contract_paymentinfo>((c, su, ccs, crr, crd, col, co, cou, pr, ci, cp) => c.Id == cp.ContractId && cp.Deleted == false)
                //.LeftJoin<Db_crm_customer_subcompany>((c, su, ccs, crr, crd, col, co, cou, pr, ci, cp, cps) => cp.PaymentCompany == cps.Id)
                //.LeftJoin<Db_crm_collectingcompany>((c, su, ccs, crr, crd, col, co, cou, pr, ci, cp, cps, cpc) => cp == co.Id)
                .GroupBy((c, su, ccs, crr, crd, col, co, cou, pr, ci, cspu, vp) => new
                {
                    c.Id,
                    vp.ProductServiceInfoStatus
                })
                .Where((c, su, ccs, crr, crd, col, co, cou, pr, ci, cspu, vp) => c.Id == id && c.Deleted == false)
                .WhereIF(dataOwner, DataOwner.BusinessData("c.Issuer", "c.Id", "cspu.CompanyCurrentUser"))
                //.WhereIF(dataOwner, DataOwner.IsPrivatePoolCustomer("c.CustomerId"))
                .Select<ContractInfoAndReceipt_Out>((c, su, ccs, crr, crd, col, co, cou, pr, ci, cspu, vp) => new ContractInfoAndReceipt_Out
                {
                    Id = c.Id,
                    CustomerId = c.CustomerId,
                    ContractNum = c.ContractNum,
                    ContractMethod = c.ContractMethod,
                    ContractNo = c.ContractNo,
                    ContractName = c.ContractName,
                    Issuer = c.Issuer,
                    SigningDate = c.SigningDate == null ? null : c.SigningDate.Value.ToString("yyyy-MM-dd"),
                    FirstParty = c.FirstParty,
                    SecondParty = c.SecondParty,
                    ContractAmount = c.ContractAmount,
                    FCContractAmount = c.FCContractAmount,
                    ContractCurrency = c.Currency,
                    ContractType = c.ContractType,
                    ContactInformation = c.ContactInformation.Value,
                    Contacts = c.Contacts,
                    Job = c.Job,
                    ContactWay = c.ContactWay,
                    Email = c.Email,
                    Telephone = c.Telephone,
                    Fax = c.Fax,
                    PostalCode = c.PostalCode,
                    Country = c.Country,
                    Province = c.Province,
                    City = c.City,
                    Address = c.Address,
                    ContractStatus = c.ContractStatus,
                    ProtectionDeadline = c.ProtectionDeadline == null ? null : c.ProtectionDeadline.Value.ToString("yyyy-MM-dd"),
                    IssuerName = su.Name,
                    FirstPartyName = ccs.CompanyName,
                    CreditType = ccs.CreditType,
                    SecondPartyName = co.CollectingCompanyName,
                    CountryName = cou.Name,
                    ProvinceName = pr.Name,
                    CityName = ci.Name,
                    Currency = SqlFunc.AggregateMin(col.Currency),
                    IsReceipt = SqlFunc.AggregateMin(crr.IsReceipt),
                    ArrivalAmount = SqlFunc.AggregateSum(col.ArrivalAmount),
                    ArrivalDate = SqlFunc.AggregateMax(col.ArrivalDate).ToString(),
                    PaymentMethod = SqlFunc.AggregateMax(col.PaymentMethod),
                    CollectingCompanyName = SqlFunc.AggregateMax(col.CollectingCompanyName),
                    BankPaymentAmount = SqlFunc.AggregateSum(col.BankPaymentAmount),
                    CashPaymentAmount = SqlFunc.AggregateSum(col.CashPaymentAmount),
                    CreditCode = ccs.CreditCode,
                    IsOverseasCustomer = c.IsOverseasCustomer,
                    SalesCountry = c.SalesCountry,
                    Remark = c.Remark,
                    RenewalContractNum = c.RenewalContractNum,
                    IsMerged = c.IsMerged,
                    ProductServiceInfoStatus = vp.ProductServiceInfoStatus
                    //ContractPaymentInfo = new ContractPaymentInfo {
                    //   ArrivalAmount = cp.ArrivalAmount,
                    //   ArrivalDate = cp.ArrivalDate.Value == null ? "" : cp.ArrivalDate.Value.ToString("yyyy-MM-dd"),
                    //   BankPaymentAmount = cp.BankPaymentAmount,
                    //    CashPaymentAmount = cp.CashPaymentAmount,
                    //     CollectingCompany = cp.CollectingCompany,
                    //      ContractId = cp.ContractId,
                    //       CreateDate = cp.CreateDate,
                    //        CreateUser = cp.CreateUser,
                    //         Currency = cp.Currency,
                    //           IsReceipt = cp.IsReceipt,
                    //            PaymentCompany = cp.PaymentCompany,
                    //             PaymentMethod = cp.PaymentMethod,
                    //               PaymentType = cp.PaymentType,
                    //                PlannedArrivalAmount = cp.PlannedArrivalAmount,
                    //                 PlannedArrivalDate = cp.PlannedArrivalDate.Value == null ? "" : cp.ArrivalDate.Value.ToString("yyyy-MM-dd"),
                    //                  CollectingCompanyName = 

                    //}
                })
                .Mapper(it =>
                {
                    it.SalesCountryName = LocalCache.LC_Address.CountryAndAreaCache.Find(c => c.Id == it.SalesCountry)?.Name ?? "";
                })
                .ToList();

            Db.ThenMapper(list, item =>
            {
                item.ArrivalDate = SqlFunc.IsNullOrEmpty(item.ArrivalDate) ? null : SqlFunc.ToDateShort(item.ArrivalDate).ToString("yyyy-MM-dd");
                item.ContractPaymentInfo = Db.Queryable<Db_crm_contract_paymentinfo>()
                .LeftJoin<Db_crm_customer_subcompany>((cp, cps) => cp.PaymentCompany == cps.Id && cps.Deleted == 0)
                .LeftJoin<Db_crm_collectingcompany>((cp, cps, cpc) => cp.CollectingCompany == cpc.Id && cpc.Deleted == false)
                .LeftJoin<Db_sys_country>((cp, cps, cpc, cou) => cp.PaymentCountry == cou.Id)
                .LeftJoin<Db_sys_province>((cp, cps, cpc, cou, pr) => cp.PaymentProvince == pr.Id)
                .LeftJoin<Db_sys_city>((cp, cps, cpc, cou, pr, ci) => cp.PaymentCity == ci.Id)
                .Where((cp, cps, cpc, cou, pr, ci) => cp.Deleted == false)
                .Select((cp, cps, cpc, cou, pr, ci) => new ContractPaymentInfo
                {
                    Id = cp.Id.SelectAll(),
                    PaymentCompanyName = cps.CompanyName,
                    CollectingCompanyName = cpc.CollectingCompanyName,
                    BehalfPaymentName = cps.CompanyName,
                    PaymentCountryName = cou.Name,
                    PaymentProvinceName = pr.Name,
                    PaymentCityName = ci.Name,
                })
                .SetContext(cp => cp.ContractId, () => item.Id, item)
                .ToList();
                // var SalesCountryNames = Db.Queryable<Db_sys_country>().SetContext(scl => scl.Id, () => item.SalesCountry, item).ToList();
                // item.SalesCountryName = SalesCountryNames.Count > 0 ? SalesCountryNames.First().Name : "";
            });
            if (list.Count == 0)
            {
                return null;
            }
            else
            {
                return list.First();
            }
        }

        public List<SearchContractBasicInfoList_Out> SearchContractBasicInfoList(SearchContractBasicInfoList_In searchContractBasicInfoListIn, string userID, ref int total)
        {
            int ContractStatus = EnumContractStatus.Pass.ToInt();
            return Queryable
                .LeftJoin<Db_crm_customer_subcompany>((r, cs) => r.FirstParty == cs.Id)
                .LeftJoin<Db_sys_user>((r, cs, u) => r.Issuer == u.Id)
                .LeftJoin<Db_crm_collectingcompany>((r, cs, u, co) => r.SecondParty == co.Id)
                .LeftJoin<Db_v_contract_invoice_state>((r, cs, u, co, vc) => r.Id == vc.ContractId)
                .LeftJoin<Db_v_customer_subcompany_private_user>((r, cs, u, co, vc, cspu) => cspu.Id == r.FirstParty)
                .WhereIF(true, DataOwner.BusinessData("r.Issuer", "", "cspu.CompanyCurrentUser", true))
                //.Where((r, cs, u, co, vc) => r.Deleted == false && r.Issuer == userID && r.ContractStatus == ContractStatus)
                .Where((r, cs, u, co, vc) => r.Deleted == false && r.ContractStatus == ContractStatus)
                .WhereIF(!string.IsNullOrEmpty(searchContractBasicInfoListIn.ContractNo), (r, cs, u, co, vc) => r.ContractNo.Contains(searchContractBasicInfoListIn.ContractNo))
                .WhereIF(!string.IsNullOrEmpty(searchContractBasicInfoListIn.ContractName), (r, cs, u, co, vc) => r.ContractName.Contains(searchContractBasicInfoListIn.ContractName))
                .WhereIF(searchContractBasicInfoListIn.CreateDateStart != null && searchContractBasicInfoListIn.CreateDateEnd != null, (r, cs, u, co, vc) => SqlFunc.ToDateShort(r.CreateDate) >= SqlFunc.ToDateShort(searchContractBasicInfoListIn.CreateDateStart) && SqlFunc.ToDateShort(r.CreateDate) <= SqlFunc.ToDateShort(searchContractBasicInfoListIn.CreateDateEnd))
                .OrderByDescending((r, cs, u, co, vc) => r.CreateDate)
                .Select((r, cs, u, co, vc) => new SearchContractBasicInfoList_Out
                {
                    Id = r.Id.SelectAll(),
                    FirstPartyName = cs.CompanyName,
                    SecondPartyName = co.CollectingCompanyName,
                    IssuerName = u.Name,
                    CreateDate = r.CreateDate.Value.ToString("yyyy-MM-dd"),
                    IsInvoice = (int)vc.DisplayStatus
                })
                .ToPageList(searchContractBasicInfoListIn.PageNumber, searchContractBasicInfoListIn.PageSize, ref total);
        }

        public List<SearchContractBasicInfoList_Out> SearchRegisterContractBasicInfoList(SearchRegisterContractBasicInfoList_In searchRegisterContractBasicInfoListIn, ref int total, bool dataOwner = false)
        {
            int ContractStatus = EnumContractStatus.Pass.ToInt();
            return Queryable
                .LeftJoin<Db_crm_customer_subcompany>((r, cs) => r.FirstParty == cs.Id)
                .LeftJoin<Db_sys_user>((r, cs, u) => r.Issuer == u.Id)
                .LeftJoin<Db_crm_collectingcompany>((r, cs, u, co) => r.SecondParty == co.Id)
                .LeftJoin<Db_v_customer_subcompany_private_user>((r, cs, u, co, cspu) => cspu.Id == r.FirstParty)
                .Where((r, cs, u, co, cspu) => r.Deleted == false && r.ContractStatus == ContractStatus)
                .WhereIF(!string.IsNullOrEmpty(searchRegisterContractBasicInfoListIn.ContractNum), (r, cs, u, co, cspu) => r.ContractNum == searchRegisterContractBasicInfoListIn.ContractNum)//(r, cs, u, co) => r.ContractNum.Contains(searchRegisterContractBasicInfoListIn.ContractNum))
                .WhereIF(!string.IsNullOrEmpty(searchRegisterContractBasicInfoListIn.FirstPartyName), (r, cs, u, co, cspu) => cs.CompanyName.Contains(searchRegisterContractBasicInfoListIn.FirstPartyName))
                .WhereIF(searchRegisterContractBasicInfoListIn.CreateDateStart != null && searchRegisterContractBasicInfoListIn.CreateDateEnd != null, (r, cs, u, co, cspu) => SqlFunc.ToDateShort(r.CreateDate) >= SqlFunc.ToDateShort(searchRegisterContractBasicInfoListIn.CreateDateStart) && SqlFunc.ToDateShort(r.CreateDate) <= SqlFunc.ToDateShort(searchRegisterContractBasicInfoListIn.CreateDateEnd))
                .WhereIF(dataOwner, DataOwner.BusinessData("r.Issuer", "r.Id", "cspu.CompanyCurrentUser"))
                .OrderByDescending((r, cs, u, co, cspu) => r.CreateDate)
                .Select((r, cs, u, co, cspu) => new SearchContractBasicInfoList_Out
                {
                    Id = r.Id.SelectAll(),
                    FirstPartyName = cs.CompanyName,
                    SecondPartyName = co.CollectingCompanyName,
                    IssuerName = u.Name,
                    CreateDate = r.CreateDate.Value.ToString("yyyy-MM-dd")
                })
                .ToPageList(searchRegisterContractBasicInfoListIn.PageNumber, searchRegisterContractBasicInfoListIn.PageSize, ref total);
        }

        public List<SearchContractBasicInfoList_Out> SearchRegisterContractBasicInfoNotAllReceivedList(SearchRegisterContractBasicInfoList_In searchRegisterContractBasicInfoListIn, ref int total, bool dataOwner = false)
        {
            int ToBeRegistered = EnumRegisterState.ToBeRegistered.ToInt();
            int AllReceived = EnumIsReceipt.AllReceived.ToInt();
            int ContractStatus = EnumContractStatus.Pass.ToInt();
            return Queryable
                .LeftJoin<Db_crm_customer_subcompany>((r, cs) => r.FirstParty == cs.Id)
                .LeftJoin<Db_sys_user>((r, cs, u) => r.Issuer == u.Id)
                .LeftJoin<Db_crm_collectingcompany>((r, cs, u, co) => r.SecondParty == co.Id)
                .LeftJoin<Db_v_customer_subcompany_private_user>((r, cs, u, co, cspu) => cspu.Id == r.FirstParty)
                .Where((r, cs, u, co, cspu) => r.Deleted == false && r.ContractStatus == ContractStatus && SqlFunc.Subqueryable<Db_crm_contract_receiptregister>().Where(cr => cr.IsReceipt == AllReceived && cr.ContractId == r.Id && cr.State != ToBeRegistered && cr.Deleted == false).NotAny())
                .WhereIF(!string.IsNullOrEmpty(searchRegisterContractBasicInfoListIn.ContractNum), (r, cs, u, co, cspu) => r.ContractNum == searchRegisterContractBasicInfoListIn.ContractNum)//(r, cs, u, co) => r.ContractNum.Contains(searchRegisterContractBasicInfoListIn.ContractNum))
                .WhereIF(!string.IsNullOrEmpty(searchRegisterContractBasicInfoListIn.FirstPartyName), (r, cs, u, co, cspu) => cs.CompanyName.Contains(searchRegisterContractBasicInfoListIn.FirstPartyName))
                .WhereIF(searchRegisterContractBasicInfoListIn.CreateDateStart != null && searchRegisterContractBasicInfoListIn.CreateDateEnd != null, (r, cs, u, co, cspu) => SqlFunc.ToDateShort(r.CreateDate) >= SqlFunc.ToDateShort(searchRegisterContractBasicInfoListIn.CreateDateStart) && SqlFunc.ToDateShort(r.CreateDate) <= SqlFunc.ToDateShort(searchRegisterContractBasicInfoListIn.CreateDateEnd))
                .WhereIF(dataOwner, DataOwner.BusinessData("r.Issuer", "r.Id", "cspu.CompanyCurrentUser"))
                .OrderByDescending((r, cs, u, co, cspu) => r.CreateDate)
                .Select((r, cs, u, co, cspu) => new SearchContractBasicInfoList_Out
                {
                    Id = r.Id.SelectAll(),
                    FirstPartyName = cs.CompanyName,
                    SecondPartyName = co.CollectingCompanyName,
                    IssuerName = u.Name,
                    //CreateDate = r.CreateDate.Value.ToString("yyyy-MM-dd")
                })
                .ToPageList(searchRegisterContractBasicInfoListIn.PageNumber, searchRegisterContractBasicInfoListIn.PageSize, ref total);
        }

        public void UpdateUrgeRegistration(string id, int isUrgeRegistration)
        {
            UpdateData(r => new Db_crm_contract() { IsUrgeRegistration = isUrgeRegistration }, id);
        }

        public int GetContractNumCount()
        {
            Db_crm_contract contract = Queryable.Where(r => r.ContractNum != null && r.ContractNum != "" && r.Deleted == false).OrderByDescending(r => r.ContractNum).First();
            if (contract == null)
            {
                return 0;
            }
            else
            {
                return contract.ContractNum.ToInt();
            }
        }

        /// <summary>
        /// 获取合同已开通服务数量
        /// </summary>
        /// <param name="contractId">合同ID</param>
        /// <param name="useIntegratedMode">是否使用整合模式（将GTIS、VIP、环球搜、SaleWits、慧思学院整合为慧思服务）</param>
        /// <returns>已开通服务数量</returns>
        public int ContractProductServiceInfoOpenedNum(string contractId, bool useIntegratedMode = true)
        {
            if (useIntegratedMode)
            {
                // 使用整合模式，调用新的整合方法
                return ContractProductServiceInfoOpenedNumIntegrated(contractId);
            }

            // 使用原有逻辑（兼容性）
            return Queryable
                .LeftJoin<Db_v_productserviceinfostatenum>((r, vp) => r.Id == vp.ContractId)
                .Where((r, vp) => r.Id == contractId)
                //.Where(r => r.ProtectionDeadline > DateTime.Now) //需求727要求：服务变更不再考虑保护截止日限制 2025.2.14
                .Sum((r, vp) =>
                    vp.OpenedNum
                ).ToInt();
        }

        ///// <summary>
        ///// 从客户获取合同（240906 这里数据权限调整一下，这个接口按客户的权限走，和发票/到账统一）
        ///// </summary>
        ///// <param name="getContractByCustomerIdIn"></param>
        ///// <param name="total"></param>
        ///// <param name="dataOwner"></param>
        ///// <returns></returns>
        //public List<GetContractByCustomerId_Out> GetContractByCustomerId(GetContractByCustomerId_In getContractByCustomerIdIn, ref int total, bool dataOwner = false)
        //{
        //    int automaticReview = EnumAuditType.AutomaticReview.ToInt();
        //    int pass = EnumContractStatus.Pass.ToInt();
        //    return Queryable
        //        .LeftJoin<Db_crm_customer_subcompany>((r, cs) => r.FirstParty == cs.Id)
        //        .LeftJoin<Db_crm_contract_paymentinfo>((r, cs, p) => r.Id == p.ContractId && p.Deleted == false)
        //        .LeftJoin<Db_sys_user>((r, cs, p, u) => r.Issuer == u.Id)
        //        .LeftJoin<Db_v_productserviceinfostatus>((r, cs, p, u, vp) => r.Id == vp.ContractId)
        //        .LeftJoin<Db_v_contractisinvoice>((r, cs, p, u, vp, vc) => r.Id == vc.contractId)
        //        .LeftJoin<Db_crm_collectingcompany>((r, cs, p, u, vp, vc, co) => p.CollectingCompany == co.Id)
        //        .LeftJoin<Db_crm_contract_audit>((r, cs, p, u, vp, vc, co, ca) => r.Id == ca.ContractId && ca.IsHistory == false && ca.Deleted == false)
        //        .Where((r, cs, p, u, vp, vc, co, ca) => r.Deleted == false && (cs.CustomerId == getContractByCustomerIdIn.CustomerId || SqlFunc.Subqueryable<Db_v_customer_merge>().Where(e => r.CustomerId == e.BeforeCustomerId && e.AfterCustomerId == getContractByCustomerIdIn.CustomerId).Any()))
        //        .WhereIF(!string.IsNullOrEmpty(getContractByCustomerIdIn.FirstPartyName), (r, cs, p, u, vp, vc, co, ca) => cs.CompanyName.Contains(getContractByCustomerIdIn.FirstPartyName))
        //        .WhereIF(getContractByCustomerIdIn.SigningDateStart != null && getContractByCustomerIdIn.SigningDateEnd != null, (r, cs, p, u, vp, vc, co, ca) => SqlFunc.ToDateShort(r.SigningDate) >= SqlFunc.ToDateShort(getContractByCustomerIdIn.SigningDateStart) && SqlFunc.ToDateShort(r.SigningDate) <= SqlFunc.ToDateShort(getContractByCustomerIdIn.SigningDateEnd))
        //        .WhereIF(getContractByCustomerIdIn.ContractStatus != null && getContractByCustomerIdIn.ContractStatus != 0 && getContractByCustomerIdIn.ContractStatus != 8, (r, cs, p, u, vp, vc, co, ca) => r.ContractStatus == getContractByCustomerIdIn.ContractStatus)
        //        .WhereIF(getContractByCustomerIdIn.ContractStatus != null && getContractByCustomerIdIn.ContractStatus != 0 && getContractByCustomerIdIn.ContractStatus == 8, (r, cs, p, u, vp, vc, co, ca) => r.ContractStatus == pass && ca.AuditType == automaticReview)
        //        .WhereIF(getContractByCustomerIdIn.ContractType != null && getContractByCustomerIdIn.ContractType != 0, (r, cs, p, u, vp, vc, co, ca) => r.ContractType == getContractByCustomerIdIn.ContractType)
        //        .WhereIF(getContractByCustomerIdIn.ProductServiceInfoStatus != null && getContractByCustomerIdIn.ProductServiceInfoStatus != 0, (r, cs, p, u, vp, vc, co, ca) => vp.ProductServiceInfoStatus == getContractByCustomerIdIn.ProductServiceInfoStatus)
        //        .WhereIF(getContractByCustomerIdIn.RecoveryStatus != null && getContractByCustomerIdIn.RecoveryStatus != 0, (r, cs, p, u, vp, vc, co, ca) => r.UsageStatus == getContractByCustomerIdIn.RecoveryStatus)
        //        //.WhereIF(dataOwner, DataOwner.BusinessData("r.CreateUser", "r.Id"))
        //        .Select((r, cs, p, u, vp, vc, co, ca) => new GetContractByCustomerId_Out
        //        {
        //            Id = r.Id.SelectAll(),
        //            FirstPartyName = cs.CompanyName,
        //            CollectingCompany = p.CollectingCompany,
        //            CollectingCompanyName = co.CollectingCompanyName,
        //            IssuerName = u.Name,
        //            ProductServiceInfoStatus = vp.ProductServiceInfoStatus,
        //            CreateDate = r.CreateDate.Value.ToString("yyyy-MM-dd"),
        //            AuditType = ca.AuditType.Value
        //        })
        //        .OrderByDescending((r) => r.CreateDate)
        //        .ToPageList(getContractByCustomerIdIn.PageNumber, getContractByCustomerIdIn.PageSize, ref total);
        //}
        /// <summary>
        /// 获取客户合同总额
        /// </summary>
        /// <param name="customerId"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        public decimal? GetContractAmountByCustomerId(string customerId, string userId = "")
        {
            return Queryable
                .LeftJoin<Db_crm_customer_subcompany>((c, company) => c.FirstParty == company.Id)
                .LeftJoinIF<Db_v_customer_subcompany_private_user>(StringUtil.IsNotNullOrEmpty(userId), (c, company, cspu) => cspu.Id == c.FirstParty)
                .WhereIF(StringUtil.IsNotNullOrEmpty(userId), DataOwner.BusinessData("c.Issuer", "", "cspu.CompanyCurrentUser"))
                .Where((c, company) => c.ContractStatus == (int)EnumContractStatus.Pass)
                .Where((c, company) => company.CustomerId == customerId)
                .Where((c, company) => c.Deleted == false)
                .GroupBy((c, company) => company.CustomerId)
                .Select((c, company) => SqlFunc.AggregateSum(c.ContractAmount))
                .First();
        }

        /// <summary>
        /// 客户数据-销售目标统计-合同金额累计趋势统计（时间取的合同审核通过时间）
        /// </summary>
        /// <param name="salesDataStatistics"></param>
        /// <param name="enumDateStatisticsType"></param>
        /// <returns></returns>
        public List<StatisticsSimpleItem<DateTime>> SalesTargetStatistics(SalesDataStatisticsParams salesDataStatistics, EnumDateStatisticsType enumDateStatisticsType = EnumDateStatisticsType.Current)
        {
            var queryableLeft = GetStatisticsDateSpanQuery(salesDataStatistics, enumDateStatisticsType);
            var query = Queryable
                .LeftJoin<Db_sys_user>((contract, user) => contract.Issuer == user.Id && user.Deleted == false)
                .LeftJoin<Db_crm_customer_subcompany>((contract, user, company) => contract.FirstParty == company.Id && company.Deleted == (int)EnumCustomerDel.NotDel)
                .LeftJoin<Db_crm_customer>((contract, user, company, customer) => company.CustomerId == customer.Id && customer.Deleted == (int)EnumCustomerDel.NotDel)
                .LeftJoin<Db_crm_contract_audit>((contract, user, company, customer, audit) => audit.ContractId == contract.Id && audit.Deleted == false && audit.IsHistory == false)
                .Where((contract, user, company, customer, audit) => contract.ContractStatus == (int)EnumContractStatus.Pass)
                .Where((contract, user, company, customer, audit) => audit.State == (int)EnumContractStatus.Pass)
                .WhereIF(!ArrayUtil.IsNullOrEmpty(salesDataStatistics.CityIds), (contract, user, company, customer, audit) => SqlFunc.ContainsArray(salesDataStatistics.CityIds, company.City))
                .WhereIF(!ArrayUtil.IsNullOrEmpty(salesDataStatistics.CustomerDataSources), (contract, user, company, customer, audit) => SqlFunc.ContainsArray(salesDataStatistics.CustomerDataSources, customer.CustomerSource))
                .WhereIF(!StringUtil.IsNullOrEmpty(salesDataStatistics.UserId), (contract, user, company, customer, audit) => salesDataStatistics.UserId == contract.Issuer)
                .WhereIF(!ArrayUtil.IsNullOrEmpty(salesDataStatistics.OrgDivisionId), (contract, user, company, customer, audit) => SqlFunc.ContainsArray(salesDataStatistics.OrgDivisionId, contract.OrgDivisionId))
                .WhereIF(!ArrayUtil.IsNullOrEmpty(salesDataStatistics.OrgBrigadeId), (contract, user, company, customer, audit) => SqlFunc.ContainsArray(salesDataStatistics.OrgBrigadeId, contract.OrgBrigadeId))
                .WhereIF(!ArrayUtil.IsNullOrEmpty(salesDataStatistics.OrgRegimentId), (contract, user, company, customer, audit) => SqlFunc.ContainsArray(salesDataStatistics.OrgRegimentId, contract.OrgRegimentId))
                .WhereIF(salesDataStatistics.DateStart != null, (contract, user, company, customer, audit) => audit.ReviewerDate >= salesDataStatistics.DateStart)
                .WhereIF(salesDataStatistics.DateEnd != null, (contract, user, company, customer, audit) => audit.ReviewerDate <= salesDataStatistics.DateEnd)
                .Select((contract, user, company, customer, audit) => new StatisticsSimpleItem<DateTime>
                {
                    Key = SqlFunc.ToDate(audit.ReviewerDate.Value.ToString("yyyy-MM-dd")),
                    Value = contract.ContractAmount
                })
                .MergeTable()
                .RightJoin(queryableLeft, (c, q) => c.Key >= q.StartDate && c.Key <= q.EndDate)
                .GroupBy((c, q) => new { q.Index, q.Key, q.StartDate })
                .Select((c, q) => new StatisticsSimpleItem<DateTime>
                {
                    Index = q.Index,
                    Key = q.StartDate,
                    Name = q.Key,
                    Value = SqlFunc.AggregateSum(c.Value)
                });
            var r = Db.Queryable(query, query, JoinType.Inner, (q1, q2) => q1.Key >= q2.Key)
            .GroupBy((q1, q2) => new { q1.Index, q1.Name, q1.Key })
            .OrderBy((q1, q2) => new { q1.Index, q1.Name, q1.Key })
            .Select((q1, q2) => new StatisticsSimpleItem<DateTime>
            {
                Index = q1.Index,
                Key = q1.Key,
                Name = q1.Name,
                Value = SqlFunc.AggregateSum(q2.Value)
            })
            .ToList();
            return r;
        }
        /// <summary>
        /// 客户数据-客户统计（时间取的合同审核通过时间）
        /// </summary>
        /// <param name="salesDataStatistics"></param>
        /// <returns></returns>
        public List<CustomerStatisticsItem_OUT> CustomerStatistics(SalesDataStatisticsParams salesDataStatistics)
        {
            List<CustomerStatisticsItem_OUT> customerStatisticsItem_OUTs = new List<CustomerStatisticsItem_OUT>();
            var queryableLeft = GetStatisticsDateSpanQuery(salesDataStatistics);
            var query = Queryable
                .LeftJoin<Db_sys_user>((contract, user) => contract.Issuer == user.Id && user.Deleted == false)
                .LeftJoin<Db_crm_customer_subcompany>((contract, user, company) => contract.FirstParty == company.Id && company.Deleted == (int)EnumCustomerDel.NotDel)
                .LeftJoin<Db_crm_customer>((contract, user, company, customer) => company.CustomerId == customer.Id && customer.Deleted == (int)EnumCustomerDel.NotDel)
                .LeftJoin<Db_crm_contract_audit>((contract, user, company, customer, audit) => audit.ContractId == contract.Id && audit.Deleted == false && audit.IsHistory == false)
                .Where((contract, user, company, customer, audit) => contract.ContractStatus == (int)EnumContractStatus.Pass)
                .Where((contract, user, company, customer, audit) => audit.State == (int)EnumContractStatus.Pass)
                //.Where((contract, user, company, customer, audit) => contract.ContractType == (int)EnumContractType.New)
                .Where((contract, user, company, customer, audit) => !(string.IsNullOrEmpty(contract.OrgBrigadeId) && string.IsNullOrEmpty(contract.OrgDivisionId) && string.IsNullOrEmpty(contract.OrgRegimentId)))
                .WhereIF(!ArrayUtil.IsNullOrEmpty(salesDataStatistics.CityIds), (contract, user, company, customer, audit) => SqlFunc.ContainsArray(salesDataStatistics.CityIds, company.City))
                .WhereIF(!ArrayUtil.IsNullOrEmpty(salesDataStatistics.CustomerDataSources), (contract, user, company, customer, audit) => SqlFunc.ContainsArray(salesDataStatistics.CustomerDataSources, customer.CustomerSource))
                .WhereIF(!StringUtil.IsNullOrEmpty(salesDataStatistics.UserId), (contract, user, company, customer, audit) => salesDataStatistics.UserId == contract.Issuer)
                .WhereIF(!ArrayUtil.IsNullOrEmpty(salesDataStatistics.OrgDivisionId), (contract, user, company, customer, audit) => SqlFunc.ContainsArray(salesDataStatistics.OrgDivisionId, contract.OrgDivisionId))
                .WhereIF(!ArrayUtil.IsNullOrEmpty(salesDataStatistics.OrgBrigadeId), (contract, user, company, customer, audit) => SqlFunc.ContainsArray(salesDataStatistics.OrgBrigadeId, contract.OrgBrigadeId))
                .WhereIF(!ArrayUtil.IsNullOrEmpty(salesDataStatistics.OrgRegimentId), (contract, user, company, customer, audit) => SqlFunc.ContainsArray(salesDataStatistics.OrgRegimentId, contract.OrgRegimentId))
                .WhereIF(salesDataStatistics.DateStart != null, (contract, user, company, customer, audit) => audit.ReviewerDate >= salesDataStatistics.DateStart)
                .WhereIF(salesDataStatistics.DateEnd != null, (contract, user, company, customer, audit) => audit.ReviewerDate <= salesDataStatistics.DateEnd)
                .Select((contract, user, company, customer, audit) => new
                {
                    Date = audit.ReviewerDate,
                    Level = customer.CustomerLevel,
                    CustomerId = company.CustomerId
                })
                .MergeTable()
                .RightJoin(queryableLeft, (c, q) => c.Date >= q.StartDate && c.Date <= q.EndDate)
                .GroupBy((c, q) => new { q.Index, q.Key, q.StartDate, c.Level })
                .OrderBy((c, q) => new { q.Index, q.Key, q.StartDate, c.Level })
                .Select((c, q) => new StatisticsTypeItem<string>
                {
                    Index = q.Index,
                    Key = q.Key,
                    Name = q.Key,
                    TypeKey = SqlFunc.ToString(c.Level),
                    Value = SqlFunc.AggregateDistinctCount(c.CustomerId)
                });
            var li = Db.Queryable(query).ToList();
            customerStatisticsItem_OUTs = Db.Queryable(query)
                .GroupBy(c => new { c.Index, c.Key, c.Name })
                .Select(c => new CustomerStatisticsItem_OUT
                {
                    Index = c.Index,
                    Name = c.Name,
                    Key = c.Key,
                    Count = SqlFunc.ToInt32(SqlFunc.AggregateSum(c.Value))
                })
                .Mapper(c =>
                {
                    c.Type = new List<CustomerStatisticsTypeItem_OUT>();
                    li.FindAll(d => d.Index == c.Index).ForEach(g =>
                    {
                        c.Type.Add(new CustomerStatisticsTypeItem_OUT
                        {
                            Key = g.TypeKey,
                            Name = StringUtil.IsNotNullOrEmpty(g.TypeKey) ? ((EnumCustomerLevel)(Int32.Parse(g.TypeKey))).GetEnumDescription() : "",
                            Count = g.Value == null ? 0 : g.Value.Value.ToInt()
                        });
                    });
                })
                .ToList();
            return customerStatisticsItem_OUTs;
        }
        /// <summary>
        /// 
        /// </summary>
        public SalesCitysStatistics_OUT SignContractStatistics(SalesAnalyseStatisticsParams salesAnalyseStatisticsParams)
        {
            SalesCitysStatistics_OUT salesCitysStatistics_OUT = new SalesCitysStatistics_OUT();
            var signDataQuery = GetSignDataQuery(salesAnalyseStatisticsParams);
            //var test = Db.Queryable(signDataQuery).ToList();
            var r = Db.Queryable(signDataQuery)
                .GroupBy(d => new { d.Country, d.Province, d.City })
                .Select(d => new SalesCitysStatisticsItem_OUT
                {
                    CountryKey = d.Country,
                    ProvinceKey = d.Province,
                    CityKey = d.City
                })
                .Mapper(d =>
                {
                    d.CountryName = LocalCache.LC_Address.CountryAndAreaCache.Find(c => c.Id == d.CountryKey)?.Name;
                    d.ProvinceName = LocalCache.LC_Address.ProvinceCache.Find(c => c.Id == d.ProvinceKey)?.Name;
                    d.CityName = LocalCache.LC_Address.CityCache.Find(c => c.Id == d.CityKey)?.Name;
                })
                .ToList();
            salesCitysStatistics_OUT.Citys = r;
            return salesCitysStatistics_OUT;

        }
        /// <summary>
        /// 获取统计条件下所有签约客户（如果有多个签约合同，取最新的一个签约合同的信息）
        /// </summary>
        /// <param name="salesAnalyseStatisticsParams"></param>
        /// <returns></returns>
        public ISugarQueryable<ContractStatistics> GetSignDataQuery(SalesAnalyseStatisticsParams salesAnalyseStatisticsParams)
        {
            return Queryable
                .LeftJoin<Db_crm_contract_audit>((contract, audit) => audit.ContractId == contract.Id && audit.Deleted == false && audit.IsHistory == false)
                //已经从数据层面删除的客户不统计在内了（从业务层面其实是不存在删除客户的）
                .LeftJoin<Db_crm_customer_subcompany>((contract, audit, company) => contract.FirstParty == company.Id)
                .Where((contract, audit, company) => contract.ContractStatus == (int)EnumContractStatus.Pass)
                //.Where((contract, audit, customer) => contract.ContractType == (int)EnumContractType.New || contract.ContractType == (int)EnumContractType.ReNew)
                .Where((contract, audit, company) => company.Deleted == (int)EnumCustomerDel.NotDel)
                .WhereIF(!ArrayUtil.IsNullOrEmpty(salesAnalyseStatisticsParams.OrgDivisionId), (contract, audit, company) => SqlFunc.ContainsArray(salesAnalyseStatisticsParams.OrgDivisionId, contract.OrgDivisionId))
                .WhereIF(!ArrayUtil.IsNullOrEmpty(salesAnalyseStatisticsParams.OrgBrigadeId), (contract, audit, company) => SqlFunc.ContainsArray(salesAnalyseStatisticsParams.OrgBrigadeId, contract.OrgBrigadeId))
                .WhereIF(!ArrayUtil.IsNullOrEmpty(salesAnalyseStatisticsParams.OrgRegimentId), (contract, audit, company) => SqlFunc.ContainsArray(salesAnalyseStatisticsParams.OrgRegimentId, contract.OrgRegimentId))
                .WhereIF(!StringUtil.IsNullOrEmpty(salesAnalyseStatisticsParams.UserId), (contract, audit, company) => contract.Issuer == salesAnalyseStatisticsParams.UserId)
                .WhereIF(salesAnalyseStatisticsParams.DateStart != null, (contract, audit, company) => audit.ReviewerDate >= salesAnalyseStatisticsParams.DateStart)
                .WhereIF(salesAnalyseStatisticsParams.DateEnd != null, (contract, audit, company) => audit.ReviewerDate <= salesAnalyseStatisticsParams.DateEnd)
                .Select((contract, audit, company) => new ContractStatistics
                {
                    index = SqlFunc.RowNumber(SqlFunc.Desc(audit.ReviewerDate), company.CustomerId),
                    Id = contract.Id.SelectAll(),
                    date = audit.ReviewerDate
                })
                .MergeTable()
                .Where(it => it.index == 1);
        }
        /// <summary>
        /// 获取统计条件下所有签约合同（取合同签约时间）
        /// </summary>
        /// <param name="staParams"></param>
        /// <returns></returns>
        public ISugarQueryable<ContractStatistics> GetSignContractQuery(SalesDataStatisticsParams staParams)
        {
            return Queryable
                //已经从数据层面删除的客户不统计在内了（从业务层面其实是不存在删除客户的）
                .LeftJoin<Db_crm_customer_subcompany>((contract, company) => contract.FirstParty == company.Id)
                .Where((contract, company) => contract.ContractStatus == (int)EnumContractStatus.Pass)
                .Where((contract, company) => company.Deleted == (int)EnumCustomerDel.NotDel)
                .Where((contract, company) => contract.Deleted == false)
                .WhereIF(!ArrayUtil.IsNullOrEmpty(staParams.OrgDivisionId), (contract, company) => SqlFunc.ContainsArray(staParams.OrgDivisionId, contract.OrgDivisionId))
                .WhereIF(!ArrayUtil.IsNullOrEmpty(staParams.OrgBrigadeId), (contract, company) => SqlFunc.ContainsArray(staParams.OrgBrigadeId, contract.OrgBrigadeId))
                .WhereIF(!ArrayUtil.IsNullOrEmpty(staParams.OrgRegimentId), (contract, company) => SqlFunc.ContainsArray(staParams.OrgRegimentId, contract.OrgRegimentId))
                .WhereIF(!StringUtil.IsNullOrEmpty(staParams.UserId), (contract, company) => contract.Issuer == staParams.UserId)
                .WhereIF(staParams.DateStart != null, (contract, company) => contract.SigningDate >= staParams.DateStart)
                .WhereIF(staParams.DateEnd != null, (contract, company) => contract.SigningDate <= staParams.DateEnd)
                .Select((contract, company) => new ContractStatistics
                {
                    Id = contract.Id.SelectAll(),
                    date = contract.SigningDate
                })
                .MergeTable();
        }
        /// <summary>
        /// 获取统计条件下所有到账合同（如果有多个到账合同，取最新的一个到账合同的信息）（10.9 时间改为取到账时间 240527 取业绩归属时间）
        /// </summary>
        /// <param name="salesAnalyseStatisticsParams"></param>
        /// <returns></returns>
        private ISugarQueryable<ContractStatistics> GetRecieveDataQuery(SalesAnalyseStatisticsParams salesAnalyseStatisticsParams)
        {
            return Db.Queryable<Db_crm_contract_receiptregister>()
                .LeftJoin<Db_crm_contract>((achivement, contract) => achivement.ContractId == contract.Id && contract.Deleted == false)
                //.LeftJoin<Db_crm_contract_receiptregister_achievement_audit>((achivement, contract, audit) => audit.ContractReceiptRegisterId == achivement.Id && audit.Deleted == false && audit.IsHistory == false)
                .LeftJoin<Db_v_contractreceiptregistercollectioninfo>((achivement, contract, audit) => achivement.Id == audit.ContractReceiptRegisterId)
                //已经从数据层面删除的客户不统计在内了（从业务层面其实是不存在删除客户的）
                .LeftJoin<Db_crm_customer_subcompany>((achivement, contract, audit, company) => contract.FirstParty == company.Id)
                .Where((achivement, contract, audit, company) => achivement.AchievementState == (int)EnumAchievementState.Confirmed)
                .Where((achivement, contract, audit, company) => company.Deleted == (int)EnumCustomerDel.NotDel)
                .WhereIF(!ArrayUtil.IsNullOrEmpty(salesAnalyseStatisticsParams.OrgDivisionId), (achivement, contract, audit, company) => SqlFunc.ContainsArray(salesAnalyseStatisticsParams.OrgDivisionId, contract.OrgDivisionId))
                .WhereIF(!ArrayUtil.IsNullOrEmpty(salesAnalyseStatisticsParams.OrgBrigadeId), (achivement, contract, audit, company) => SqlFunc.ContainsArray(salesAnalyseStatisticsParams.OrgBrigadeId, contract.OrgBrigadeId))
                .WhereIF(!ArrayUtil.IsNullOrEmpty(salesAnalyseStatisticsParams.OrgRegimentId), (achivement, contract, audit, company) => SqlFunc.ContainsArray(salesAnalyseStatisticsParams.OrgRegimentId, contract.OrgRegimentId))
                .WhereIF(!StringUtil.IsNullOrEmpty(salesAnalyseStatisticsParams.UserId), (achivement, contract, audit, company) => contract.Issuer == salesAnalyseStatisticsParams.UserId)
                .WhereIF(salesAnalyseStatisticsParams.DateStart != null, (achivement, contract, audit, company) => audit.BelongingMonth >= salesAnalyseStatisticsParams.DateStart)
                .WhereIF(salesAnalyseStatisticsParams.DateEnd != null, (achivement, contract, audit, company) => audit.BelongingMonth <= salesAnalyseStatisticsParams.DateEnd)
                .Select((achivement, contract, audit, company) => new ContractStatistics
                {
                    index = SqlFunc.RowNumber(SqlFunc.Desc(audit.BelongingMonth), company.CustomerId),
                    Id = contract.Id.SelectAll(),
                    date = audit.BelongingMonth
                })
                .MergeTable()
                .Where(it => it.index == 1);
        }
        /// <summary>
        /// 获取统计条件下所有保留客户数（包括签约到账等；只要保留/创建过就算;这里注意，人员调动问题，人员调动前后组织都算拥有过这个客户）
        /// </summary>
        /// <param name="salesAnalyseStatisticsParams"></param>
        /// <returns></returns>
        private int GetCustomerTotalQuery(SalesAnalyseStatisticsParams salesAnalyseStatisticsParams)
        {
            return Db.Queryable<Db_crm_customer_org_log>()
                .Where(customerlog => customerlog.Deleted == (int)EnumCustomerDel.NotDel)
                .WhereIF(!ArrayUtil.IsNullOrEmpty(salesAnalyseStatisticsParams.OrgDivisionId), customerlog => SqlFunc.ContainsArray(salesAnalyseStatisticsParams.OrgDivisionId, customerlog.OrgDivisionId))
                .WhereIF(!ArrayUtil.IsNullOrEmpty(salesAnalyseStatisticsParams.OrgBrigadeId), customerlog => SqlFunc.ContainsArray(salesAnalyseStatisticsParams.OrgBrigadeId, customerlog.OrgBrigadeId))
                .WhereIF(!ArrayUtil.IsNullOrEmpty(salesAnalyseStatisticsParams.OrgRegimentId), customerlog => SqlFunc.ContainsArray(salesAnalyseStatisticsParams.OrgRegimentId, customerlog.OrgRegimentId))
                .WhereIF(salesAnalyseStatisticsParams.DateStart != null, customerlog => customerlog.CreateDate >= salesAnalyseStatisticsParams.DateStart)
                .WhereIF(salesAnalyseStatisticsParams.DateEnd != null, customerlog => customerlog.CreateDate <= salesAnalyseStatisticsParams.DateEnd)
                .Select(customerlog => SqlFunc.AggregateDistinctCount(customerlog.CustomerId)).First();
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="salesAnalyseCountryStatisticsParams"></param>
        /// <returns></returns>
        public SalesCountryDetailAnalyseStatistics_OUT SalesCountryDetailAnalyseStatistics(SalesAnalyseCountryStatisticsParams salesAnalyseCountryStatisticsParams)
        {
            SalesCountryDetailAnalyseStatistics_OUT salesCountryDetailAnalyseStatistics_OUT = new SalesCountryDetailAnalyseStatistics_OUT();
            var signDataQuery = GetSignDataQuery(salesAnalyseCountryStatisticsParams);
            var total = GetCustomerTotalQuery(salesAnalyseCountryStatisticsParams);
            var signTotal = Db.Queryable(signDataQuery).Select(it => SqlFunc.AggregateDistinctCount(it.CustomerId)).First();
            //var t = Db.Queryable(signDataQuery).ToList();
            var signGroupData = Db.Queryable(signDataQuery)
                .LeftJoin<Db_crm_customer_subcompany>((contract, company) => contract.FirstParty == company.Id && company.Deleted == (int)EnumCustomerDel.NotDel)
                .LeftJoin<Db_crm_customer>((contract, company, customer) => company.CustomerId == customer.Id && customer.Deleted == (int)EnumCustomerDel.NotDel)
                .Where((contract, company, customer) => contract.Country == salesAnalyseCountryStatisticsParams.CountryId)
                .GroupBy((contract, company, customer) => new { customer.CustomerLevel })
                .Select((contract, company, customer) => new SalesCountryAnalyseStatisticsItem_OUT
                {
                    //以客户现在的CustomerLevel为准
                    TypeKey = customer.CustomerLevel,
                    Count = SqlFunc.AggregateCount(customer.Id)
                })
                .MergeTable()
                .OrderBy(it => it.Count, OrderByType.Desc)
                .Mapper(d =>
                {
                    d.TypeName = ((EnumCustomerLevel)d.TypeKey).GetEnumDescription();
                    d.TransPercent = decimal.Round(((decimal)d.Count / total) * 100, 2);
                })
                .ToList();
            var signCityRank = Db.Queryable(signDataQuery)
                .LeftJoin<Db_crm_customer_subcompany>((contract, company) => contract.FirstParty == company.Id && company.Deleted == (int)EnumCustomerDel.NotDel)
                .LeftJoin<Db_crm_customer>((contract, company, customer) => company.CustomerId == customer.Id && customer.Deleted == (int)EnumCustomerDel.NotDel)
                .LeftJoin<Db_crm_customer_subcompany>((contract, company, customer, mainCompany) => customer.Id == mainCompany.CustomerId && mainCompany.Deleted == (int)EnumCustomerDel.NotDel && mainCompany.IsMain == (int)EnumCustomerCompanyMain.Main)
                .Where((contract, company, customer, mainCompany) => contract.Country == salesAnalyseCountryStatisticsParams.CountryId)
                .GroupBy((contract, company, customer, mainCompany) => new { mainCompany.City })
                .Select((contract, company, customer, mainCompany) => new SalesAnalyseStatisticsRankItem_OUT
                {
                    //以客户（主公司）现在的City为准
                    Key = mainCompany.City,
                    Count = SqlFunc.AggregateCount(mainCompany.CustomerId)
                })
                .MergeTable()
                .OrderBy(it => it.Count, OrderByType.Desc)
                .Mapper(d =>
                {
                    d.Name = LocalCache.LC_Address.CityCache.Find(c => c.Id == d.Key)?.Name;
                })
                .ToPageList(1, 10);
            var reciveDataQuery = GetRecieveDataQuery(salesAnalyseCountryStatisticsParams);
            var reciveTotal = Db.Queryable(reciveDataQuery).Select(it => SqlFunc.AggregateDistinctCount(it.CustomerId)).First();
            var reciveGroupData = Db.Queryable(reciveDataQuery)
                .LeftJoin<Db_crm_customer_subcompany>((contract, company) => contract.FirstParty == company.Id && company.Deleted == (int)EnumCustomerDel.NotDel)
                .LeftJoin<Db_crm_customer>((contract, company, customer) => company.CustomerId == customer.Id && customer.Deleted == (int)EnumCustomerDel.NotDel)
                .Where((contract, company, customer) => contract.Country == salesAnalyseCountryStatisticsParams.CountryId)
                .GroupBy((contract, company, customer) => new { customer.CustomerLevel })
                .Select((contract, company, customer) => new SalesCountryAnalyseStatisticsItem_OUT
                {
                    //以客户现在的CustomerLevel为准
                    TypeKey = customer.CustomerLevel,
                    Count = SqlFunc.AggregateCount(customer.Id)
                })
                .MergeTable()
                .OrderBy(it => it.Count, OrderByType.Desc)
                .Mapper(d =>
                {
                    d.TypeName = ((EnumCustomerLevel)d.TypeKey).GetEnumDescription();
                    d.TransPercent = decimal.Round(((decimal)d.Count / total) * 100, 2);
                })
                .ToList();
            salesCountryDetailAnalyseStatistics_OUT.SignTotal = signTotal;
            salesCountryDetailAnalyseStatistics_OUT.SignCustomer = signGroupData;
            salesCountryDetailAnalyseStatistics_OUT.RecieveTotal = reciveTotal;
            salesCountryDetailAnalyseStatistics_OUT.ReciveCustomer = reciveGroupData;
            salesCountryDetailAnalyseStatistics_OUT.CitySignRank = signCityRank;
            return salesCountryDetailAnalyseStatistics_OUT;
        }
        public SalesCountryRankAnalyseStatistics_OUT SalesCountryRankAnalyseStatistics(SalesAnalyseCountryStatisticsParams salesAnalyseCountryStatisticsParams)
        {
            SalesCountryRankAnalyseStatistics_OUT salesCountryRankAnalyseStatistics_OUT = new SalesCountryRankAnalyseStatistics_OUT();
            var signDataQuery = GetSignDataQuery(salesAnalyseCountryStatisticsParams);
            var signCountryRankQuery = Db.Queryable(signDataQuery)
               .LeftJoin<Db_crm_customer_subcompany>((contract, company) => contract.FirstParty == company.Id && company.Deleted == (int)EnumCustomerDel.NotDel)
               .LeftJoin<Db_crm_customer>((contract, company, customer) => company.CustomerId == customer.Id && customer.Deleted == (int)EnumCustomerDel.NotDel)
               .LeftJoin<Db_crm_customer_subcompany>((contract, company, customer, mainCompany) => customer.Id == mainCompany.CustomerId && mainCompany.Deleted == (int)EnumCustomerDel.NotDel && mainCompany.IsMain == (int)EnumCustomerCompanyMain.Main)
               //.Where((contract, customer, mainCompany) => contract.Country == salesAnalyseCountryStatisticsParams.CountryId)
               .GroupBy((contract, company, customer, mainCompany) => new { mainCompany.Country })
               .Select((contract, company, customer, mainCompany) => new SalesAnalyseStatisticsRankItem_OUT
               {
                   //以客户（主公司）现在的Country为准
                   Key = mainCompany.Country,
                   Count = SqlFunc.AggregateCount(mainCompany.CustomerId)
               })
               .MergeTable();
            var signCountryRank = Db.Queryable(signCountryRankQuery)
               .OrderBy(it => it.Count, OrderByType.Desc)
               .Mapper(d =>
               {
                   d.Name = LocalCache.LC_Address.CountryAndAreaCache.Find(c => c.Id == d.Key)?.Name;
               })
               .ToPageList(1, 10);
            var signSelfRank = Db.Queryable(signCountryRankQuery)
                .OrderBy(it => it.Count, OrderByType.Desc)
                .ToList()
                .FindIndex(d => d.Key == salesAnalyseCountryStatisticsParams.CountryId) + 1;
            var reciveDataQuery = GetRecieveDataQuery(salesAnalyseCountryStatisticsParams);
            var reciveCountryRank = Db.Queryable(reciveDataQuery)
               .LeftJoin<Db_crm_customer_subcompany>((contract, company) => contract.FirstParty == company.Id && company.Deleted == (int)EnumCustomerDel.NotDel)
               .LeftJoin<Db_crm_customer>((contract, company, customer) => company.CustomerId == customer.Id && customer.Deleted == (int)EnumCustomerDel.NotDel)
               .LeftJoin<Db_crm_customer_subcompany>((contract, company, customer, mainCompany) => customer.Id == mainCompany.CustomerId && mainCompany.Deleted == (int)EnumCustomerDel.NotDel && mainCompany.IsMain == (int)EnumCustomerCompanyMain.Main)
               //.Where((contract, customer, mainCompany) => contract.Country == salesAnalyseCountryStatisticsParams.CountryId)
               .GroupBy((contract, company, customer, mainCompany) => new { mainCompany.Country })
               .Select((contract, company, customer, mainCompany) => new SalesAnalyseStatisticsRankItem_OUT
               {
                   //以客户（主公司）现在的Country为准
                   Key = mainCompany.Country,
                   Count = SqlFunc.AggregateCount(mainCompany.CustomerId)
               })
               .MergeTable()
               .OrderBy(it => it.Count, OrderByType.Desc)
               .Mapper(d =>
               {
                   d.Name = LocalCache.LC_Address.CountryAndAreaCache.Find(c => c.Id == d.Key)?.Name;
               })
               .ToPageList(1, 10);
            salesCountryRankAnalyseStatistics_OUT.SelfSignRank = signSelfRank;
            salesCountryRankAnalyseStatistics_OUT.CountrySignRank = signCountryRank;
            salesCountryRankAnalyseStatistics_OUT.CountryReciveRank = reciveCountryRank;
            return salesCountryRankAnalyseStatistics_OUT;
        }
        public SalesReciveCustomerSourceStatistics_OUT SalesReciveCustomerSourceStatistics(SalesAnalyseStatisticsParams salesAnalyseStatisticsParams)
        {
            SalesReciveCustomerSourceStatistics_OUT salesReciveCustomerSourceStatistics_OUT = new SalesReciveCustomerSourceStatistics_OUT();
            var reciveDataQuery = GetRecieveDataQuery(salesAnalyseStatisticsParams);
            var reciveTotal = Db.Queryable(reciveDataQuery).Select(it => SqlFunc.AggregateDistinctCount(it.CustomerId)).First();
            var reciveGroupQuery = Db.Queryable(reciveDataQuery)
                .LeftJoin<Db_crm_customer_subcompany>((contract, company) => contract.FirstParty == company.Id && company.Deleted == (int)EnumCustomerDel.NotDel)
                .LeftJoin<Db_crm_customer>((contract, company, customer) => company.CustomerId == customer.Id && customer.Deleted == (int)EnumCustomerDel.NotDel)
                .GroupBy((contract, company, customer) => new { customer.CustomerSource, customer.CustomerLevel })
                .Select((contract, company, customer) => new
                {
                    //以客户现在的CustomerSource为准
                    TypeKey = customer.CustomerSource,
                    Key = customer.CustomerLevel,
                    Count = SqlFunc.AggregateCount(customer.Id)
                })
                .MergeTable();
            var li = Db.Queryable(reciveGroupQuery).ToList();
            salesReciveCustomerSourceStatistics_OUT.StatisticsItems =
                Db.Queryable(reciveGroupQuery)
                .GroupBy(r => r.TypeKey)
                .Select(r => new SalesReciveCustomerSourceStatisticsItem_OUT
                {
                    Key = r.TypeKey,
                    Count = SqlFunc.AggregateSum(r.Count)
                })
                .Mapper(r =>
                {
                    r.Type = new List<CustomerStatisticsTypeItem_OUT>();
                    li.FindAll(d => d.TypeKey == r.Key).ForEach(g =>
                    {
                        r.Type.Add(new CustomerStatisticsTypeItem_OUT
                        {
                            Key = SqlFunc.ToString(g.Key),
                            Name = ((EnumCustomerLevel)(g.Key)).GetEnumDescription(),
                            Count = g.Count
                        });
                    });
                    r.Name = ((EnumCustomerSource)(r.Key)).GetEnumDescription();
                }).ToList();
            return salesReciveCustomerSourceStatistics_OUT;
        }
        public SalesReciveContractTypeTransStatistics_OUT SalesReciveContractTypeTransStatistics(SalesAnalyseStatisticsParams salesAnalyseStatisticsParams)
        {
            SalesReciveContractTypeTransStatistics_OUT salesReciveContractTypeTransStatistics_OUT = new SalesReciveContractTypeTransStatistics_OUT();
            var queryableLeft = GetStatisticsDateSpanQuery(salesAnalyseStatisticsParams);
            var reciveDataQuery = GetRecieveDataQuery(salesAnalyseStatisticsParams);
            var reciveTotal = Db.Queryable(reciveDataQuery).Select(it => SqlFunc.AggregateDistinctCount(it.CustomerId)).First();
            var reciveGroupQuery = Db.Queryable(reciveDataQuery)
                .LeftJoin<Db_crm_customer_subcompany>((contract, company) => contract.FirstParty == company.Id && company.Deleted == (int)EnumCustomerDel.NotDel)
                .LeftJoin<Db_crm_customer>((contract, company, customer) => company.CustomerId == customer.Id && customer.Deleted == (int)EnumCustomerDel.NotDel)
                .LeftJoin<Db_crm_contract_audit>((contract, company, customer, audit) => audit.ContractId == contract.Id && audit.Deleted == false && audit.IsHistory == false)
                .Where((contract, company, customer, audit) => contract.ContractStatus == (int)EnumContractStatus.Pass)
                .Where((contract, company, customer, audit) => audit.State == (int)EnumContractStatus.Pass)
                .Select((contract, company, customer, audit) => new
                {
                    DateTime = SqlFunc.ToDate(audit.ReviewerDate.Value.ToString("yyyy-MM-dd")),
                    ContractType = contract.ContractType,
                    CutomerId = customer.Id
                })
                .MergeTable()
                .GroupBy(it => new { it.ContractType, it.DateTime })
                .Select(it => new
                {
                    DateTime = it.DateTime,
                    ContractType = it.ContractType,
                    Count = SqlFunc.AggregateDistinctCount(it.CutomerId)
                })
                .MergeTable();
            var newR = Db.Queryable(reciveGroupQuery)
                .Where(q => q.ContractType == (int)EnumContractType.New)
                .MergeTable()
                .RightJoin(queryableLeft, (q, left) => q.DateTime >= left.StartDate && q.DateTime <= left.EndDate)
                .Select((q, left) => new ContractStatisticsTypeTransItem_OUT
                {
                    Index = left.Index,
                    DateKey = left.Key,
                    DateName = left.Key,
                    Count = q.Count,
                })
                .Mapper(it =>
                {
                    it.TypeKey = (int)EnumContractType.New;
                    it.TypeName = EnumContractType.New.GetEnumDescription();
                    it.Percent = decimal.Round(((decimal)it.Count / reciveTotal) * 100, 2);
                })
                .ToList();
            var renewR = Db.Queryable(reciveGroupQuery)
                .Where(q => q.ContractType == (int)EnumContractType.ReNew)
                .MergeTable()
                .RightJoin(queryableLeft, (q, left) => q.DateTime >= left.StartDate && q.DateTime <= left.EndDate)
                .Select((q, left) => new ContractStatisticsTypeTransItem_OUT
                {
                    Index = left.Index,
                    DateKey = left.Key,
                    DateName = left.Key,
                    Count = SqlFunc.IsNullOrEmpty(q.Count) ? 0 : q.Count,
                })
                .Mapper(it =>
                {
                    it.TypeKey = (int)EnumContractType.ReNew;
                    it.TypeName = EnumContractType.ReNew.GetEnumDescription();
                    it.Percent = decimal.Round(((decimal)it.Count / reciveTotal) * 100, 2);
                })
                .ToList();
            var addR = Db.Queryable(reciveGroupQuery)
                .Where(q => q.ContractType == (int)EnumContractType.AddItem)
                .MergeTable()
                .RightJoin(queryableLeft, (q, left) => q.DateTime >= left.StartDate && q.DateTime <= left.EndDate)
                .Select((q, left) => new ContractStatisticsTypeTransItem_OUT
                {
                    Index = left.Index,
                    DateKey = left.Key,
                    DateName = left.Key,
                    Count = SqlFunc.IsNullOrEmpty(q.Count) ? 0 : q.Count,
                })
                .Mapper(it =>
                {
                    it.TypeKey = (int)EnumContractType.AddItem;
                    it.TypeName = EnumContractType.AddItem.GetEnumDescription();
                    it.Percent = decimal.Round(((decimal)it.Count / reciveTotal) * 100, 2);
                })
                .ToList();
            salesReciveContractTypeTransStatistics_OUT.AddItemItems = addR;
            salesReciveContractTypeTransStatistics_OUT.NewItems = newR;
            salesReciveContractTypeTransStatistics_OUT.ReNewItems = renewR;
            return salesReciveContractTypeTransStatistics_OUT;
        }
        public SalesReciveCustomerSourceTransStatistics_OUT SalesReciveCustomerSourceTransStatistics(SalesAnalyseStatisticsParams salesAnalyseStatisticsParams)
        {
            SalesReciveCustomerSourceTransStatistics_OUT salesReciveCustomerSourceTransStatistics_OUT = new SalesReciveCustomerSourceTransStatistics_OUT();
            var queryableLeft = GetStatisticsDateSpanQuery(salesAnalyseStatisticsParams);
            var reciveDataQuery = GetRecieveDataQuery(salesAnalyseStatisticsParams);
            var reciveTotal = Db.Queryable(reciveDataQuery).Select(it => SqlFunc.AggregateDistinctCount(it.CustomerId)).First();
            var reciveGroupQuery = Db.Queryable(reciveDataQuery)
                .LeftJoin<Db_crm_customer_subcompany>((contract, company) => contract.FirstParty == company.Id && company.Deleted == (int)EnumCustomerDel.NotDel)
                .LeftJoin<Db_crm_customer>((contract, company, customer) => company.CustomerId == customer.Id && customer.Deleted == (int)EnumCustomerDel.NotDel)
                .LeftJoin<Db_crm_contract_audit>((contract, company, customer, audit) => audit.ContractId == contract.Id && audit.Deleted == false && audit.IsHistory == false)
                .Where((contract, company, customer, audit) => contract.ContractStatus == (int)EnumContractStatus.Pass)
                .Where((contract, company, customer, audit) => audit.State == (int)EnumContractStatus.Pass)
                .Select((contract, company, customer, audit) => new
                {
                    DateTime = SqlFunc.ToDate(audit.ReviewerDate.Value.ToString("yyyy-MM-dd")),
                    CustomerSource = customer.CustomerSource,
                    CutomerId = customer.Id
                })
                .MergeTable()
                .GroupBy(it => new { it.CustomerSource, it.DateTime })
                .Select(it => new
                {
                    DateTime = it.DateTime,
                    CustomerSource = it.CustomerSource,
                    Count = SqlFunc.AggregateDistinctCount(it.CutomerId)
                })
                .MergeTable();
            var customerSourceTable = Db.Reportable(new List<int>() {
                (int)EnumCustomerSource.Search,
                (int)EnumCustomerSource.Intro,
                (int)EnumCustomerSource.Ad,
                (int)EnumCustomerSource.ChinaExport,
                (int)EnumCustomerSource.Fair,
                (int)EnumCustomerSource.Other
            }).ToQueryable<int>();
            var mixTableQuery = Db.Queryable(customerSourceTable).InnerJoin(queryableLeft, (source, left) => true)
                .Select((source, left) => new StatisticsTypeDateSpan
                {
                    Index = left.Index.SelectAll(),
                    TypeKey = source.ColumnName
                })
                .MergeTable();
            var r = Db.Queryable(reciveGroupQuery)
                .RightJoin(mixTableQuery, (q, left) => q.DateTime >= left.StartDate && q.DateTime <= left.EndDate && q.CustomerSource == left.TypeKey)
                .Select((q, left) => new SalesReciveCustomerSourceTransStatisticsItem_OUT
                {
                    Index = left.Index,
                    DateKey = left.Key,
                    DateName = left.Key,
                    TypeKey = left.TypeKey,
                    Count = SqlFunc.IsNullOrEmpty(q.Count) ? 0 : q.Count,
                })
                .Mapper(it =>
                {
                    it.TypeName = ((EnumCustomerSource)it.TypeKey).GetEnumDescription();
                    it.Percent = decimal.Round(((decimal)it.Count / reciveTotal) * 100, 2);
                })
                .ToList();
            salesReciveCustomerSourceTransStatistics_OUT.StatisticsItems = r;
            return salesReciveCustomerSourceTransStatistics_OUT;
        }

        public List<GetContractAuditByContractId_Out> GetContractAuditByContractId(GetContractAuditByContractIdIn getContractAuditByContractIdIn, ref int total)
        {
            return Db.Queryable<Db_v_contract_audit>()
                .LeftJoin<Db_sys_user>((r, au) => r.ApplicantId == au.Id)
                .LeftJoin<Db_sys_user>((r, au, ru) => r.ReviewerId == ru.Id)
                .Where((r, au, ru) => r.ContractId == getContractAuditByContractIdIn.ContractId)
                .WhereIF(getContractAuditByContractIdIn.EndDateStart != null && getContractAuditByContractIdIn.EndDateEnd != null, (r, au, ru) => SqlFunc.ToDateShort(r.ReviewerDate) >= SqlFunc.ToDateShort(getContractAuditByContractIdIn.EndDateStart) && SqlFunc.ToDateShort(r.ReviewerDate) <= SqlFunc.ToDateShort(getContractAuditByContractIdIn.EndDateEnd))
                .Select((r, au, ru) => new GetContractAuditByContractId_Out
                {
                    Id = r.Id,
                    ContractId = r.ContractId,
                    ApplicantId = r.ApplicantId,
                    ApplicantName = au.Name,
                    ApplicantDate = r.ApplicantDate == null ? "" : r.ApplicantDate.Value.ToString("yyyy-MM-dd"),
                    State = r.State,
                    StateName = r.StateName,
                    ReviewerId = r.ReviewerId,
                    ReviewerName = ru.Name,
                    ReviewerDate = r.ReviewerDate == null ? "" : r.ReviewerDate.Value.ToString("yyyy-MM-dd"),
                    AuditCategory = r.AuditCategory,
                    Feedback = r.Feedback
                })
                .OrderByDescending(r => r.ReviewerDate)
                .ToPageList(getContractAuditByContractIdIn.PageNumber, getContractAuditByContractIdIn.PageSize, ref total);
        }


        public List<GetContractAuditByContractId4Record_Out> GetContractAuditByContractId(string contractId, DateTime? createDateStart, DateTime? createDateEnd)
        {
            return Db.Queryable<Db_v_contract_audit>()
                .LeftJoin<Db_sys_user>((r, au) => r.ApplicantId == au.Id)
                .LeftJoin<Db_sys_user>((r, au, ru) => r.ReviewerId == ru.Id)
                .LeftJoin<Db_crm_contract>((r, au, ru, c) => r.ContractId == c.Id)
                .Where((r, au, ru, c) => r.ContractId == contractId)
                .WhereIF(createDateStart != null, r => SqlFunc.IIF(r.ReviewerDate == null, SqlFunc.ToDateShort(r.ApplicantDate) >= SqlFunc.ToDateShort(createDateStart), SqlFunc.ToDateShort(r.ReviewerDate) >= SqlFunc.ToDateShort(createDateStart)))
                .WhereIF(createDateEnd != null, r => SqlFunc.IIF(r.ReviewerDate == null, SqlFunc.ToDateShort(r.ApplicantDate) >= SqlFunc.ToDateShort(createDateStart), SqlFunc.ToDateShort(r.ReviewerDate) >= SqlFunc.ToDateShort(createDateEnd)))
                .Select((r, au, ru, c) => new GetContractAuditByContractId4Record_Out
                {
                    Id = r.Id,
                    ContractId = r.ContractId,
                    ContractName = c.ContractName,
                    ApplicantId = r.ApplicantId,
                    ApplicantName = au.Name,
                    ApplicantDate = r.ApplicantDate == null ? "" : r.ApplicantDate.Value.ToString("yyyy-MM-dd"),
                    State = r.State,
                    StateName = r.StateName,
                    ReviewerId = r.ReviewerId,
                    ReviewerName = ru.Name,
                    ReviewerDate = r.ReviewerDate == null ? "" : r.ReviewerDate.Value.ToString("yyyy-MM-dd"),
                    AuditCategory = r.AuditCategory,
                    Feedback = r.Feedback,
                    AvatarImage = ru.AvatarImage,
                })
                .OrderByDescending(r => r.ReviewerDate)
                .ToList();
        }

        public void UpdateIsNotShowReceiptRegister(string id, bool isNotShow)
        {
            UpdateData(r => new Db_crm_contract() { IsNotShowReceiptRegister = isNotShow }, id);
        }
        /// <summary>
        /// 11.20 开通服务后 刷新合同保护截止日（同时刷新客户的保护截止日）
        /// 合同保护截止日规则：如果保护截止日早于服务结束时间 合同保护时间取时间最长的服务结束日+60天
        /// 2024.1.12  取消
        /// </summary>
        /// <param name="contractId"></param>
        public void RefreshContractProtectDate(string contractId)
        {
            //var contract = GetContractById(contractId);
            //if (contract != null)
            //{
            //    var company = DbOpe_crm_customer_subcompany.Instance.GetCompanyInfoById(contract.FirstParty);
            //    if (company == null)
            //    {
            //        throw new ApiException("未找到甲方公司");
            //    }
            //    PrivateCustomerSimpleInfo privateCustomer = null;
            //    if (!DbOpe_crm_customer_privatepool.Instance.CheckInPool(company.CustomerId, ref privateCustomer, contract.Issuer))
            //    {
            //        throw new ApiException("未找到客户");
            //    }
            //    //获取合同对应甲方公司所属客户的所有服务里结束时间最大的一条记录
            //    //11.28  一个客户有可能有多个合同，客户的保护截止日看所有合同的服务，合同的保护截止日只看这个合同的所有服务
            //    var serviceEndDatas = Db.Queryable<Db_v_customer_lastservice>().Where(s => s.CustomerId == company.CustomerId).ToList();
            //    if (serviceEndDatas != null && serviceEndDatas.Count > 0)
            //    {
            //        //合同保护时间
            //        var service_contract = serviceEndDatas.Find(s => s.ContractId == contractId);
            //        if (service_contract != null)
            //        {
            //            var serviceEndDay_contract = service_contract.ServiceCycleEnd.Value.GetDaysEnd();
            //            var protectDay = contract.ProtectionDeadline;
            //            if (protectDay == null || serviceEndDay_contract > protectDay)
            //            {
            //                //如果保护截止日早于最长的服务结束日，保护时间取时间最长的服务结束日+60天
            //                var serviceTargetProtectDay = serviceEndDay_contract.AddDays(60);
            //                contract.ProtectionDeadline = serviceTargetProtectDay;
            //                DbOpe_crm_contract.Instance.Update(contract);
            //            }
            //        }
            //        //客户保护时间
            //        var serviceEndDay_customer = serviceEndDatas.Max(s => s.ServiceCycleEnd).Value;
            //        var privateCustomerProtectDay = privateCustomer.ProtectionDeadline;
            //        if (privateCustomerProtectDay == null || serviceEndDay_customer > privateCustomerProtectDay)
            //        {
            //            //如果保护截止日早于最长的服务结束日，客户保护时间取时间最长的服务结束日+60天
            //            var serviceTargetProtectDay = serviceEndDay_customer.AddDays(60);
            //            privateCustomer.ProtectionDeadline = serviceTargetProtectDay;
            //            DbOpe_crm_customer_privatepool.Instance.Update(privateCustomer);
            //        }
            //    }
            //    else
            //    {
            //        //一个有效的开通服务都没了（都过期了或者作废了）
            //    }
            //}
            //else
            //{
            //    throw new ApiException("未找到合同");
            //}
        }

        /// <summary>
        /// 根据甲方公司名称&产品Id获取合同信息
        /// </summary>
        /// <param name="firstParty"></param>
        /// <param name="productId"></param>
        /// <returns></returns>
        public List<Db_crm_contract> GetContractInfoByFirstParty(string firstParty, string productId)
        {
            return Queryable
                .LeftJoin<Db_crm_contract_productinfo>((e, f) => e.Id == f.ContractId && f.Deleted == false)
                .Where(e => e.FirstParty == firstParty)
                .Where(e => e.Deleted == false)
                .WhereIF(!string.IsNullOrEmpty(productId), (e, f) => f.ProductId == productId)
                .Select<Db_crm_contract>((e, f) => e)
                .ToList();
        }

        /// <summary>
        /// 检查当前近甲方公司的所有合同中是否含有已经开通过的gtis或vip产品
        /// </summary>
        /// <param name="firstParty"></param>
        /// <returns></returns>
        public List<Db_v_productserviceinfo> CheckFirstPartyHaveGtisOrVip(string firstParty)
        {
            return Queryable
                .LeftJoin<Db_v_productserviceinfo>((e, f) => e.Id == f.ContractId)
                .Where(e => e.FirstParty == firstParty)
                .Where(e => e.Deleted == false)
                .Where((e, f) => f.ProductType == (int)EnumProductType.Gtis || f.ProductType == (int)EnumProductType.Vip)
                .Where((e, f) => f.ApplState == (int)EnumProcessStatus.Pass)
                .Where((e, f) => f.ServiceState == (int)EnumContractServiceState.VALID)
                .Select((e, f) => f)
                .ToList();
        }

        /// <summary>
        /// 检查当前近甲方公司的所有合同中是否含有已经开通过的gtis或vip产品
        /// </summary>
        /// <param name="firstPartyList"></param>
        /// <returns></returns>
        public List<Db_v_productserviceinfo> CheckFirstPartyHaveGtisOrVip(List<string> firstPartyList)
        {
            return Queryable
                .LeftJoin<Db_v_productserviceinfo>((e, f) => e.Id == f.ContractId)
                .Where(e => firstPartyList.Contains(e.FirstParty))
                .Where(e => e.Deleted == false)
                .Where((e, f) => f.ProductType == (int)EnumProductType.Gtis || f.ProductType == (int)EnumProductType.Vip)
                .Where((e, f) => f.ApplState == (int)EnumProcessStatus.Pass)
                .Select((e, f) => f)
                .ToList();
        }

        /// <summary>
        /// 获取即将到期的合同
        /// </summary>
        /// <returns></returns>
        public List<ContractDueReminder_Out> GetDueContractList()
        {
            int pass = EnumContractStatus.Pass.ToInt();
            DateTime ThirtyDay = DateTime.Now.AddDays(30);
            DateTime SevenDay = DateTime.Now.AddDays(7);
            DateTime SixDay = DateTime.Now.AddDays(6);
            DateTime Day = DateTime.Now;
            bool weekday = DateTime.Now.DayOfWeek.ToInt() == 0 || DateTime.Now.DayOfWeek.ToInt() == 6;
            return Queryable
                .LeftJoin<Db_v_contract_serviceinfo_servicecycleendmax>((r, s) => r.Id == s.ContractId)
                .LeftJoin<Db_crm_customer_subcompany>((r, s, cs) => r.FirstParty == cs.Id)
                .Where((r, s, cs) => r.Deleted == false && r.ContractStatus == pass && (SqlFunc.ToDateShort(s.ServiceCycleEndMax) == SqlFunc.ToDateShort(ThirtyDay) || SqlFunc.ToDateShort(s.ServiceCycleEndMax) == SqlFunc.ToDateShort(SevenDay) || (SqlFunc.ToDateShort(s.ServiceCycleEndMax) >= SqlFunc.ToDateShort(Day) && SqlFunc.ToDateShort(s.ServiceCycleEndMax) <= SqlFunc.ToDateShort(SixDay) && !weekday)))
                .Select<ContractDueReminder_Out>((r, s, cs) => new ContractDueReminder_Out
                {
                    ContractName = r.ContractName,
                    ContractNo = r.ContractNo,
                    FirstParty = r.FirstParty,
                    Issuer = r.Issuer,
                    FirstPartyName = cs.CompanyName
                }).ToList();
        }

        public Db_crm_contract GetContractInfoByContractNum(string ContractNum)
        {
            return Queryable
                .Where(e => e.ContractNum == ContractNum)
                .Where(e => e.Deleted == false)
                .First();
        }

        public Db_crm_contract GetOldContractInfo(string contractId, string firstParty)
        {
            return Queryable
                .Where(e => e.FirstParty == firstParty)
                .Where(e => e.Id != contractId)
                .Where(e => e.Deleted == false)
                .OrderByDescending(e => e.CreateDate)
            .First();
        }

        public bool IsHaveProductServiceInfo(string contractId)
        {
            bool result = false;
            var info = Db.Queryable<Db_v_productserviceinfostatus>().Where(w => w.ContractId == contractId).First();
            if (info != null)
            {
                if (info.ProductServiceInfoStatus != 8 && info.ProductServiceInfoStatus != 2)
                {
                    //开通状态的期刊以外的服务
                    var havePassServWithoutProject = Db.Queryable<Db_crm_contract_productinfo>()
                       .LeftJoin<Db_v_productserviceinfo>((r, v) => r.Id == v.Id)
                       .Where((r, v) => r.ContractId == contractId && r.Deleted == false && v.ApplState == (int)EnumProcessStatus.Pass && v.ProductType != (int)EnumProductType.Periodicals)
                       .Any();
                    //期刊邮寄的记录
                    var haveMailed = Db.Queryable<Db_crm_contract_serviceinfo_mailing>()
                        .Where(e => e.ContractId == contractId)
                        .Where(e => e.Deleted == false)
                        .Any();
                    //存在：开通状态的期刊以外的服务 或者 期刊邮寄的记录
                    if (havePassServWithoutProject || haveMailed)
                        result = true;
                }
            }

            return result;
        }

        public List<string> GetHistoryContractByContractId(string contractId)
        {
            List<string> reault = new List<string>();
            var parameter = new { contractid = contractId };
            var dataReader = Db.Ado.UseStoredProcedure().GetDataTable("p_GetHistoryContractByContractId", parameter);
            foreach (DataRow row in dataReader.Rows)
            {
                reault.Add(row["id"].ToString());
            }
            return reault;
        }


        /// <summary>
        /// 切换合同保密状态
        /// </summary>
        /// <param name="ContractId"></param>
        /// <param name="IsSecret"></param>
        public void SwitchContractSecretState(string ContractId, bool IsSecret)
        {
            Updateable
                .SetColumns(e => e.IsSecret == IsSecret)
                .Where(e => e.Id == ContractId)
                .ExecuteCommand();
        }

        /// <summary>
        /// 合同通过后，一定时间内如果不到账（没有匹配的银行到账）
        /// </summary>
        /// <returns></returns>
        public List<Db_crm_contract> GetAutoVoidContract(int Days)
        {
            var dt = DateTime.Now;
            var end = dt.AddDays(-Days).ToString("yyyy-MM-dd");
            var start = dt.ToString("yyyy-MM-dd");
            int Denied = EnumAutoMatchingState.Denied.ToInt();
            int ContractStatus = EnumContractStatus.Pass.ToInt();
            return Queryable
                .LeftJoin<Db_crm_contract_collectioninfo_automatching>((r, ca) => r.Id == ca.ContractId && ca.State != Denied && ca.Deleted == false)
                .LeftJoin<Db_crm_contract_receiptregister>((r, ca, cr) => r.Id == cr.ContractId && cr.Deleted == false)
                .Where((r, ca, cr) => r.Deleted == false && r.NewContractId == null && r.ContractStatus == ContractStatus && ca.Id == null && cr.Id == null && SqlFunc.ToDateShort(r.SigningDate) < SqlFunc.ToDateShort(end))
                .Where((r, ca, cr) => !(r.ContractType == (int)EnumContractType.AddItem && r.ContractAmount == 0 && r.FCContractAmount == 0))
                .Select((r, ca, cr) => new Db_crm_contract
                {
                    Id = r.Id.SelectAll()
                }).ToList();
        }
        /// <summary>
        /// 根据合同Id获取赠送产品信息
        /// </summary>
        public List<ProductInfo_Out> GetFreeContractProductInfoByContractId(string contractId, bool dataOwner = false)
        {
            return Db.Queryable<Db_v_productserviceinfo_free>()
                .LeftJoin<Db_crm_contract>((r, c) => r.ContractId == c.Id && c.Deleted == false)
                .LeftJoin<Db_crm_product>((r, c, p) => r.ProductId == p.Id && p.Deleted == false)
                .LeftJoin<Db_v_customer_subcompany_private_user>((r, c, p, cspu) => cspu.Id == c.FirstParty)
                //.LeftJoin<Db_sys_product_month_year>((r, c, pmy) => r.OpeningMonths == pmy.Month && pmy.Deleted == false)
                .Where((r, c, p, cspu) => r.ContractId == contractId)
                .WhereIF(dataOwner, DataOwner.BusinessData("c.Issuer", "r.ContractId", "cspu.CompanyCurrentUser"))
                .Select((r, c, p, cspu) => new ProductInfo_Out
                {
                    Id = r.Id,
                    ProductId = Guid.Empty.ToString(),
                    ProductName = p.ProductName,
                    ProductType = r.ProductType,
                    ContractId = r.ContractId,
                    ProductPrice = "0",
                    ContractProductinfoPrice = 0,
                    ContractProductinfoPriceTotal = 0,
                    OpeningMonths = r.ServiceMonth,
                    FirstOpeningMonths = r.ServiceMonth,
                    //  ???
                    OpeningYears = 0,
                    ParentProductId = Guid.Empty.ToString(),
                    ParentProductPriceId = Guid.Empty.ToString(),
                    ServiceCycle = 0,
                    ServiceStartDate = r.ServiceCycleStart,
                    ServiceEndDate = r.ServiceCycleEnd,
                    ProductServiceInfoApplState = r.ApplState,
                    ServiceState = r.ServiceState,
                    OpenServiceCycle = (r.ServiceCycleStart == null || r.ServiceCycleEnd == null) ? "--" : r.ServiceCycleStart.Value.ToString("yyyy-MM-dd") + "至" + r.ServiceCycleEnd.Value.ToString("yyyy-MM-dd")
                }).ToList();
        }

        public List<string> FindAddItemContract(string id)
        {
            return Db.Queryable<Db_crm_contract>().Where(c => c.Deleted == false)
                     .Where(c => c.ParentContractId == id).Select(c => c.Id.ToString()).ToList();
        }

        /// <summary>
        /// 根据合同Id获取相同客户下的所有通过合同的客户编码
        /// </summary>
        /// <param name="contractId"></param>
        /// <returns></returns>
        public string[] GetAllSameCustomerContractNumByContractId(string contractId)
        {
            return Queryable
                .LeftJoin<Db_crm_contract>((target, f) => target.CustomerId == f.CustomerId && f.Deleted == false)
                .Where((target, f) => f.Id == contractId)
                .Where((target, f) => target.ContractStatus == (int)EnumContractStatus.Pass || target.ContractStatus == (int)EnumContractStatus.AutoPass)
                .Where(target => target.Deleted == false)
                .Where(target => !string.IsNullOrEmpty(target.ContractNum))
                .Select(target => target.ContractNum)
                .Distinct()
                .ToArray();
        }
    }
}
