﻿using CRM2_API.BLL.Common;
using CRM2_API.BLL.TrackingRecord;
using CRM2_API.Common;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.BusinessModel.BM_GtisOpe;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Presentation;
using DocumentFormat.OpenXml.Wordprocessing;
using JiebaNet.Segmenter.Common;
using LgyUtil;
using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;
using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using System.Collections.Generic;
using System.Diagnostics.Contracts;
using System.IO;
using System.Threading.Tasks;
using Tea.Utils;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.BLLModel.Enum.MessageCenterEnumOption;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;
using static CRM2_API.Model.ControllersViewModel.VM_ContractInvoice;
using static CRM2_API.Model.ControllersViewModel.VM_ContractReceiptRegister;
using static CRM2_API.Model.ControllersViewModel.VM_Coupon;
using static CRM2_API.Model.ControllersViewModel.VM_MessageCenter;
using static CRM2_API.Model.ControllersViewModel.VM_SalesRules;
using static Lucene.Net.Util.Fst.Util;
using CRM2_API.DAL.DbCommon;

namespace CRM2_API.BLL
{
    public class BLL_ContractReceiptRegister : BaseBLL<BLL_ContractReceiptRegister>
    {
        private void Validating(List<ContractReceiptRegisterAchievement> achievement, List<ContractReceiptRegisterProjectInfoAchievement> projectInfoAchievement)
        {
            //根据产品类型验证产品输入项
            if (achievement == null && achievement.Count == 0)
            {
                throw new ApiException("产品不可为空");
            }
            //根据项目类型验证项目输入项
            if (projectInfoAchievement != null && projectInfoAchievement.Count > 0)
            {
                projectInfoAchievement.ForEach(p =>
                {
                    if (p.Type == EnumProjectinfoType.RefundItems.ToInt())
                    {
                        if (p.PerformanceDeduction > 0)
                        {
                            if (p.ItemsNum == null)
                            {
                                throw new ApiException("项目数不可为空");
                            }
                        }
                    }
                    if (p.Type == EnumProjectinfoType.Others.ToInt())
                    {
                        if (p.PerformanceDeduction > 0)
                        {
                            if (p.Price == null)
                            {
                                throw new ApiException("售价不可为空");
                            }
                            if (p.OtherItemsDetails == null || p.OtherItemsDetails.Count == 0)
                            {
                                throw new ApiException("其他项目明细不可为空");
                            }
                        }
                    }
                });
            }
            else
            {
                throw new ApiException("项目不可为空");
            }
        }

        /// <summary>
        /// 添加到账登记
        /// </summary>
        public string AddReceiptRegister(AddReceiptRegister_In addReceiptRegisterIn)
        {
            //验证输入项
            Validating(addReceiptRegisterIn.ReceiptRegisterAchievement, addReceiptRegisterIn.ReceiptRegisterProjectInfoAchievement);

            //充值金额验证提醒（非阻断式）
            decimal rechargeAmount = addReceiptRegisterIn.RechargeAmount ?? 0;
            if (rechargeAmount > 0 && rechargeAmount < 5000)
            {
                LogUtil.AddWarningLog($"充值金额提醒: 合同{addReceiptRegisterIn.ContractId}的充值金额为{rechargeAmount}元，小于5000元，请注意确认。");
            }
            Guid ContractReceiptRegisterId = Guid.Empty;
            DbOpe_crm_contract_receiptregister.Instance.TransDeal(() =>
            {
                List<string> CollectionInfoIds = addReceiptRegisterIn.ReceiptDetails.Select(r => r.CollectionInfoId.ToString()).ToList();

                //验证到账信息是否已经删除
                List<Db_crm_collectioninfo> IsCollectionInfo = DbOpe_crm_collectioninfo.Instance.GetDataList(r => CollectionInfoIds.Contains(r.Id)).ToList();
                if (CollectionInfoIds.Count > IsCollectionInfo.Count)
                {
                    throw new ApiException("当前到账信息已经被删除,不可以登记");
                }

                //验证到账信息是否已经登记过，待登记状态的不算登记过
                bool IsRegistered = DbOpe_crm_contract_receipt_details.Instance.IsRegisteredByCollectionInfoId(CollectionInfoIds);
                if (IsRegistered)
                {
                    throw new ApiException("当前到账信息已经登记过,不可以重复登记");
                }

                ////验证是否存在自动匹配,并且自动匹配的合同和当前所选合同不一致,如果存在进行拆分
                //List<Db_crm_contract_collectioninfo_automatching> automatching = DbOpe_crm_contract_collectioninfo_automatching.Instance.GetMatchedToBeConfirmedByUnContractIdAndCollectionInfoId(addReceiptRegisterIn.ContractId.ToString(), addReceiptRegisterIn.ContractPaymentInfoId.ToString(), CollectionInfoIds);
                //if (automatching.Count > 0)
                //{
                //    foreach (Db_crm_contract_collectioninfo_automatching am in automatching)
                //    {
                //        SplitMatchingRegisteredById(am.Id);
                //    }
                //}

                //到账金额
                decimal ArrivalAmount = DbOpe_crm_collectioninfo.Instance.GetArrivalAmountByIds(CollectionInfoIds);

                //获取项目扣除
                decimal RefundItems = addReceiptRegisterIn.ReceiptRegisterProjectInfoAchievement.Where(r => r.Type == EnumProjectinfoType.RefundItems.ToInt()).Sum(r => r.PerformanceDeduction);
                decimal ReturnPerformance = addReceiptRegisterIn.ReceiptRegisterProjectInfoAchievement.Where(r => r.Type == EnumProjectinfoType.ReturnPerformance.ToInt()).Sum(r => r.PerformanceDeduction);
                decimal Others = addReceiptRegisterIn.ReceiptRegisterProjectInfoAchievement.Where(r => r.Type == EnumProjectinfoType.Others.ToInt()).Sum(r => r.PerformanceDeduction);

                //获取产品扣除
                decimal DBPerformanceDeduction = addReceiptRegisterIn.ReceiptRegisterAchievement.Where(r => r.ProductType == EnumProductType.DandB.ToInt()).Sum(r => r.PerformanceDeduction);
                decimal GlobalSearchPerformanceDeduction = addReceiptRegisterIn.ReceiptRegisterAchievement.Where(r => r.ProductType == EnumProductType.Global.ToInt()).Sum(r => r.PerformanceDeduction);
                decimal OtherPerformanceDeduction = addReceiptRegisterIn.ReceiptRegisterAchievement.Where(r => r.ProductType == EnumProductType.Other.ToInt()).Sum(r => r.PerformanceDeduction);

                ////销售业绩：到账金额-退还项目+返还业绩
                //decimal SalesPerformance = ArrivalAmount - RefundItems + ReturnPerformance;
                ////扣除业绩：自动计算签约产品中扣除业绩之和,返还业绩做减法
                //decimal DeductPerformance = addReceiptRegisterIn.ReceiptRegisterAchievement.Sum(r=>r.PerformanceDeduction) + addReceiptRegisterIn.ReceiptRegisterProjectInfoAchievement.Sum(r=>r.PerformanceDeduction) - 2 * addReceiptRegisterIn.ReceiptRegisterProjectInfoAchievement.Where(r=> r.Type == EnumProjectinfoType.ReturnPerformance.ToInt()).Sum(r => r.PerformanceDeduction);
                ////有效业绩：销售业绩-邓白氏扣除-环球搜扣除-其他(其他扣除+其他项目)
                //decimal EffectivePerformance = SalesPerformance - DBPerformanceDeduction - GlobalSearchPerformanceDeduction - (Others + OtherPerformanceDeduction);

                //获取业绩规则系数
                Db_crm_contract contract = DbOpe_crm_contract.Instance.GetContractById(addReceiptRegisterIn.ContractId.ToString());
                Db_sys_comparam_achi comparam_achi = DbOpe_sys_comparam_achi.Instance.GetComparamAchisByUserId(contract.Issuer);
                //decimal ratio = 1;
                string ratio = "";
                if (comparam_achi != null)
                {
                    //ratio = comparam_achi.PerformanceCoefficient.Value;
                    ratio = comparam_achi.PerformanceCoefficient;
                }

                //新公式
                //销售业绩：到账金额 - 退还项目 - 不计入业绩金额
                decimal NonPerformanceAmount = addReceiptRegisterIn.NonPerformanceAmount ?? 0;
                decimal SalesPerformance = Math.Floor(ArrivalAmount - RefundItems - NonPerformanceAmount);
                //扣除业绩：签约产品扣除业绩之和
                decimal DeductPerformance = addReceiptRegisterIn.ReceiptRegisterAchievement.Sum(r => r.PerformanceDeduction);
                //有效业绩：销售业绩 - 扣除业绩 - 其他项目 + 返还业绩
                decimal EffectivePerformanceBase = Math.Floor((SalesPerformance - DeductPerformance - Others + ReturnPerformance));//* ratio;  //20240705取消系数相乘//20240704调整销售业绩基础值，两数值对调

                decimal EffectivePerformance = Math.Floor(SalesPerformance - DeductPerformance - Others + ReturnPerformance);
                //decimal EffectivePerformanceRatio = ratio;
                string EffectivePerformanceRatio = ratio;

                //获取用户信息
                var user = DbOpe_sys_user.Instance.GetDbSysUserById(contract.Issuer);
                //获取组织信息树，从当前组织追溯出所有上级组织
                var orgList = DbOpe_sys_organization.Instance.GetParentOrgList(user.OrganizationId);
                string? OrgDivisionId = null;
                string OrgDivisionName = "";
                string? OrgBrigadeId = null;
                string OrgBrigadeName = "";
                string? OrgRegimentId = null;
                string OrgRegimentName = "";
                foreach (var org in orgList)
                {
                    switch (org.OrgType)
                    {
                        case EnumOrgType.BattleTeam:
                            OrgDivisionId = org.Id;
                            OrgDivisionName = org.OrgName;
                            break;
                        case EnumOrgType.Battalion:
                            OrgBrigadeId = org.Id;
                            OrgBrigadeName = org.OrgName;
                            break;
                        case EnumOrgType.Squadron:
                            OrgRegimentId = org.Id;
                            OrgRegimentName = org.OrgName;
                            break;
                    }
                }

                ContractReceiptRegisterId = DbOpe_crm_contract_receiptregister.Instance.AddReceiptRegister(addReceiptRegisterIn, SalesPerformance, DeductPerformance, EffectivePerformance, EffectivePerformanceBase, EffectivePerformanceRatio, contract.Issuer, OrgDivisionId, OrgDivisionName, OrgBrigadeId, OrgBrigadeName, OrgRegimentId, OrgRegimentName);
                //添加到账信息
                DbOpe_crm_contract_receipt_details.Instance.AddContractReceiptDetails(addReceiptRegisterIn.ReceiptDetails, addReceiptRegisterIn.ContractId.ToString(), ContractReceiptRegisterId.ToString());
                //添加产品扣除业绩

                //格式化组合产品信息
                List<ContractReceiptRegisterAchievement> AchievementList = new List<ContractReceiptRegisterAchievement>();
                int CombinationProductType = EnumProductType.Combination.ToInt();
                AchievementList = addReceiptRegisterIn.ReceiptRegisterAchievement.Where(r => r.ProductType != CombinationProductType && r.IsSuperSubAccount == false).ToList();
                List<ContractReceiptRegisterAchievement> SubProductInfo = new List<ContractReceiptRegisterAchievement>();
                List<ContractReceiptRegisterAchievement> CombinationList = addReceiptRegisterIn.ReceiptRegisterAchievement.Where(r => r.ProductType == CombinationProductType).ToList();
                foreach (ContractReceiptRegisterAchievement item in CombinationList)
                {
                    foreach (ContractReceiptRegisterAchievement sub in item.ChildNode)
                    {
                        SubProductInfo.Add(sub);
                    }
                }
                AchievementList.AddRange(SubProductInfo);
                DbOpe_crm_contract_receiptregister_achievement.Instance.AddContractReceiptRegisterAchievement(AchievementList, addReceiptRegisterIn.ContractId.ToString(), ContractReceiptRegisterId.ToString());
                //DbOpe_crm_contract_receiptregister_achievement.Instance.AddContractReceiptRegisterAchievement(addReceiptRegisterIn.ReceiptRegisterAchievement, addReceiptRegisterIn.ContractId.ToString(), ContractReceiptRegisterId.ToString());
                List<ContractReceiptRegisterAchievement> SuperSubAccountAchievementList = new List<ContractReceiptRegisterAchievement>();
                SuperSubAccountAchievementList = addReceiptRegisterIn.ReceiptRegisterAchievement.Where(r => r.IsSuperSubAccount == true).ToList();
                DbOpe_crm_contract_receiptregister_supersubaccount_achievement.Instance.AddContractReceiptRegisterSuperSubAccountAchievement(SuperSubAccountAchievementList, addReceiptRegisterIn.ContractId.ToString(), ContractReceiptRegisterId.ToString());

                //添加项目扣除业绩
                foreach (ContractReceiptRegisterProjectInfoAchievement projectInfoAchievement in addReceiptRegisterIn.ReceiptRegisterProjectInfoAchievement)
                {
                    Guid ContractProjectInfoId = DbOpe_crm_contract_projectinfo.Instance.AddContractProjectInfo(projectInfoAchievement, addReceiptRegisterIn.ContractId.ToString());
                    if (projectInfoAchievement.Type == EnumProjectinfoType.Others.ToInt() && projectInfoAchievement.OtherItemsDetails != null)
                    {
                        DbOpe_crm_contract_projectinfo_otheritemsdetails.Instance.AddContractProjectInfoOtherItemsDetails(projectInfoAchievement.OtherItemsDetails, ContractProjectInfoId.ToString());
                    }
                    DbOpe_crm_contract_receiptregister_projectinfo_achievement.Instance.AddContractReceiptRegisterProjectInfoAchievement(addReceiptRegisterIn.ContractId.ToString(), ContractReceiptRegisterId.ToString(), ContractProjectInfoId.ToString(), projectInfoAchievement.PerformanceDeduction);
                }

                //添加合同到账登记恭喜确认
                DbOpe_crm_contract_receiptregister_congratulations.Instance.AddContractReceiptRegisterCongratulations(addReceiptRegisterIn.ReceiptRegisterCongratulations, addReceiptRegisterIn.ContractId.ToString(), ContractReceiptRegisterId.ToString());

                //添加客户优惠券
                if (addReceiptRegisterIn.ReceiptRegisterCoupons != null && addReceiptRegisterIn.ReceiptRegisterCoupons.Count() > 0)
                {
                    foreach (var item in addReceiptRegisterIn.ReceiptRegisterCoupons)
                    {
                        if (item.YearNum.IsNotNull() && item.CouponCount.IsNotNull())
                        {
                            AddCouponByContractReceipt_In Coupon = new AddCouponByContractReceipt_In();
                            Coupon.ContractReceiptRegisterId = ContractReceiptRegisterId.ToString();
                            Coupon.CompanyId = contract.FirstParty;
                            Coupon.YearNum = item.YearNum.Value;
                            Coupon.CouponCount = item.CouponCount.Value;
                            Coupon.Deadline = item.Deadline;
                            Coupon.Remark = item.Remark;
                            Coupon.CollectioninfoId = CollectionInfoIds[0];
                            Coupon.ContractId = contract.Id;
                            BLL_Coupon.Instance.AddCouponByContractReceipt(Coupon);
                        }
                    }
                }

                ////获取合同id相同银行到账id不同的匹配信息（已匹配待确认）
                //List<Db_crm_contract_collectioninfo_automatching> UnCollectionInfo = DbOpe_crm_contract_collectioninfo_automatching.Instance.GetMatchedToBeConfirmedByContractIdAndUnCollectionInfoId(addReceiptRegisterIn.ContractId.ToString(), addReceiptRegisterIn.ContractPaymentInfoId.ToString(), CollectionInfoIds);
                ////获取合同id不同银行到账id相同的匹配信息（已匹配待确认）
                //List<Db_crm_contract_collectioninfo_automatching> UnContract = DbOpe_crm_contract_collectioninfo_automatching.Instance.GetMatchedToBeConfirmedByUnContractIdAndCollectionInfoId(addReceiptRegisterIn.ContractId.ToString(), addReceiptRegisterIn.ContractPaymentInfoId.ToString(), CollectionInfoIds);

                ////更新取消登记的回款信息状态，变为待登记，自动匹配的变为已否认
                //if (UnCollectionInfo.Count > 0)
                //{
                //    List<string> ToBeMatchedCollectionInfoIds = UnCollectionInfo.Select(r => r.CollectionInfoId).ToList();
                //    DbOpe_crm_contract_collectioninfo_automatching.Instance.DenyMatchingRegisteredByContractIdAndCollectionInfoId(addReceiptRegisterIn.ContractId.ToString(), addReceiptRegisterIn.ContractPaymentInfoId.ToString(), ToBeMatchedCollectionInfoIds);
                //    DbOpe_crm_collectioninfo.Instance.ToBeMatchedCollectioninfoByCollectionInfoId(ToBeMatchedCollectionInfoIds);
                //}
                //if (UnContract.Count > 0)
                //{
                //    List<string> ToBeMatchedCollectionInfoIds = UnContract.Select(r => r.CollectionInfoId).ToList();
                //    foreach (Db_crm_contract_collectioninfo_automatching unc in UnContract)
                //    {
                //        DbOpe_crm_contract_collectioninfo_automatching.Instance.DenyMatchingRegisteredByContractIdAndCollectionInfoId(unc.ContractId.ToString(), unc.ContractPaymentInfoId.ToString(), ToBeMatchedCollectionInfoIds);
                //    }
                //    DbOpe_crm_collectioninfo.Instance.ToBeMatchedCollectioninfoByCollectionInfoId(ToBeMatchedCollectionInfoIds);
                //}

                if (addReceiptRegisterIn.CollectionInfoAutoMatchingId.IsNotNullOrEmpty())
                {
                    Db_crm_contract_collectioninfo_automatching automatched = DbOpe_crm_contract_collectioninfo_automatching.Instance.GetDataById(addReceiptRegisterIn.CollectionInfoAutoMatchingId);
                    if (automatched.ContractId != addReceiptRegisterIn.ContractId.ToString() || automatched.ContractPaymentInfoId != addReceiptRegisterIn.ContractPaymentInfoId.ToString())
                    {
                        DbOpe_crm_contract_collectioninfo_automatching.Instance.DenyMatchingRegisteredById(addReceiptRegisterIn.CollectionInfoAutoMatchingId);
                        DbOpe_crm_collectioninfo.Instance.ToBeMatchedCollectioninfoById(automatched.CollectionInfoId);
                        ////获取合同id不同银行到账id相同的匹配信息（已匹配待确认）
                        //List<Db_crm_contract_collectioninfo_automatching> UnContract = DbOpe_crm_contract_collectioninfo_automatching.Instance.GetMatchedToBeConfirmedByUnContractIdAndCollectionInfoId(addReceiptRegisterIn.ContractId.ToString(), addReceiptRegisterIn.ContractPaymentInfoId.ToString(), CollectionInfoIds);
                        //if (UnContract.Count > 0)
                        //{
                        //    List<string> ToBeMatchedCollectionInfoIds = UnContract.Select(r => r.CollectionInfoId).ToList();
                        //    foreach (Db_crm_contract_collectioninfo_automatching unc in UnContract)
                        //    {
                        //        DbOpe_crm_contract_collectioninfo_automatching.Instance.DenyMatchingRegisteredByContractIdAndCollectionInfoId(unc.ContractId.ToString(), unc.ContractPaymentInfoId.ToString(), ToBeMatchedCollectionInfoIds);
                        //    }
                        //    DbOpe_crm_collectioninfo.Instance.ToBeMatchedCollectioninfoByCollectionInfoId(ToBeMatchedCollectionInfoIds);
                        //}
                        ////获取合同id相同银行到账id不同的匹配信息（已匹配待确认）
                        //List<Db_crm_contract_collectioninfo_automatching> UnCollectionInfo = DbOpe_crm_contract_collectioninfo_automatching.Instance.GetMatchedToBeConfirmedByContractIdAndUnCollectionInfoId(addReceiptRegisterIn.ContractId.ToString(), addReceiptRegisterIn.ContractPaymentInfoId.ToString(), CollectionInfoIds);
                        ////更新取消登记的回款信息状态，变为待登记，自动匹配的变为已否认
                        //if (UnCollectionInfo.Count > 0)
                        //{
                        //    List<string> ToBeMatchedCollectionInfoIds = UnCollectionInfo.Select(r => r.CollectionInfoId).ToList();
                        //    DbOpe_crm_contract_collectioninfo_automatching.Instance.DenyMatchingRegisteredByContractIdAndCollectionInfoId(addReceiptRegisterIn.ContractId.ToString(), addReceiptRegisterIn.ContractPaymentInfoId.ToString(), ToBeMatchedCollectionInfoIds);
                        //    DbOpe_crm_collectioninfo.Instance.ToBeMatchedCollectioninfoByCollectionInfoId(ToBeMatchedCollectionInfoIds);
                        //}
                    }
                    if (automatched.CollectionInfoId != CollectionInfoIds.First())
                    {
                        DbOpe_crm_contract_collectioninfo_automatching.Instance.DenyMatchingRegisteredById(addReceiptRegisterIn.CollectionInfoAutoMatchingId);
                        DbOpe_crm_collectioninfo.Instance.ToBeMatchedCollectioninfoById(automatched.CollectionInfoId);
                        //获取合同id不同银行到账id相同的匹配信息（已匹配待确认）
                        List<Db_crm_contract_collectioninfo_automatching> UnContract = DbOpe_crm_contract_collectioninfo_automatching.Instance.GetMatchedToBeConfirmedByUnContractIdAndCollectionInfoId(addReceiptRegisterIn.ContractId.ToString(), addReceiptRegisterIn.ContractPaymentInfoId.ToString(), CollectionInfoIds);
                        if (UnContract.Count > 0)
                        {
                            List<string> ToBeMatchedCollectionInfoIds = UnContract.Select(r => r.CollectionInfoId).ToList();
                            foreach (Db_crm_contract_collectioninfo_automatching unc in UnContract)
                            {
                                DbOpe_crm_contract_collectioninfo_automatching.Instance.DenyMatchingRegisteredByContractIdAndCollectionInfoId(unc.ContractId.ToString(), unc.ContractPaymentInfoId.ToString(), ToBeMatchedCollectionInfoIds);
                            }
                            DbOpe_crm_collectioninfo.Instance.ToBeMatchedCollectioninfoByCollectionInfoId(ToBeMatchedCollectionInfoIds);
                        }
                    }
                }
                else
                {
                    List<Db_crm_contract_collectioninfo_automatching> automatchingList = DbOpe_crm_contract_collectioninfo_automatching.Instance.GetMatchedToBeConfirmedByCollectionInfoId(CollectionInfoIds);
                    foreach (Db_crm_contract_collectioninfo_automatching automatched in automatchingList)
                    {
                        DbOpe_crm_contract_collectioninfo_automatching.Instance.DenyMatchingRegisteredById(automatched.Id);
                        DbOpe_crm_collectioninfo.Instance.ToBeMatchedCollectioninfoById(automatched.CollectionInfoId);
                    }
                }


                //更新匹配待登记信息
                DbOpe_crm_contract_collectioninfo_automatching.Instance.ConfirmMatchingRegisteredByContractIdAndCollectionInfoId(addReceiptRegisterIn.ContractId.ToString(), addReceiptRegisterIn.ContractPaymentInfoId.ToString(), CollectionInfoIds);
                //更新回款信息状态
                DbOpe_crm_collectioninfo.Instance.RegisteredCollectioninfoByCollectionInfoId(CollectionInfoIds);

                //更新合同催单状态
                //Db_crm_contract contract = DbOpe_crm_contract.Instance.GetContractById(addReceiptRegisterIn.ContractId.ToString());
                if (contract.IsUrgeRegistration == EnumUrgeRegistration.Reminded.ToInt())
                {
                    int NoReminder = EnumUrgeRegistration.NoReminder.ToInt();
                    DbOpe_crm_contract.Instance.UpdateUrgeRegistration(addReceiptRegisterIn.ContractId.ToString(), NoReminder);
                }

                //提交审核
                DbOpe_crm_contract_receiptregister_audit.Instance.AddReceiptRegisterAudit(ContractReceiptRegisterId.ToString(), UserId);

                //流程
                //Db_crm_contract contract = DbOpe_crm_contract.Instance.GetContractById(addReceiptRegisterIn.ContractId.ToString());
                Db_crm_contract_receiptregister receiptregister = DbOpe_crm_contract_receiptregister.Instance.GetDataById(ContractReceiptRegisterId.ToString());

                //更新到账登记的创建时间为匹配的时间
                int Confirmed = EnumAutoMatchingState.Confirmed.ToInt();
                Db_crm_contract_collectioninfo_automatching automatching = DbOpe_crm_contract_collectioninfo_automatching.Instance.GetData(r => r.State == Confirmed && r.CollectionInfoId == CollectionInfoIds.First());
                if (automatching != null)
                {
                    DbOpe_crm_contract_receiptregister.Instance.UpdateData(r => new Db_crm_contract_receiptregister { CreateDate = automatching.CreateDate }, ContractReceiptRegisterId.ToString());
                }

                int state = DbOpe_crm_contract_receiptregister.Instance.GetReceiptRegisterStateById(ContractReceiptRegisterId.ToString()).State.Value;
                string dataState = Dictionary.RegisterState.First(e => e.Value == state.ToInt().ToString()).Name;
                BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_receiptregister, Db_crm_contract>("到账业绩审核流程", ContractReceiptRegisterId.ToString(), receiptregister, contract, addReceiptRegisterIn.Remark, dataState, "新建");

            });
            return ContractReceiptRegisterId.ToString();
        }

        /// <summary>
        /// 修改已确认的到账登记
        /// </summary>
        public void UpdateConfirmedReceiptRegister(UpdateReceiptRegister_In updateReceiptRegisterIn)
        {
            //验证输入项
            Validating(updateReceiptRegisterIn.ReceiptRegisterAchievement.Cast<ContractReceiptRegisterAchievement>().ToList(), updateReceiptRegisterIn.ReceiptRegisterProjectInfoAchievement.Cast<ContractReceiptRegisterProjectInfoAchievement>().ToList());

            Db_crm_contract_receiptregister receiptregister = DbOpe_crm_contract_receiptregister.Instance.GetDataById(updateReceiptRegisterIn.Id);
            //状态为待登记、拒绝、有疑问的数据才可以被修改
            if (receiptregister.AchievementState != EnumAchievementState.Confirmed.ToInt())
            {
                throw new ApiException("当前状态无法修改到账登记信息");
            }

            DbOpe_crm_contract_receiptregister.Instance.TransDeal(() =>
            {
                Db_crm_contract_receiptregister rr = DbOpe_crm_contract_receiptregister.Instance.GetDataById(updateReceiptRegisterIn.Id);
                List<string> CollectionInfoIds = updateReceiptRegisterIn.ReceiptDetails.Select(r => r.CollectionInfoId.ToString()).ToList();

                //验证到账信息是否已经删除
                List<Db_crm_collectioninfo> IsCollectionInfo = DbOpe_crm_collectioninfo.Instance.GetDataList(r => CollectionInfoIds.Contains(r.Id)).ToList();
                if (CollectionInfoIds.Count > IsCollectionInfo.Count)
                {
                    throw new ApiException("当前到账信息已经被删除,不可以登记");
                }

                //验证到账信息是否已经登记过，待登记状态的不算登记过
                bool IsRegistered = DbOpe_crm_contract_receipt_details.Instance.IsRegisteredByCollectionInfoId(CollectionInfoIds, updateReceiptRegisterIn.Id);
                if (IsRegistered)
                {
                    throw new ApiException("当前到账信息已经登记过,不可以重复登记");
                }

                ////验证是否存在自动匹配,并且自动匹配的合同和当前所选合同不一致,如果存在进行拆分
                //List<Db_crm_contract_collectioninfo_automatching> automatching = DbOpe_crm_contract_collectioninfo_automatching.Instance.GetMatchedToBeConfirmedByUnContractIdAndCollectionInfoId(addReceiptRegisterIn.ContractId.ToString(), addReceiptRegisterIn.ContractPaymentInfoId.ToString(), CollectionInfoIds);
                //if (automatching.Count > 0)
                //{
                //    foreach (Db_crm_contract_collectioninfo_automatching am in automatching)
                //    {
                //        SplitMatchingRegisteredById(am.Id);
                //    }
                //}

                //到账金额
                decimal ArrivalAmount = DbOpe_crm_collectioninfo.Instance.GetArrivalAmountByIds(CollectionInfoIds);

                //获取项目扣除
                decimal RefundItems = updateReceiptRegisterIn.ReceiptRegisterProjectInfoAchievement.Where(r => r.Type == EnumProjectinfoType.RefundItems.ToInt()).Sum(r => r.PerformanceDeduction);
                decimal ReturnPerformance = updateReceiptRegisterIn.ReceiptRegisterProjectInfoAchievement.Where(r => r.Type == EnumProjectinfoType.ReturnPerformance.ToInt()).Sum(r => r.PerformanceDeduction);
                decimal Others = updateReceiptRegisterIn.ReceiptRegisterProjectInfoAchievement.Where(r => r.Type == EnumProjectinfoType.Others.ToInt()).Sum(r => r.PerformanceDeduction);

                //获取产品扣除
                decimal DBPerformanceDeduction = updateReceiptRegisterIn.ReceiptRegisterAchievement.Where(r => r.ProductType == EnumProductType.DandB.ToInt()).Sum(r => r.PerformanceDeduction);
                decimal GlobalSearchPerformanceDeduction = updateReceiptRegisterIn.ReceiptRegisterAchievement.Where(r => r.ProductType == EnumProductType.Global.ToInt()).Sum(r => r.PerformanceDeduction);
                decimal OtherPerformanceDeduction = updateReceiptRegisterIn.ReceiptRegisterAchievement.Where(r => r.ProductType == EnumProductType.Other.ToInt()).Sum(r => r.PerformanceDeduction);

                ////销售业绩：到账金额-退还项目+返还业绩
                //decimal SalesPerformance = ArrivalAmount - RefundItems + ReturnPerformance;
                ////扣除业绩：自动计算签约产品中扣除业绩之和,返还业绩做减法
                //decimal DeductPerformance = updateReceiptRegisterIn.ReceiptRegisterAchievement.Sum(r => r.PerformanceDeduction) + updateReceiptRegisterIn.ReceiptRegisterProjectInfoAchievement.Sum(r => r.PerformanceDeduction) - 2 * updateReceiptRegisterIn.ReceiptRegisterProjectInfoAchievement.Where(r => r.Type == EnumProjectinfoType.ReturnPerformance.ToInt()).Sum(r => r.PerformanceDeduction);
                ////有效业绩：销售业绩-邓白氏扣除-环球搜扣除-其他(其他扣除+其他项目)
                //decimal EffectivePerformance = SalesPerformance - DBPerformanceDeduction - GlobalSearchPerformanceDeduction - (Others + OtherPerformanceDeduction);

                //获取业绩规则系数
                Db_crm_contract contract = DbOpe_crm_contract.Instance.GetContractById(updateReceiptRegisterIn.ContractId.ToString());
                Db_sys_comparam_achi comparam_achi = DbOpe_sys_comparam_achi.Instance.GetComparamAchisByUserId(contract.Issuer);
                //decimal ratio = 1;
                string ratio = "";
                if (comparam_achi != null)
                {
                    //ratio = comparam_achi.PerformanceCoefficient.Value;
                    ratio = comparam_achi.PerformanceCoefficient;
                }

                //新公式
                //销售业绩：到账金额 - 退还项目 - 不计入业绩金额
                decimal NonPerformanceAmount = updateReceiptRegisterIn.NonPerformanceAmount ?? 0;
                decimal SalesPerformance = Math.Floor(ArrivalAmount - RefundItems - NonPerformanceAmount);
                //扣除业绩：签约产品扣除业绩之和
                decimal DeductPerformance = updateReceiptRegisterIn.ReceiptRegisterAchievement.Sum(r => r.PerformanceDeduction);
                //有效业绩：销售业绩 - 扣除业绩 - 其他项目 + 返还业绩
                decimal EffectivePerformanceBase = Math.Floor((SalesPerformance - DeductPerformance - Others + ReturnPerformance));// * ratio; 20240705 取消系数相乘//20240704调整销售业绩基础值，两数值对调

                decimal EffectivePerformance = Math.Floor(SalesPerformance - DeductPerformance - Others + ReturnPerformance);
                //decimal EffectivePerformanceRatio = ratio;
                string EffectivePerformanceRatio = ratio;

                //获取已保存的回款信息id
                List<string> HistoryCollectionInfoIds = DbOpe_crm_contract_receipt_details.Instance.GetCollectionInfoIdListByContractReceiptRegisterId(updateReceiptRegisterIn.Id);

                //获取用户信息
                var user = DbOpe_sys_user.Instance.GetDbSysUserById(contract.Issuer);
                //获取组织信息树，从当前组织追溯出所有上级组织
                var orgList = DbOpe_sys_organization.Instance.GetParentOrgList(user.OrganizationId);
                string? OrgDivisionId = null;
                string OrgDivisionName = "";
                string? OrgBrigadeId = null;
                string OrgBrigadeName = "";
                string? OrgRegimentId = null;
                string OrgRegimentName = "";
                foreach (var org in orgList)
                {
                    switch (org.OrgType)
                    {
                        case EnumOrgType.BattleTeam:
                            OrgDivisionId = org.Id;
                            OrgDivisionName = org.OrgName;
                            break;
                        case EnumOrgType.Battalion:
                            OrgBrigadeId = org.Id;
                            OrgBrigadeName = org.OrgName;
                            break;
                        case EnumOrgType.Squadron:
                            OrgRegimentId = org.Id;
                            OrgRegimentName = org.OrgName;
                            break;
                    }
                }
                int Congratulations = rr.Congratulations;
                DateTime? CongratulationsDate = rr.CongratulationsDate;

                DbOpe_crm_contract_receiptregister.Instance.UpdateReceiptRegister(updateReceiptRegisterIn, SalesPerformance, DeductPerformance, EffectivePerformance, EffectivePerformanceBase, EffectivePerformanceRatio, contract.Issuer, OrgDivisionId, OrgDivisionName, OrgBrigadeId, OrgBrigadeName, OrgRegimentId, OrgRegimentName, Congratulations, CongratulationsDate);
                //解绑到账匹配的发票
                BLL_ContractInvoiceNew.Instance.UnbindInvoiceReceipt(updateReceiptRegisterIn.Id, "因修改到账登记，解除该登记信息和发票的绑定关系");

                //修改到账信息
                DbOpe_crm_contract_receipt_details.Instance.UpdateContractReceiptDetails(updateReceiptRegisterIn.ReceiptDetails, updateReceiptRegisterIn.ContractId.ToString(), updateReceiptRegisterIn.Id);
                //修改产品扣除业绩

                //格式化组合产品信息
                List<UpdateContractReceiptRegisterAchievement> AchievementList = new List<UpdateContractReceiptRegisterAchievement>();
                int CombinationProductType = EnumProductType.Combination.ToInt();
                AchievementList = updateReceiptRegisterIn.ReceiptRegisterAchievement.Where(r => r.ProductType != CombinationProductType && r.IsSuperSubAccount == false).ToList();
                List<UpdateContractReceiptRegisterAchievement> SubProductInfo = new List<UpdateContractReceiptRegisterAchievement>();
                List<UpdateContractReceiptRegisterAchievement> CombinationList = updateReceiptRegisterIn.ReceiptRegisterAchievement.Where(r => r.ProductType == CombinationProductType).ToList();
                foreach (UpdateContractReceiptRegisterAchievement item in CombinationList)
                {
                    foreach (UpdateContractReceiptRegisterAchievement sub in item.ChildNode)
                    {
                        SubProductInfo.Add(sub);
                    }
                }
                AchievementList.AddRange(SubProductInfo);
                DbOpe_crm_contract_receiptregister_achievement.Instance.UpdateContractReceiptRegisterAchievement(AchievementList, updateReceiptRegisterIn.ContractId.ToString(), updateReceiptRegisterIn.Id);

                //DbOpe_crm_contract_receiptregister_achievement.Instance.UpdateContractReceiptRegisterAchievement(updateReceiptRegisterIn.ReceiptRegisterAchievement, updateReceiptRegisterIn.ContractId.ToString(), updateReceiptRegisterIn.Id);
                List<UpdateContractReceiptRegisterAchievement> SuperSubAccountAchievementList = new List<UpdateContractReceiptRegisterAchievement>();
                SuperSubAccountAchievementList = updateReceiptRegisterIn.ReceiptRegisterAchievement.Where(r => r.IsSuperSubAccount == true).ToList();
                DbOpe_crm_contract_receiptregister_supersubaccount_achievement.Instance.UpdateContractReceiptRegisterSuperSubAccountAchievement(SuperSubAccountAchievementList, updateReceiptRegisterIn.ContractId.ToString(), updateReceiptRegisterIn.Id);

                //修改项目扣除业绩
                foreach (UpdateContractReceiptRegisterProjectInfoAchievement projectInfoAchievement in updateReceiptRegisterIn.ReceiptRegisterProjectInfoAchievement)
                {
                    DbOpe_crm_contract_projectinfo.Instance.UpdateContractProjectInfo(projectInfoAchievement, updateReceiptRegisterIn.ContractId.ToString());
                    if (projectInfoAchievement.Type == EnumProjectinfoType.Others.ToInt())
                    {
                        DbOpe_crm_contract_projectinfo_otheritemsdetails.Instance.UpdateContractProjectInfoOtherItemsDetails(projectInfoAchievement.OtherItemsDetails, projectInfoAchievement.ContractProjectInfoId);
                    }
                    DbOpe_crm_contract_receiptregister_projectinfo_achievement.Instance.UpdateContractReceiptRegisterProjectInfoAchievement(projectInfoAchievement.Id, updateReceiptRegisterIn.ContractId.ToString(), updateReceiptRegisterIn.Id, projectInfoAchievement.ContractProjectInfoId, projectInfoAchievement.PerformanceDeduction);
                }

                //修改合同到账登记恭喜确认
                DbOpe_crm_contract_receiptregister_congratulations.Instance.UpdateContractReceiptRegisterCongratulations(updateReceiptRegisterIn.ReceiptRegisterCongratulations, updateReceiptRegisterIn.ContractId.ToString(), updateReceiptRegisterIn.Id);

                //添加客户优惠券
                if (updateReceiptRegisterIn.ReceiptRegisterCoupons != null && updateReceiptRegisterIn.ReceiptRegisterCoupons.Count() > 0)
                {
                    foreach (var item in updateReceiptRegisterIn.ReceiptRegisterCoupons)
                    {
                        if (item.YearNum.IsNotNull() && item.CouponCount.IsNotNull())
                        {
                            UpdateCouponByContractReceipt_In Coupon = new UpdateCouponByContractReceipt_In();
                            Coupon.Id = item.Id;
                            Coupon.ContractReceiptRegisterId = updateReceiptRegisterIn.Id;
                            Coupon.CompanyId = contract.FirstParty;
                            Coupon.YearNum = item.YearNum.Value;
                            Coupon.CouponCount = item.CouponCount.Value;
                            Coupon.Deadline = item.Deadline;
                            Coupon.Remark = item.Remark;
                            Coupon.CollectioninfoId = CollectionInfoIds[0];
                            Coupon.ContractId = contract.Id;
                            BLL_Coupon.Instance.UpdateCouponByContractReceipt(Coupon);
                        }
                    }
                }

                //更新审核历史状态
                DbOpe_crm_contract_receiptregister_audit.Instance.UpdateIsHistoryByContractReceiptRegisterId(updateReceiptRegisterIn.Id);
                DbOpe_crm_contract_receiptregister_achievement_audit.Instance.UpdateIsHistoryByContractReceiptRegisterId(updateReceiptRegisterIn.Id);

                List<string> ToBeMatchedCollectionInfoIds = new List<string>();
                foreach (string i in HistoryCollectionInfoIds)
                {
                    if (!CollectionInfoIds.Contains(i))
                    {
                        ToBeMatchedCollectionInfoIds.Add(i);
                    }
                }

                //更新取消登记的回款信息状态，变为待登记，自动匹配的变为已否认
                if (ToBeMatchedCollectionInfoIds.Count > 0)
                {
                    DbOpe_crm_contract_collectioninfo_automatching.Instance.DenyMatchingRegisteredByContractIdAndCollectionInfoId(updateReceiptRegisterIn.ContractId.ToString(), updateReceiptRegisterIn.ContractPaymentInfoId.ToString(), ToBeMatchedCollectionInfoIds);
                    DbOpe_crm_collectioninfo.Instance.ToBeMatchedCollectioninfoByCollectionInfoId(ToBeMatchedCollectionInfoIds);
                }

                ////获取合同id相同银行到账id不同的匹配信息（已匹配待确认）
                //List<Db_crm_contract_collectioninfo_automatching> UnCollectionInfo = DbOpe_crm_contract_collectioninfo_automatching.Instance.GetMatchedToBeConfirmedByContractIdAndUnCollectionInfoId(updateReceiptRegisterIn.ContractId.ToString(), updateReceiptRegisterIn.ContractPaymentInfoId.ToString(), CollectionInfoIds);
                ////获取合同id不同银行到账id相同的匹配信息（已匹配待确认）
                //List<Db_crm_contract_collectioninfo_automatching> UnContract = DbOpe_crm_contract_collectioninfo_automatching.Instance.GetMatchedToBeConfirmedByUnContractIdAndCollectionInfoId(updateReceiptRegisterIn.ContractId.ToString(), updateReceiptRegisterIn.ContractPaymentInfoId.ToString(), CollectionInfoIds);

                ////更新取消登记的回款信息状态，变为待登记，自动匹配的变为已否认
                //if (UnCollectionInfo.Count > 0)
                //{
                //    List<string> UpdateToBeMatchedCollectionInfoIds = UnCollectionInfo.Select(r => r.CollectionInfoId).ToList();
                //    DbOpe_crm_contract_collectioninfo_automatching.Instance.DenyMatchingRegisteredByContractIdAndCollectionInfoId(updateReceiptRegisterIn.ContractId.ToString(), updateReceiptRegisterIn.ContractPaymentInfoId.ToString(), UpdateToBeMatchedCollectionInfoIds);
                //    DbOpe_crm_collectioninfo.Instance.ToBeMatchedCollectioninfoByCollectionInfoId(ToBeMatchedCollectionInfoIds);
                //}
                //if (UnContract.Count > 0)
                //{
                //    List<string> UpdateToBeMatchedCollectionInfoIds = UnContract.Select(r => r.CollectionInfoId).ToList();
                //    foreach (Db_crm_contract_collectioninfo_automatching unc in UnContract)
                //    {
                //        DbOpe_crm_contract_collectioninfo_automatching.Instance.DenyMatchingRegisteredByContractIdAndCollectionInfoId(unc.ContractId.ToString(), unc.ContractPaymentInfoId.ToString(), UpdateToBeMatchedCollectionInfoIds);
                //    }
                //    DbOpe_crm_collectioninfo.Instance.ToBeMatchedCollectioninfoByCollectionInfoId(ToBeMatchedCollectionInfoIds);
                //}

                if (updateReceiptRegisterIn.CollectionInfoAutoMatchingId.IsNotNullOrEmpty())
                {
                    Db_crm_contract_collectioninfo_automatching automatched = DbOpe_crm_contract_collectioninfo_automatching.Instance.GetDataById(updateReceiptRegisterIn.CollectionInfoAutoMatchingId);
                    if (automatched.ContractId != updateReceiptRegisterIn.ContractId.ToString() || automatched.ContractPaymentInfoId != updateReceiptRegisterIn.ContractPaymentInfoId.ToString())
                    {
                        DbOpe_crm_contract_collectioninfo_automatching.Instance.DenyMatchingRegisteredById(updateReceiptRegisterIn.CollectionInfoAutoMatchingId);
                        DbOpe_crm_collectioninfo.Instance.ToBeMatchedCollectioninfoById(automatched.CollectionInfoId);
                        ////获取合同id不同银行到账id相同的匹配信息（已匹配待确认）
                        //List<Db_crm_contract_collectioninfo_automatching> UnContract = DbOpe_crm_contract_collectioninfo_automatching.Instance.GetMatchedToBeConfirmedByUnContractIdAndCollectionInfoId(updateReceiptRegisterIn.ContractId.ToString(), updateReceiptRegisterIn.ContractPaymentInfoId.ToString(), CollectionInfoIds);
                        //if (UnContract.Count > 0)
                        //{
                        //    List<string> MatchedCollectionInfoIds = UnContract.Select(r => r.CollectionInfoId).ToList();
                        //    foreach (Db_crm_contract_collectioninfo_automatching unc in UnContract)
                        //    {
                        //        DbOpe_crm_contract_collectioninfo_automatching.Instance.DenyMatchingRegisteredByContractIdAndCollectionInfoId(unc.ContractId.ToString(), unc.ContractPaymentInfoId.ToString(), MatchedCollectionInfoIds);
                        //    }
                        //    DbOpe_crm_collectioninfo.Instance.ToBeMatchedCollectioninfoByCollectionInfoId(MatchedCollectionInfoIds);
                        //}
                        ////获取合同id相同银行到账id不同的匹配信息（已匹配待确认）
                        //List<Db_crm_contract_collectioninfo_automatching> UnCollectionInfo = DbOpe_crm_contract_collectioninfo_automatching.Instance.GetMatchedToBeConfirmedByContractIdAndUnCollectionInfoId(addReceiptRegisterIn.ContractId.ToString(), addReceiptRegisterIn.ContractPaymentInfoId.ToString(), CollectionInfoIds);
                        ////更新取消登记的回款信息状态，变为待登记，自动匹配的变为已否认
                        //if (UnCollectionInfo.Count > 0)
                        //{
                        //    List<string> ToBeMatchedCollectionInfoIds = UnCollectionInfo.Select(r => r.CollectionInfoId).ToList();
                        //    DbOpe_crm_contract_collectioninfo_automatching.Instance.DenyMatchingRegisteredByContractIdAndCollectionInfoId(addReceiptRegisterIn.ContractId.ToString(), addReceiptRegisterIn.ContractPaymentInfoId.ToString(), ToBeMatchedCollectionInfoIds);
                        //    DbOpe_crm_collectioninfo.Instance.ToBeMatchedCollectioninfoByCollectionInfoId(ToBeMatchedCollectionInfoIds);
                        //}
                    }
                    if (automatched.CollectionInfoId != CollectionInfoIds.First())
                    {
                        DbOpe_crm_contract_collectioninfo_automatching.Instance.DenyMatchingRegisteredById(updateReceiptRegisterIn.CollectionInfoAutoMatchingId);
                        DbOpe_crm_collectioninfo.Instance.ToBeMatchedCollectioninfoById(automatched.CollectionInfoId);
                        //获取合同id不同银行到账id相同的匹配信息（已匹配待确认）
                        List<Db_crm_contract_collectioninfo_automatching> UnContract = DbOpe_crm_contract_collectioninfo_automatching.Instance.GetMatchedToBeConfirmedByUnContractIdAndCollectionInfoId(updateReceiptRegisterIn.ContractId.ToString(), updateReceiptRegisterIn.ContractPaymentInfoId.ToString(), CollectionInfoIds);
                        if (UnContract.Count > 0)
                        {
                            List<string> AToBeMatchedCollectionInfoIds = UnContract.Select(r => r.CollectionInfoId).ToList();
                            foreach (Db_crm_contract_collectioninfo_automatching unc in UnContract)
                            {
                                DbOpe_crm_contract_collectioninfo_automatching.Instance.DenyMatchingRegisteredByContractIdAndCollectionInfoId(unc.ContractId.ToString(), unc.ContractPaymentInfoId.ToString(), AToBeMatchedCollectionInfoIds);
                            }
                            DbOpe_crm_collectioninfo.Instance.ToBeMatchedCollectioninfoByCollectionInfoId(AToBeMatchedCollectionInfoIds);
                        }
                    }
                }
                else
                {
                    //将银行到账的状态和银行匹配的状态变更为初始状态
                    List<Db_crm_contract_collectioninfo_automatching> automatchingList = DbOpe_crm_contract_collectioninfo_automatching.Instance.GetMatchedToBeConfirmedByCollectionInfoId(CollectionInfoIds);
                    foreach (Db_crm_contract_collectioninfo_automatching automatched in automatchingList)
                    {
                        DbOpe_crm_contract_collectioninfo_automatching.Instance.DenyMatchingRegisteredById(automatched.Id);
                        DbOpe_crm_collectioninfo.Instance.ToBeMatchedCollectioninfoById(automatched.CollectionInfoId);
                    }
                }

                //更新匹配待登记信息
                DbOpe_crm_contract_collectioninfo_automatching.Instance.ConfirmMatchingRegisteredConfirmedByContractIdAndCollectionInfoId(updateReceiptRegisterIn.ContractId.ToString(), updateReceiptRegisterIn.ContractPaymentInfoId.ToString(), CollectionInfoIds);
                //更新回款信息状态
                DbOpe_crm_collectioninfo.Instance.RegisteredCollectioninfoByCollectionInfoId(CollectionInfoIds);

                //更新合同催单状态
                //Db_crm_contract contract = DbOpe_crm_contract.Instance.GetContractById(updateReceiptRegisterIn.ContractId.ToString());
                if (contract.IsUrgeRegistration == EnumUrgeRegistration.Reminded.ToInt())
                {
                    int NoReminder = EnumUrgeRegistration.NoReminder.ToInt();
                    DbOpe_crm_contract.Instance.UpdateUrgeRegistration(updateReceiptRegisterIn.ContractId.ToString(), NoReminder);
                }

                //提交审核
                DbOpe_crm_contract_receiptregister_audit.Instance.AddReceiptRegisterAudit(updateReceiptRegisterIn.Id, UserId);
                //更换绑定合同时，解绑到账和服务的绑定关系
                if (rr.ContractId != updateReceiptRegisterIn.ContractId.ToString())
                {
                    DbOpe_crm_contract_receiptregister_service.Instance.DeleteReturnServiceLinkDataByReceiptRegisterId(rr.Id);
                }

                //更新跟踪记录  撤销跟踪记录
                //Db_crm_contract contract = DbOpe_crm_contract.Instance.GetContractById(receiptregister.ContractId, true);
                //BLL_TrackingRecord.Instance.InternalAddOrModifyTrackingRecord(contract.CustomerId,contract.FirstParty,contract.Id,contract.ContractName,EnumTrackingStage.ConfirmDemand, 2, false, true);
                BLL_TrackingRecord.Instance.InternalUseForContractRollback(contract.CustomerId, contract.FirstParty, contract.Id, EnumTrackingStage.Received, true, updateReceiptRegisterIn.Id);

                //流程
                //Db_crm_contract contract = DbOpe_crm_contract.Instance.GetContractById(updateReceiptRegisterIn.ContractId.ToString());
                Db_crm_contract_receiptregister receiptregister = DbOpe_crm_contract_receiptregister.Instance.GetDataById(updateReceiptRegisterIn.Id.ToString());




                int state = DbOpe_crm_contract_receiptregister.Instance.GetReceiptRegisterStateById(updateReceiptRegisterIn.Id.ToString()).State.Value;
                string dataState = Dictionary.RegisterState.First(e => e.Value == state.ToInt().ToString()).Name;
                BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_receiptregister, Db_crm_contract>("到账业绩审核流程", updateReceiptRegisterIn.Id.ToString(), receiptregister, contract, updateReceiptRegisterIn.Remark, dataState, "修改");
            });
        }

        /// <summary>
        /// 修改到账登记
        /// </summary>
        public void UpdateReceiptRegister(UpdateReceiptRegister_In updateReceiptRegisterIn)
        {
            //验证输入项
            Validating(updateReceiptRegisterIn.ReceiptRegisterAchievement.Cast<ContractReceiptRegisterAchievement>().ToList(), updateReceiptRegisterIn.ReceiptRegisterProjectInfoAchievement.Cast<ContractReceiptRegisterProjectInfoAchievement>().ToList());

            //充值金额验证提醒（非阻断式）
            decimal rechargeAmount = updateReceiptRegisterIn.RechargeAmount ?? 0;
            if (rechargeAmount > 0 && rechargeAmount < 5000)
            {
                LogUtil.AddWarningLog($"充值金额提醒: 合同{updateReceiptRegisterIn.ContractId}的充值金额为{rechargeAmount}元，小于5000元，请注意确认。");
            }

            Db_crm_contract_receiptregister receiptregister = DbOpe_crm_contract_receiptregister.Instance.GetDataById(updateReceiptRegisterIn.Id);
            //状态为待登记、拒绝、有疑问的数据才可以被修改
            if (receiptregister.State != EnumRegisterState.Refuse.ToInt() && receiptregister.AchievementState != EnumAchievementState.Questionable.ToInt() && receiptregister.State != EnumRegisterState.ToBeRegistered.ToInt())
            {
                throw new ApiException("当前状态无法修改到账登记信息");
            }

            DbOpe_crm_contract_receiptregister.Instance.TransDeal(() =>
            {
                Db_crm_contract_receiptregister rr = DbOpe_crm_contract_receiptregister.Instance.GetDataById(updateReceiptRegisterIn.Id);
                List<string> CollectionInfoIds = updateReceiptRegisterIn.ReceiptDetails.Select(r => r.CollectionInfoId.ToString()).ToList();

                //验证到账信息是否已经删除
                List<Db_crm_collectioninfo> IsCollectionInfo = DbOpe_crm_collectioninfo.Instance.GetDataList(r => CollectionInfoIds.Contains(r.Id)).ToList();
                if (CollectionInfoIds.Count > IsCollectionInfo.Count)
                {
                    throw new ApiException("当前到账信息已经被删除,不可以登记");
                }

                //验证到账信息是否已经登记过，待登记状态的不算登记过
                bool IsRegistered = DbOpe_crm_contract_receipt_details.Instance.IsRegisteredByCollectionInfoId(CollectionInfoIds, updateReceiptRegisterIn.Id);
                if (IsRegistered)
                {
                    throw new ApiException("当前到账信息已经登记过,不可以重复登记");
                }

                ////验证是否存在自动匹配,并且自动匹配的合同和当前所选合同不一致,如果存在进行拆分
                //List<Db_crm_contract_collectioninfo_automatching> automatching = DbOpe_crm_contract_collectioninfo_automatching.Instance.GetMatchedToBeConfirmedByUnContractIdAndCollectionInfoId(addReceiptRegisterIn.ContractId.ToString(), addReceiptRegisterIn.ContractPaymentInfoId.ToString(), CollectionInfoIds);
                //if (automatching.Count > 0)
                //{
                //    foreach (Db_crm_contract_collectioninfo_automatching am in automatching)
                //    {
                //        SplitMatchingRegisteredById(am.Id);
                //    }
                //}

                //到账金额
                decimal ArrivalAmount = DbOpe_crm_collectioninfo.Instance.GetArrivalAmountByIds(CollectionInfoIds);

                //获取项目扣除
                decimal RefundItems = updateReceiptRegisterIn.ReceiptRegisterProjectInfoAchievement.Where(r => r.Type == EnumProjectinfoType.RefundItems.ToInt()).Sum(r => r.PerformanceDeduction);
                decimal ReturnPerformance = updateReceiptRegisterIn.ReceiptRegisterProjectInfoAchievement.Where(r => r.Type == EnumProjectinfoType.ReturnPerformance.ToInt()).Sum(r => r.PerformanceDeduction);
                decimal Others = updateReceiptRegisterIn.ReceiptRegisterProjectInfoAchievement.Where(r => r.Type == EnumProjectinfoType.Others.ToInt()).Sum(r => r.PerformanceDeduction);

                //获取产品扣除
                decimal DBPerformanceDeduction = updateReceiptRegisterIn.ReceiptRegisterAchievement.Where(r => r.ProductType == EnumProductType.DandB.ToInt()).Sum(r => r.PerformanceDeduction);
                decimal GlobalSearchPerformanceDeduction = updateReceiptRegisterIn.ReceiptRegisterAchievement.Where(r => r.ProductType == EnumProductType.Global.ToInt()).Sum(r => r.PerformanceDeduction);
                decimal OtherPerformanceDeduction = updateReceiptRegisterIn.ReceiptRegisterAchievement.Where(r => r.ProductType == EnumProductType.Other.ToInt()).Sum(r => r.PerformanceDeduction);

                ////销售业绩：到账金额-退还项目+返还业绩
                //decimal SalesPerformance = ArrivalAmount - RefundItems + ReturnPerformance;
                ////扣除业绩：自动计算签约产品中扣除业绩之和,返还业绩做减法
                //decimal DeductPerformance = updateReceiptRegisterIn.ReceiptRegisterAchievement.Sum(r => r.PerformanceDeduction) + updateReceiptRegisterIn.ReceiptRegisterProjectInfoAchievement.Sum(r => r.PerformanceDeduction) - 2 * updateReceiptRegisterIn.ReceiptRegisterProjectInfoAchievement.Where(r => r.Type == EnumProjectinfoType.ReturnPerformance.ToInt()).Sum(r => r.PerformanceDeduction);
                ////有效业绩：销售业绩-邓白氏扣除-环球搜扣除-其他(其他扣除+其他项目)
                //decimal EffectivePerformance = SalesPerformance - DBPerformanceDeduction - GlobalSearchPerformanceDeduction - (Others + OtherPerformanceDeduction);

                //获取业绩规则系数
                Db_crm_contract contract = DbOpe_crm_contract.Instance.GetContractById(updateReceiptRegisterIn.ContractId.ToString());
                Db_sys_comparam_achi comparam_achi = DbOpe_sys_comparam_achi.Instance.GetComparamAchisByUserId(contract.Issuer);
                //decimal ratio = 1;
                string ratio = "";
                if (comparam_achi != null)
                {
                    //ratio = comparam_achi.PerformanceCoefficient.value;
                    ratio = comparam_achi.PerformanceCoefficient;
                }

                //新公式
                //销售业绩：到账金额 - 退还项目 - 不计入业绩金额
                decimal NonPerformanceAmount = updateReceiptRegisterIn.NonPerformanceAmount ?? 0;
                decimal SalesPerformance = Math.Floor(ArrivalAmount - RefundItems - NonPerformanceAmount);
                //扣除业绩：签约产品扣除业绩之和
                decimal DeductPerformance = updateReceiptRegisterIn.ReceiptRegisterAchievement.Sum(r => r.PerformanceDeduction);
                //有效业绩：销售业绩 - 扣除业绩 - 其他项目 + 返还业绩
                decimal EffectivePerformanceBase = Math.Floor((SalesPerformance - DeductPerformance - Others + ReturnPerformance)); //* ratio 20240705取消系数相乘 ;//20240704调整销售业绩基础值，两数值对调

                decimal EffectivePerformance = Math.Floor(SalesPerformance - DeductPerformance - Others + ReturnPerformance);
                //decimal EffectivePerformanceRatio = ratio;
                string EffectivePerformanceRatio = ratio;

                //获取已保存的回款信息id
                List<string> HistoryCollectionInfoIds = DbOpe_crm_contract_receipt_details.Instance.GetCollectionInfoIdListByContractReceiptRegisterId(updateReceiptRegisterIn.Id);

                //获取用户信息
                var user = DbOpe_sys_user.Instance.GetDbSysUserById(contract.Issuer);
                //获取组织信息树，从当前组织追溯出所有上级组织
                var orgList = DbOpe_sys_organization.Instance.GetParentOrgList(user.OrganizationId);
                string? OrgDivisionId = null;
                string OrgDivisionName = "";
                string? OrgBrigadeId = null;
                string OrgBrigadeName = "";
                string? OrgRegimentId = null;
                string OrgRegimentName = "";
                foreach (var org in orgList)
                {
                    switch (org.OrgType)
                    {
                        case EnumOrgType.BattleTeam:
                            OrgDivisionId = org.Id;
                            OrgDivisionName = org.OrgName;
                            break;
                        case EnumOrgType.Battalion:
                            OrgBrigadeId = org.Id;
                            OrgBrigadeName = org.OrgName;
                            break;
                        case EnumOrgType.Squadron:
                            OrgRegimentId = org.Id;
                            OrgRegimentName = org.OrgName;
                            break;
                    }
                }
                int Congratulations = rr.Congratulations;
                DateTime? CongratulationsDate = rr.CongratulationsDate;

                DbOpe_crm_contract_receiptregister.Instance.UpdateReceiptRegister(updateReceiptRegisterIn, SalesPerformance, DeductPerformance, EffectivePerformance, EffectivePerformanceBase, EffectivePerformanceRatio, contract.Issuer, OrgDivisionId, OrgDivisionName, OrgBrigadeId, OrgBrigadeName, OrgRegimentId, OrgRegimentName, Congratulations, CongratulationsDate);
                //修改到账信息
                DbOpe_crm_contract_receipt_details.Instance.UpdateContractReceiptDetails(updateReceiptRegisterIn.ReceiptDetails, updateReceiptRegisterIn.ContractId.ToString(), updateReceiptRegisterIn.Id);
                //修改产品扣除业绩

                //格式化组合产品信息
                List<UpdateContractReceiptRegisterAchievement> AchievementList = new List<UpdateContractReceiptRegisterAchievement>();
                int CombinationProductType = EnumProductType.Combination.ToInt();
                AchievementList = updateReceiptRegisterIn.ReceiptRegisterAchievement.Where(r => r.ProductType != CombinationProductType).ToList();
                List<UpdateContractReceiptRegisterAchievement> SubProductInfo = new List<UpdateContractReceiptRegisterAchievement>();
                List<UpdateContractReceiptRegisterAchievement> CombinationList = updateReceiptRegisterIn.ReceiptRegisterAchievement.Where(r => r.ProductType == CombinationProductType).ToList();
                foreach (UpdateContractReceiptRegisterAchievement item in CombinationList)
                {
                    foreach (UpdateContractReceiptRegisterAchievement sub in item.ChildNode)
                    {
                        SubProductInfo.Add(sub);
                    }
                }
                AchievementList.AddRange(SubProductInfo);
                DbOpe_crm_contract_receiptregister_achievement.Instance.UpdateContractReceiptRegisterAchievement(AchievementList, updateReceiptRegisterIn.ContractId.ToString(), updateReceiptRegisterIn.Id);

                //DbOpe_crm_contract_receiptregister_achievement.Instance.UpdateContractReceiptRegisterAchievement(updateReceiptRegisterIn.ReceiptRegisterAchievement, updateReceiptRegisterIn.ContractId.ToString(), updateReceiptRegisterIn.Id);
                //修改项目扣除业绩
                foreach (UpdateContractReceiptRegisterProjectInfoAchievement projectInfoAchievement in updateReceiptRegisterIn.ReceiptRegisterProjectInfoAchievement)
                {
                    DbOpe_crm_contract_projectinfo.Instance.UpdateContractProjectInfo(projectInfoAchievement, updateReceiptRegisterIn.ContractId.ToString());
                    if (projectInfoAchievement.Type == EnumProjectinfoType.Others.ToInt())
                    {
                        DbOpe_crm_contract_projectinfo_otheritemsdetails.Instance.UpdateContractProjectInfoOtherItemsDetails(projectInfoAchievement.OtherItemsDetails, projectInfoAchievement.ContractProjectInfoId);
                    }
                    DbOpe_crm_contract_receiptregister_projectinfo_achievement.Instance.UpdateContractReceiptRegisterProjectInfoAchievement(projectInfoAchievement.Id, updateReceiptRegisterIn.ContractId.ToString(), updateReceiptRegisterIn.Id, projectInfoAchievement.ContractProjectInfoId, projectInfoAchievement.PerformanceDeduction);
                }

                //修改合同到账登记恭喜确认
                DbOpe_crm_contract_receiptregister_congratulations.Instance.UpdateContractReceiptRegisterCongratulations(updateReceiptRegisterIn.ReceiptRegisterCongratulations, updateReceiptRegisterIn.ContractId.ToString(), updateReceiptRegisterIn.Id);

                //添加客户优惠券
                if (updateReceiptRegisterIn.ReceiptRegisterCoupons != null && updateReceiptRegisterIn.ReceiptRegisterCoupons.Count() > 0)
                {
                    foreach (var item in updateReceiptRegisterIn.ReceiptRegisterCoupons)
                    {
                        if (item.YearNum.IsNotNull() && item.CouponCount.IsNotNull())
                        {
                            UpdateCouponByContractReceipt_In Coupon = new UpdateCouponByContractReceipt_In();
                            Coupon.Id = item.Id;
                            Coupon.ContractReceiptRegisterId = updateReceiptRegisterIn.Id;
                            Coupon.CompanyId = contract.FirstParty;
                            Coupon.YearNum = item.YearNum.Value;
                            Coupon.CouponCount = item.CouponCount.Value;
                            Coupon.Deadline = item.Deadline;
                            Coupon.Remark = item.Remark;
                            Coupon.CollectioninfoId = CollectionInfoIds[0];
                            Coupon.ContractId = contract.Id;
                            BLL_Coupon.Instance.UpdateCouponByContractReceipt(Coupon);
                        }
                    }
                }

                //更新审核历史状态
                DbOpe_crm_contract_receiptregister_audit.Instance.UpdateIsHistoryByContractReceiptRegisterId(updateReceiptRegisterIn.Id);
                DbOpe_crm_contract_receiptregister_achievement_audit.Instance.UpdateIsHistoryByContractReceiptRegisterId(updateReceiptRegisterIn.Id);

                List<string> ToBeMatchedCollectionInfoIds = new List<string>();
                foreach (string i in HistoryCollectionInfoIds)
                {
                    if (!CollectionInfoIds.Contains(i))
                    {
                        ToBeMatchedCollectionInfoIds.Add(i);
                    }
                }

                //更新取消登记的回款信息状态，变为待登记，自动匹配的变为已否认
                if (ToBeMatchedCollectionInfoIds.Count > 0)
                {
                    DbOpe_crm_contract_collectioninfo_automatching.Instance.DenyMatchingRegisteredByContractIdAndCollectionInfoId(updateReceiptRegisterIn.ContractId.ToString(), updateReceiptRegisterIn.ContractPaymentInfoId.ToString(), ToBeMatchedCollectionInfoIds);
                    DbOpe_crm_collectioninfo.Instance.ToBeMatchedCollectioninfoByCollectionInfoId(ToBeMatchedCollectionInfoIds);
                }

                ////获取合同id相同银行到账id不同的匹配信息（已匹配待确认）
                //List<Db_crm_contract_collectioninfo_automatching> UnCollectionInfo = DbOpe_crm_contract_collectioninfo_automatching.Instance.GetMatchedToBeConfirmedByContractIdAndUnCollectionInfoId(updateReceiptRegisterIn.ContractId.ToString(), updateReceiptRegisterIn.ContractPaymentInfoId.ToString(), CollectionInfoIds);
                ////获取合同id不同银行到账id相同的匹配信息（已匹配待确认）
                //List<Db_crm_contract_collectioninfo_automatching> UnContract = DbOpe_crm_contract_collectioninfo_automatching.Instance.GetMatchedToBeConfirmedByUnContractIdAndCollectionInfoId(updateReceiptRegisterIn.ContractId.ToString(), updateReceiptRegisterIn.ContractPaymentInfoId.ToString(), CollectionInfoIds);

                ////更新取消登记的回款信息状态，变为待登记，自动匹配的变为已否认
                //if (UnCollectionInfo.Count > 0)
                //{
                //    List<string> UpdateToBeMatchedCollectionInfoIds = UnCollectionInfo.Select(r => r.CollectionInfoId).ToList();
                //    DbOpe_crm_contract_collectioninfo_automatching.Instance.DenyMatchingRegisteredByContractIdAndCollectionInfoId(updateReceiptRegisterIn.ContractId.ToString(), updateReceiptRegisterIn.ContractPaymentInfoId.ToString(), UpdateToBeMatchedCollectionInfoIds);
                //    DbOpe_crm_collectioninfo.Instance.ToBeMatchedCollectioninfoByCollectionInfoId(ToBeMatchedCollectionInfoIds);
                //}
                //if (UnContract.Count > 0)
                //{
                //    List<string> UpdateToBeMatchedCollectionInfoIds = UnContract.Select(r => r.CollectionInfoId).ToList();
                //    foreach (Db_crm_contract_collectioninfo_automatching unc in UnContract)
                //    {
                //        DbOpe_crm_contract_collectioninfo_automatching.Instance.DenyMatchingRegisteredByContractIdAndCollectionInfoId(unc.ContractId.ToString(), unc.ContractPaymentInfoId.ToString(), UpdateToBeMatchedCollectionInfoIds);
                //    }
                //    DbOpe_crm_collectioninfo.Instance.ToBeMatchedCollectioninfoByCollectionInfoId(ToBeMatchedCollectionInfoIds);
                //}

                if (updateReceiptRegisterIn.CollectionInfoAutoMatchingId.IsNotNullOrEmpty())
                {
                    Db_crm_contract_collectioninfo_automatching automatched = DbOpe_crm_contract_collectioninfo_automatching.Instance.GetDataById(updateReceiptRegisterIn.CollectionInfoAutoMatchingId);
                    if (automatched.ContractId != updateReceiptRegisterIn.ContractId.ToString() || automatched.ContractPaymentInfoId != updateReceiptRegisterIn.ContractPaymentInfoId.ToString())
                    {
                        DbOpe_crm_contract_collectioninfo_automatching.Instance.DenyMatchingRegisteredById(updateReceiptRegisterIn.CollectionInfoAutoMatchingId);
                        DbOpe_crm_collectioninfo.Instance.ToBeMatchedCollectioninfoById(automatched.CollectionInfoId);
                        ////获取合同id不同银行到账id相同的匹配信息（已匹配待确认）
                        //List<Db_crm_contract_collectioninfo_automatching> UnContract = DbOpe_crm_contract_collectioninfo_automatching.Instance.GetMatchedToBeConfirmedByUnContractIdAndCollectionInfoId(updateReceiptRegisterIn.ContractId.ToString(), updateReceiptRegisterIn.ContractPaymentInfoId.ToString(), CollectionInfoIds);
                        //if (UnContract.Count > 0)
                        //{
                        //    List<string> MatchedCollectionInfoIds = UnContract.Select(r => r.CollectionInfoId).ToList();
                        //    foreach (Db_crm_contract_collectioninfo_automatching unc in UnContract)
                        //    {
                        //        DbOpe_crm_contract_collectioninfo_automatching.Instance.DenyMatchingRegisteredByContractIdAndCollectionInfoId(unc.ContractId.ToString(), unc.ContractPaymentInfoId.ToString(), MatchedCollectionInfoIds);
                        //    }
                        //    DbOpe_crm_collectioninfo.Instance.ToBeMatchedCollectioninfoByCollectionInfoId(MatchedCollectionInfoIds);
                        //}
                        ////获取合同id相同银行到账id不同的匹配信息（已匹配待确认）
                        //List<Db_crm_contract_collectioninfo_automatching> UnCollectionInfo = DbOpe_crm_contract_collectioninfo_automatching.Instance.GetMatchedToBeConfirmedByContractIdAndUnCollectionInfoId(addReceiptRegisterIn.ContractId.ToString(), addReceiptRegisterIn.ContractPaymentInfoId.ToString(), CollectionInfoIds);
                        ////更新取消登记的回款信息状态，变为待登记，自动匹配的变为已否认
                        //if (UnCollectionInfo.Count > 0)
                        //{
                        //    List<string> ToBeMatchedCollectionInfoIds = UnCollectionInfo.Select(r => r.CollectionInfoId).ToList();
                        //    DbOpe_crm_contract_collectioninfo_automatching.Instance.DenyMatchingRegisteredByContractIdAndCollectionInfoId(addReceiptRegisterIn.ContractId.ToString(), addReceiptRegisterIn.ContractPaymentInfoId.ToString(), ToBeMatchedCollectionInfoIds);
                        //    DbOpe_crm_collectioninfo.Instance.ToBeMatchedCollectioninfoByCollectionInfoId(ToBeMatchedCollectionInfoIds);
                        //}
                    }
                    if (automatched.CollectionInfoId != CollectionInfoIds.First())
                    {
                        DbOpe_crm_contract_collectioninfo_automatching.Instance.DenyMatchingRegisteredById(updateReceiptRegisterIn.CollectionInfoAutoMatchingId);
                        DbOpe_crm_collectioninfo.Instance.ToBeMatchedCollectioninfoById(automatched.CollectionInfoId);
                        //获取合同id不同银行到账id相同的匹配信息（已匹配待确认）
                        List<Db_crm_contract_collectioninfo_automatching> UnContract = DbOpe_crm_contract_collectioninfo_automatching.Instance.GetMatchedToBeConfirmedByUnContractIdAndCollectionInfoId(updateReceiptRegisterIn.ContractId.ToString(), updateReceiptRegisterIn.ContractPaymentInfoId.ToString(), CollectionInfoIds);
                        if (UnContract.Count > 0)
                        {
                            List<string> AToBeMatchedCollectionInfoIds = UnContract.Select(r => r.CollectionInfoId).ToList();
                            foreach (Db_crm_contract_collectioninfo_automatching unc in UnContract)
                            {
                                DbOpe_crm_contract_collectioninfo_automatching.Instance.DenyMatchingRegisteredByContractIdAndCollectionInfoId(unc.ContractId.ToString(), unc.ContractPaymentInfoId.ToString(), AToBeMatchedCollectionInfoIds);
                            }
                            DbOpe_crm_collectioninfo.Instance.ToBeMatchedCollectioninfoByCollectionInfoId(AToBeMatchedCollectionInfoIds);
                        }
                    }
                }
                else
                {
                    List<Db_crm_contract_collectioninfo_automatching> automatchingList = DbOpe_crm_contract_collectioninfo_automatching.Instance.GetMatchedToBeConfirmedByCollectionInfoId(CollectionInfoIds);
                    foreach (Db_crm_contract_collectioninfo_automatching automatched in automatchingList)
                    {
                        DbOpe_crm_contract_collectioninfo_automatching.Instance.DenyMatchingRegisteredById(automatched.Id);
                        DbOpe_crm_collectioninfo.Instance.ToBeMatchedCollectioninfoById(automatched.CollectionInfoId);
                    }
                }

                //更新匹配待登记信息
                DbOpe_crm_contract_collectioninfo_automatching.Instance.ConfirmMatchingRegisteredByContractIdAndCollectionInfoId(updateReceiptRegisterIn.ContractId.ToString(), updateReceiptRegisterIn.ContractPaymentInfoId.ToString(), CollectionInfoIds);
                //更新回款信息状态
                DbOpe_crm_collectioninfo.Instance.RegisteredCollectioninfoByCollectionInfoId(CollectionInfoIds);

                //更新合同催单状态
                //Db_crm_contract contract = DbOpe_crm_contract.Instance.GetContractById(updateReceiptRegisterIn.ContractId.ToString());
                if (contract.IsUrgeRegistration == EnumUrgeRegistration.Reminded.ToInt())
                {
                    int NoReminder = EnumUrgeRegistration.NoReminder.ToInt();
                    DbOpe_crm_contract.Instance.UpdateUrgeRegistration(updateReceiptRegisterIn.ContractId.ToString(), NoReminder);
                }

                //提交审核
                DbOpe_crm_contract_receiptregister_audit.Instance.AddReceiptRegisterAudit(updateReceiptRegisterIn.Id, UserId);

                //流程
                //Db_crm_contract contract = DbOpe_crm_contract.Instance.GetContractById(updateReceiptRegisterIn.ContractId.ToString());
                Db_crm_contract_receiptregister receiptregister = DbOpe_crm_contract_receiptregister.Instance.GetDataById(updateReceiptRegisterIn.Id.ToString());

                int state = DbOpe_crm_contract_receiptregister.Instance.GetReceiptRegisterStateById(updateReceiptRegisterIn.Id.ToString()).State.Value;
                string dataState = Dictionary.RegisterState.First(e => e.Value == state.ToInt().ToString()).Name;
                BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_receiptregister, Db_crm_contract>("到账业绩审核流程", updateReceiptRegisterIn.Id.ToString(), receiptregister, contract, updateReceiptRegisterIn.Remark, dataState, "修改");
            });
        }

        /// <summary>
        /// 到账登记复核
        /// </summary>
        public void AuditContractReceiptRegister(AuditContractReceiptRegister_In auditContractReceiptRegisterIn)
        {
            DbOpe_crm_contract_receiptregister_audit.Instance.TransDeal(() =>
            {
                int Submit = EnumRegisterState.Submit.ToInt();
                Db_crm_contract_receiptregister_audit receiptregister_audit = DbOpe_crm_contract_receiptregister_audit.Instance.GetData(r => r.ContractReceiptRegisterId == auditContractReceiptRegisterIn.Id && r.State == Submit && r.IsHistory == false);
                if (receiptregister_audit == null)
                {
                    throw new ApiException("当前状态无法审核");
                }
                int State = EnumRegisterState.Pass.ToInt();

                //流程
                Db_crm_contract_receiptregister receiptregister = DbOpe_crm_contract_receiptregister.Instance.GetDataById(receiptregister_audit.ContractReceiptRegisterId);
                Db_crm_contract contract = DbOpe_crm_contract.Instance.GetContractById(receiptregister.ContractId);


                //复核通过则添加登记确认申请
                if (auditContractReceiptRegisterIn.State == EnumReceiptRegisterAudit.Pass.ToInt())
                {
                    if (auditContractReceiptRegisterIn.ProtectionDeadline == null)
                    {
                        throw new ApiException("保护截止日不可为空");
                    }
                    if (auditContractReceiptRegisterIn.IsSecret == null)
                    {
                        throw new ApiException("是否私密不可为空");
                    }
                    if (auditContractReceiptRegisterIn.CustomerType == null)
                    {
                        throw new ApiException("客户类型不可为空");
                    }
                    if (auditContractReceiptRegisterIn.IsSettlementExchange == null)
                    {
                        throw new ApiException("是否结汇不可为空");
                    }
                    if (auditContractReceiptRegisterIn.BelongingMonth == null)
                    {
                        throw new ApiException("归属月份不可为空");
                    }
                    if (auditContractReceiptRegisterIn.GlobalSearchUnit == null)
                    {
                        throw new ApiException("环球搜结算单位不可为空");
                    }

                    State = EnumRegisterState.Pass.ToInt();
                    DbOpe_crm_contract_receiptregister_achievement_audit.Instance.AddReceiptRegisterAchievementAudit(receiptregister_audit.ContractReceiptRegisterId, receiptregister_audit.Id, UserId);
                    //复核通过则修改到账登记表中保护截止日、是否私密、客户类型、到账备注、审核状态字段
                    DbOpe_crm_contract_receiptregister.Instance.UpdateData(r => new Db_crm_contract_receiptregister { State = State, ProtectionDeadline = auditContractReceiptRegisterIn.ProtectionDeadline, IsSecret = auditContractReceiptRegisterIn.IsSecret, CustomerType = auditContractReceiptRegisterIn.CustomerType, IsSettlementExchange = auditContractReceiptRegisterIn.IsSettlementExchange, Remark = auditContractReceiptRegisterIn.Remark, BelongingMonth = auditContractReceiptRegisterIn.BelongingMonth, GlobalSearchUnit = auditContractReceiptRegisterIn.GlobalSearchUnit }, receiptregister_audit.ContractReceiptRegisterId);

                    //修改合同到账登记恭喜确认
                    Db_crm_contract_receiptregister rr = DbOpe_crm_contract_receiptregister.Instance.GetDataById(receiptregister_audit.ContractReceiptRegisterId);
                    DbOpe_crm_contract_receiptregister_congratulations.Instance.UpdateContractReceiptRegisterCongratulations(auditContractReceiptRegisterIn.ReceiptRegisterCongratulations, rr.ContractId.ToString(), receiptregister_audit.ContractReceiptRegisterId);

                    //到账复核通过通知确认业绩

                    MessageMainInfo message = new MessageMainInfo();
                    message.Issuer = contract.Issuer;
                    message.MessageTypeToId = receiptregister_audit.ContractReceiptRegisterId;
                    message.MessagemMainAboutDes = contract.ContractName;
                    MessageGiveBack giveBack = BLL_MessageCenter.Instance.InsertMessageCenterDetail(message, EnumMessageLocalType.AchievementAbout, EnumMessageStepInfo.BeConfirmed, EnumMessageStateInfo.Pass);
                    BLL_MessageCenter.Instance.RealTimeSend(giveBack);

                }
                else
                {
                    if (auditContractReceiptRegisterIn.ReviewRemarks == "")
                    {
                        throw new ApiException("复核备注不可为空");
                    }

                    State = EnumRegisterState.Refuse.ToInt();
                    //复核拒绝则修改到账登记表中审核状态字段
                    DbOpe_crm_contract_receiptregister.Instance.UpdateData(r => new Db_crm_contract_receiptregister { State = State }, receiptregister_audit.ContractReceiptRegisterId);
                }
                DbOpe_crm_contract_receiptregister_audit.Instance.UpdateReceiptRegisterAudit(receiptregister_audit.Id, auditContractReceiptRegisterIn.AuditFeedback, auditContractReceiptRegisterIn.ReviewRemarks, State, UserId);

                receiptregister = DbOpe_crm_contract_receiptregister.Instance.GetDataById(receiptregister_audit.ContractReceiptRegisterId);

                //更新合同催单状态
                //Db_crm_contract contract = DbOpe_crm_contract.Instance.GetContractById(receiptregister.ContractId.ToString());
                if (contract.IsUrgeRegistration == EnumUrgeRegistration.Reminded.ToInt())
                {
                    int NoReminder = EnumUrgeRegistration.NoReminder.ToInt();
                    DbOpe_crm_contract.Instance.UpdateUrgeRegistration(receiptregister.ContractId.ToString(), NoReminder);
                }

                //判断合同的客户编码是否为空，如果为空则添加客户编码信息
                if (auditContractReceiptRegisterIn.State == EnumReceiptRegisterAudit.Pass.ToInt())
                {
                    if (contract.ContractNum.IsNullOrEmpty())
                    {
                        if (contract.ContractType != (int)EnumContractType.AddItem)
                        {
                            int Num = DbOpe_crm_contract.Instance.GetContractNumCount();
                            string ContractNum = (Num + 1).ToString().PadLeft(5, '0');
                            DbOpe_crm_contract.Instance.UpdateData(r => new Db_crm_contract { ContractNum = ContractNum }, contract.Id);
                        }
                        else
                        {
                            string OldContractNum = DbOpe_crm_contract.Instance.GetDataById(contract.ParentContractId).ContractNum;
                            DbOpe_crm_contract.Instance.UpdateData(r => new Db_crm_contract { ContractNum = OldContractNum }, contract.Id);
                        }

                    }
                }

                int state = DbOpe_crm_contract_receiptregister.Instance.GetReceiptRegisterStateById(auditContractReceiptRegisterIn.Id.ToString()).State.Value;
                string dataState = Dictionary.RegisterState.First(e => e.Value == state.ToInt().ToString()).Name;
                if (auditContractReceiptRegisterIn.State == EnumReceiptRegisterAudit.Pass.ToInt())
                {
                    BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_receiptregister, Db_crm_contract>("到账业绩审核流程", receiptregister_audit.ContractReceiptRegisterId, receiptregister, contract, contract.Issuer, auditContractReceiptRegisterIn.AuditFeedback, dataState, "审核");
                }
                else
                {
                    BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_receiptregister, Db_crm_contract>("到账业绩审核流程", receiptregister_audit.ContractReceiptRegisterId, receiptregister, contract, auditContractReceiptRegisterIn.AuditFeedback, dataState, "审核");
                }
            });
        }

        /// <summary>
        /// 锁定合同到账登记业绩审核信息
        /// </summary>
        public void LockContractReceiptRegisterAchievement(List<string> ids)
        {
            DbOpe_crm_contract_receiptregister.Instance.TransDeal(() =>
            {
                List<Db_crm_contract_receiptregister> list = DbOpe_crm_contract_receiptregister.Instance.GetUnConfirmedContractReceiptRegister(ids);
                if (list.Count() > 0)
                {
                    throw new ApiException("包含未确认到账登记信息,无法进行锁定操作");
                }

                DbOpe_crm_contract_receiptregister.Instance.UpdateContractReceiptRegisterLocked(ids);
            });
        }

        /// <summary>
        /// 解除锁定合同到账登记业绩审核信息
        /// </summary>
        public void UnLockContractReceiptRegisterAchievement(List<string> ids)
        {
            DbOpe_crm_contract_receiptregister.Instance.TransDeal(() =>
            {
                List<Db_crm_contract_receiptregister> list = DbOpe_crm_contract_receiptregister.Instance.GetUnLockedContractReceiptRegister(ids);
                if (list.Count() > 0)
                {
                    throw new ApiException("包含未锁定到账登记信息,无法进行解除锁定操作");
                }

                DbOpe_crm_contract_receiptregister.Instance.UpdateContractReceiptRegisterUnLocked(ids);
            });
        }

        /// <summary>
        /// 添加项目其他信息
        /// </summary>
        public Guid AddProjectInfoOtherItem(ProjectInfoOtherItem_In projectInfoOtherItemIn)
        {
            string name = projectInfoOtherItemIn.Name.Trim();
            List<Db_crm_projectinfo_otheritem> otheritemList = DbOpe_crm_projectinfo_otheritem.Instance.GetDataList(r => r.Name == name);
            if (otheritemList.Count() > 0)
            {
                throw new ApiException("当前名称已存在");
            }
            return DbOpe_crm_projectinfo_otheritem.Instance.InsertDataReturnId(projectInfoOtherItemIn);
        }

        /// <summary>
        /// 获取项目其他信息列表
        /// </summary>
        public List<ProjectInfoOtherItemList_Out> GetProjectInfoOtherItemList()
        {
            return DbOpe_crm_projectinfo_otheritem.Instance.GetDataAllList().Select<ProjectInfoOtherItemList_Out>().ToList();
        }

        /// <summary>
        /// 获取项目信息列表
        /// </summary>
        public List<ProjectInfoList_Out> GetProjectInfoList()
        {
            return DbOpe_crm_projectinfo.Instance.GetDataAllList().OrderBy(r => r.OrderNum).Select<ProjectInfoList_Out>().ToList();
        }

        /// <summary>
        /// 根据匹配待登记Id获取匹配待登记信息
        /// </summary>
        public MatchingRegistered_Out GetMatchingRegisteredById(string id)
        {
            return DbOpe_crm_contract_collectioninfo_automatching.Instance.GetMatchingRegisteredById(id);
        }

        /// <summary>
        /// 根据匹配待登记Id拆分匹配待登记信息
        /// </summary>
        public void SplitMatchingRegisteredById(SplitMatchingRegisteredIn splitMatchingRegisteredIn)
        {
            int ToBeMatched = EnumMatchingStatus.ToBeMatched.ToInt();
            Db_crm_contract_collectioninfo_automatching automatching = DbOpe_crm_contract_collectioninfo_automatching.Instance.GetDataById(splitMatchingRegisteredIn.ContractCollectioninfoAutoMatchingId);

            Db_crm_collectioninfo collectioninfo = DbOpe_crm_collectioninfo.Instance.GetDataById(automatching.CollectionInfoId);
            if (collectioninfo.MatchingStatus == EnumMatchingStatus.Registered.ToInt())
            {
                throw new ApiException("该回款信息已登记不可以拆分");
            }

            DbOpe_crm_contract_receiptregister.Instance.TransDeal(() =>
            {
                DbOpe_crm_collectioninfo.Instance.UpdateData(r => new Db_crm_collectioninfo { MatchingStatus = ToBeMatched, SplitDate = DateTime.Now, SplitRemark = splitMatchingRegisteredIn.SplitRemark }, automatching.CollectionInfoId);
                DbOpe_crm_contract_collectioninfo_automatching.Instance.SplitMatchingRegisteredById(splitMatchingRegisteredIn.ContractCollectioninfoAutoMatchingId, splitMatchingRegisteredIn.SplitRemark);
            });
        }

        /// <summary>
        /// 根据查询条件获取合同到账登记恭喜确认列表
        /// </summary>
        public ApiTableOut<SearchContractReceiptRegisterCongratulationsList_Out> SearchContractReceiptRegisterCongratulationsList(SearchContractReceiptRegisterCongratulationsList_In searchContractReceiptRegisterListIn)
        {
            int total = 0;
            return new ApiTableOut<SearchContractReceiptRegisterCongratulationsList_Out> { Data = DbOpe_crm_contract_receiptregister.Instance.SearchContractReceiptRegisterCongratulationsList(searchContractReceiptRegisterListIn, ref total), Total = total };
        }

        /// <summary>
        /// 根据查询条件获取合同到账登记列表
        /// </summary>
        public ApiTableOut<SearchContractReceiptRegisterList_Out> SearchContractReceiptRegisterList(SearchContractReceiptRegisterList_In searchContractReceiptRegisterListIn)
        {
            int total = 0;
            return new ApiTableOut<SearchContractReceiptRegisterList_Out> { Data = DbOpe_crm_contract_receiptregister.Instance.SearchContractReceiptRegisterList(searchContractReceiptRegisterListIn, ref total), Total = total };
        }
        /// <summary>
        /// 根据查询条件获取合同到账登记列表合计
        /// </summary>
        public SearchContractReceiptRegisterList_Sta_Out SearchContractReceiptRegisterSta(SearchContractReceiptRegisterList_In searchContractReceiptRegisterListIn)
        {
            return DbOpe_crm_contract_receiptregister.Instance.SearchContractReceiptRegisterSta(searchContractReceiptRegisterListIn);
        }

        /// <summary>
        /// 根据到账登记Id获取到账登记信息
        /// </summary>
        public ReceiptRegister_Out GetReceiptRegisterById(string id)
        {
            return DbOpe_crm_contract_receiptregister.Instance.GetReceiptRegisterById(id);
        }

        /// <summary>
        /// 撤销到账登记，当确认状态为待确认、待复核、被驳回时，可返回上一个状态，返回后可重新进行操作。
        /// </summary>
        //当确认状态为待确认、待复核、被驳回时，可返回上一个状态，返回后可重新进行操作
        public void RevokeReceiptRegister(string id)
        {
            Db_crm_contract_receiptregister receiptregister = DbOpe_crm_contract_receiptregister.Instance.GetDataById(id);
            if (receiptregister.State == EnumRegisterState.Pass.ToInt() || receiptregister.State == EnumRegisterState.Submit.ToInt() || receiptregister.State == EnumRegisterState.Refuse.ToInt())
            {
                DbOpe_crm_contract_receiptregister.Instance.TransDeal(() =>
                {
                    int registerState = EnumRegisterState.Submit.ToInt();
                    if (receiptregister.State == EnumRegisterState.Submit.ToInt())
                    {
                        //临时
                        List<FormInterfaceName> list = DbOpe_sys_form.Instance.GetFormIdsByUserId(UserId);
                        List<FormInterfaceName> addPermissionList = list.Where(r => r.ControllerName == "ContractReceiptRegister" && (r.MethodName == "AddReceiptRegister" || r.MethodName == "UpdateReceiptRegister")).ToList();
                        if (addPermissionList.Count() == 0)
                        {
                            throw new ApiException("该用户无法撤销到账登记状态为待复核的数据");
                        }
                        //临时

                        ////验证数据权限（到账登记创建者可撤销登记）
                        //if (receiptregister.CreateUser != UserId)
                        //{
                        //    throw new ApiException("该用户没有数据权限");
                        //}

                        registerState = EnumRegisterState.ToBeRegistered.ToInt();

                        //获取已保存的回款信息id
                        List<string> CollectionInfoIds = DbOpe_crm_contract_receipt_details.Instance.GetCollectionInfoIdListByContractReceiptRegisterId(receiptregister.Id);
                        ////20240430修改自动匹配的变为已否认，修改为任然保持匹配待登记，只是统一更成手动匹配
                        ////更新取消登记的回款信息状态，变为待登记，自动匹配的变为已否认
                        //if (CollectionInfoIds.Count > 0)
                        //{
                        //    DbOpe_crm_contract_receipt_details.Instance.DeleteContractReceiptDetailsByContractReceiptRegisterId(receiptregister.Id);
                        //    DbOpe_crm_contract_collectioninfo_automatching.Instance.DenyMatchingRegisteredByContractIdAndCollectionInfoId(receiptregister.ContractId.ToString(), receiptregister.ContractPaymentInfoId.ToString(), CollectionInfoIds);
                        //    DbOpe_crm_collectioninfo.Instance.ToBeMatchedCollectioninfoByCollectionInfoId(CollectionInfoIds);
                        //}
                        //更新取消登记的回款信息状态，修改为任然保持匹配待登记，只是统一更成手动匹配
                        if (CollectionInfoIds.Count > 0)
                        {
                            //DbOpe_crm_contract_receipt_details.Instance.DeleteContractReceiptDetailsByContractReceiptRegisterId(receiptregister.Id);
                            DbOpe_crm_contract_collectioninfo_automatching.Instance.MatchedToBeConfirmedRegisteredByContractIdAndCollectionInfoId(receiptregister.ContractId.ToString(), receiptregister.ContractPaymentInfoId.ToString(), CollectionInfoIds);
                            DbOpe_crm_collectioninfo.Instance.MatchingPendingRegistrationCollectioninfoByCollectionInfoId(CollectionInfoIds);//????
                            //DbOpe_crm_collectioninfo.Instance.RegisteredCollectioninfoByCollectionInfoId(CollectionInfoIds);
                        }

                        //撤销已发送的消息
                        string dataState = Dictionary.RegisterState.First(e => e.Value == receiptregister.State.ToInt().ToString()).Name;
                        Db_crm_contract_receiptregister dataObject = DbOpe_crm_contract_receiptregister.Instance.GetDataById(id);
                        BLL_WorkFlow.Instance.CancelWorkflowPending("到账业绩审核流程", id, dataState, dataObject);

                        //删除到账登记
                        DbOpe_crm_contract_receiptregister.Instance.DeleteData(id);
                        //删除到账登记明细
                        DbOpe_crm_contract_receipt_details.Instance.DeleteContractReceiptDetailsByContractReceiptRegisterId(id);
                        //删除到账登记产品
                        DbOpe_crm_contract_receiptregister_achievement.Instance.DeleteContractReceiptRegisterAchievementByContractReceiptRegisterId(id);
                        //删除到账登记产品超级子账号
                        DbOpe_crm_contract_receiptregister_supersubaccount_achievement.Instance.DeleteContractReceiptRegisterSuperSubAccountAchievementByContractReceiptRegisterId(id);
                        //删除到账登记确认审核
                        DbOpe_crm_contract_receiptregister_achievement_audit.Instance.DeleteContractReceiptRegisterAchievementAuditByContractReceiptRegisterId(id);
                        //删除到账登记审核
                        DbOpe_crm_contract_receiptregister_audit.Instance.DeleteReceiptRegisterAuditByContractReceiptRegisterId(id);
                        //删除到账登记项目
                        DbOpe_crm_contract_receiptregister_projectinfo_achievement.Instance.DeleteContractReceiptRegisterProjectInfoAchievementByContractReceiptRegisterId(id);
                        //该合同暂时不在到账登记中显示
                        DbOpe_crm_contract.Instance.UpdateIsNotShowReceiptRegister(receiptregister.ContractId, true);

                        DbOpe_crm_contract_receiptregister_congratulations.Instance.DeleteContractReceiptRegisterCongratulationsByContractReceiptRegisterId(id);

                        //撤销优惠券
                        DbOpe_crm_customer_coupon.Instance.RemoveCouponDataByRegId(id, CollectionInfoIds.Count > 0 ? CollectionInfoIds[0] : string.Empty, receiptregister.ContractId);
                    }
                    else if (receiptregister.State == EnumRegisterState.Pass.ToInt() || receiptregister.State == EnumRegisterState.Refuse.ToInt())
                    {
                        //临时
                        List<FormInterfaceName> list = DbOpe_sys_form.Instance.GetFormIdsByUserId(UserId);
                        List<FormInterfaceName> auditPermissionList = list.Where(r => r.ControllerName == "ContractReceiptRegister" && r.MethodName == "AuditContractReceiptRegister").ToList();
                        if (auditPermissionList.Count() == 0)
                        {
                            throw new ApiException("该用户无法撤销到账登记状态为待确认或被驳回的数据");
                        }
                        //临时

                        ////Db_crm_contract_receiptregister_audit audit = DbOpe_crm_contract_receiptregister_audit.Instance.GetData(r => r.ContractReceiptRegisterId == id && r.IsHistory == false);
                        //Db_crm_contract_receiptregister_audit audit = DbOpe_crm_contract_receiptregister_audit.Instance.GetReceiptRegisterAuditByContractReceiptRegisterId(id, true);
                        ////验证数据权限（合同所有者可撤销登记）
                        ////if (audit.ReviewerId != UserId)
                        ////{
                        ////    throw new ApiException("该用户没有数据权限");
                        ////}
                        ////验证数据权限（合同所有者可撤销登记）
                        //if (audit == null)
                        //{
                        //    throw new ApiException("该用户没有数据权限");
                        //}

                        registerState = EnumRegisterState.Submit.ToInt();
                        //撤销到账登记审核
                        DbOpe_crm_contract_receiptregister_audit.Instance.RevokeReceiptRegisterAuditByContractReceiptRegisterId(receiptregister.Id);

                        //如果是复核通过的需要将到账登记确认的申请撤回
                        if (receiptregister.State == EnumRegisterState.Pass.ToInt())
                        {
                            DbOpe_crm_contract_receiptregister_achievement_audit.Instance.UpdateIsHistoryByContractReceiptRegisterId(receiptregister.Id);

                            //更新跟踪记录  撤销跟踪记录
                            Db_crm_contract contract = DbOpe_crm_contract.Instance.GetContractById(receiptregister.ContractId, true);
                            //BLL_TrackingRecord.Instance.InternalAddOrModifyTrackingRecord(contract.CustomerId,contract.FirstParty,contract.Id,contract.ContractName,EnumTrackingStage.ConfirmDemand, 2, false, true);
                            BLL_TrackingRecord.Instance.InternalUseForContractRollback(contract.CustomerId,
                                contract.FirstParty, contract.Id, contract.Issuer,
                                EnumTrackingStage.Received, true, id);
                        }

                        //修改到账登记状态
                        DbOpe_crm_contract_receiptregister.Instance.UpdateReceiptRegisterStatus(id, registerState);

                        ////获取已保存的回款信息id 修改 2024年11月14日
                        //List<string> CollectionInfoIds = DbOpe_crm_contract_receipt_details.Instance.GetCollectionInfoIdListByContractReceiptRegisterId(receiptregister.Id);
                        ////撤销优惠券
                        //DbOpe_crm_customer_coupon.Instance.RemoveCouponDataByRegId(id, CollectionInfoIds.Count > 0 ? CollectionInfoIds[0] : string.Empty, receiptregister.ContractId);


                        //撤销已发送的消息
                        string dataState = Dictionary.RegisterState.First(e => e.Value == receiptregister.State.ToInt().ToString()).Name;
                        Db_crm_contract_receiptregister dataObject = DbOpe_crm_contract_receiptregister.Instance.GetDataById(id);
                        BLL_WorkFlow.Instance.CancelWorkflowPending("到账业绩审核流程", id, dataState, dataObject);
                    }

                    ////修改到账登记状态
                    //DbOpe_crm_contract_receiptregister.Instance.UpdateReceiptRegisterStatus(id, registerState);
                    ////撤销已发送的消息
                    //string dataState = Dictionary.RegisterState.First(e => e.Value == receiptregister.State.ToInt().ToString()).Name;
                    //Db_crm_contract_receiptregister dataObject = DbOpe_crm_contract_receiptregister.Instance.GetDataById(id);
                    //BLL_WorkFlow.Instance.CancelWorkflowPending("到账业绩审核流程", id, dataState, dataObject);
                });
            }
            else
            {
                throw new ApiException("当前到账登记状态下，不可以撤销");
            }
        }

        /// <summary>
        /// 根据合同id获取到账登记信息
        /// </summary>
        public ApiTableOut<GetReceiptRegisterByContractId_Out> GetReceiptRegisterByContractId(GetReceiptRegisterByContractId_In GetReceiptRegisterByContractIdIn)
        {
            Db_crm_contract result = DbOpe_crm_contract.Instance.GetContractById(GetReceiptRegisterByContractIdIn.ContractId, true);
            if (result == null)
            {
                throw new ApiException("该用户没有数据权限");
            }
            int total = 0;
            return new ApiTableOut<GetReceiptRegisterByContractId_Out> { Data = DbOpe_crm_contract_receiptregister.Instance.GetReceiptRegisterByContractId(GetReceiptRegisterByContractIdIn, ref total), Total = total };
        }

        /// <summary>
        /// 根据客户id和到账日期获取到账登记信息
        /// </summary>
        public ApiTableOut<GetReceiptRegisterByCustomerId_Out> GetReceiptRegisterByCustomerId(GetReceiptRegisterByCustomerId_In getReceiptRegisterByCustomerIdIn)
        {
            PrivateCustomerSimpleInfo privatepool = null;
            bool IsPrivate = DbOpe_crm_customer_privatepool.Instance.CheckInPool(getReceiptRegisterByCustomerIdIn.CustomerId, ref privatepool, UserId, false, false);
            bool IsPublic = DbOpe_crm_customer_publicpool.Instance.CheckInPool(getReceiptRegisterByCustomerIdIn.CustomerId, true, UserId, false, false);
            if (!IsPrivate && !IsPublic)
            {
                throw new ApiException("该用户没有数据权限");
            }
            int total = 0;
            return new ApiTableOut<GetReceiptRegisterByCustomerId_Out> { Data = DbOpe_crm_contract_receiptregister.Instance.GetReceiptRegisterByCustomerId(getReceiptRegisterByCustomerIdIn, ref total), Total = total };
        }

        /// <summary>
        /// 根据甲方公司和到账日期获取到账登记信息
        /// </summary>
        public ApiTableOut<GetReceiptRegisterByFirstParty_Out> GetReceiptRegisterByFirstParty(GetReceiptRegisterByFirstParty_In getReceiptRegisterByFirstPartyIn)
        {
            int total = 0;
            return new ApiTableOut<GetReceiptRegisterByFirstParty_Out> { Data = DbOpe_crm_contract_receiptregister.Instance.GetReceiptRegisterByFirstParty(getReceiptRegisterByFirstPartyIn, ref total), Total = total };
        }

        /// <summary>
        /// 根据合同id获取回款金额和退款信息
        /// </summary>
        public GetReceiptRegisterArrivalAmountInfoByContractId_Out GetReceiptRegisterArrivalAmountInfoByContractId(string contractId)
        {
            Db_crm_contract result = DbOpe_crm_contract.Instance.GetContractById(contractId, true);
            if (result == null)
            {
                throw new ApiException("该用户没有数据权限");
            }
            decimal ArrivalAmount = DbOpe_crm_contract_receiptregister.Instance.GetReceiptRegisterArrivalAmountByContractId(contractId);
            decimal RefundAmount = DbOpe_crm_contract_receiptregister.Instance.GetReceiptRegisterRefundAmountByContractId(contractId);
            return new GetReceiptRegisterArrivalAmountInfoByContractId_Out { ContractId = contractId, ArrivalAmount = ArrivalAmount, RefundAmount = RefundAmount };
        }

        /// <summary>
        /// 获取待锁定到账登记信息
        /// </summary>
        public List<GetTobeLockContractReceiptRegister_Out> GetTobeLockContractReceiptRegister(GetTobeLockContractReceiptRegister_In getTobeLockContractReceiptRegisterIn)
        {
            return DbOpe_crm_contract_receiptregister.Instance.GetTobeLockContractReceiptRegister(getTobeLockContractReceiptRegisterIn);
        }

        /// <summary>
        /// 获取已锁定到账登记信息
        /// </summary>
        public List<GetLockedContractReceiptRegister_Out> GetLockedContractReceiptRegister(GetLockedContractReceiptRegister_In getLockedContractReceiptRegisterIn)
        {
            return DbOpe_crm_contract_receiptregister.Instance.GetLockedContractReceiptRegister(getLockedContractReceiptRegisterIn);
        }

        /// <summary>
        /// 获取待确认到账登记信息
        /// </summary>
        public List<GetTobeConfirmedContractReceiptRegister_Out> GetTobeConfirmedContractReceiptRegister(GetTobeConfirmedContractReceiptRegister_In getTobeConfirmedContractReceiptRegisterIn)
        {
            return DbOpe_crm_contract_receiptregister.Instance.GetTobeConfirmedContractReceiptRegister(getTobeConfirmedContractReceiptRegisterIn, UserId);
        }

        /// <summary>
        /// 根据合同id获取已确认到账登记款项信息
        /// </summary>
        public List<GetConfirmedReceiptDetailsByContractId_Out> GetConfirmedReceiptDetailsByContractId(string contractId)
        {
            return DbOpe_crm_contract_receiptregister.Instance.GetConfirmedReceiptDetailsByContractId(contractId);
        }

        /// <summary>
        /// 删除到账登记，当状态为待登记可进行操作
        /// </summary>
        public void DeleteReceiptRegister(string id, int type)
        {
            if (type == 1 || type == 3)
            {
                DbOpe_crm_contract_receiptregister.Instance.TransDeal(() =>
                {
                    if (type == 1)
                    {
                        Db_crm_contract_receiptregister receiptregister = DbOpe_crm_contract_receiptregister.Instance.GetDataById(id);
                        if (receiptregister.State == EnumRegisterState.ToBeRegistered.ToInt())
                        {
                            //删除到账登记
                            DbOpe_crm_contract_receiptregister.Instance.DeleteData(id);
                            //删除到账登记明细
                            DbOpe_crm_contract_receipt_details.Instance.DeleteContractReceiptDetailsByContractReceiptRegisterId(id);
                            //删除到账登记产品
                            DbOpe_crm_contract_receiptregister_achievement.Instance.DeleteContractReceiptRegisterAchievementByContractReceiptRegisterId(id);
                            //删除到账登记产品超级子账号
                            DbOpe_crm_contract_receiptregister_supersubaccount_achievement.Instance.DeleteContractReceiptRegisterSuperSubAccountAchievementByContractReceiptRegisterId(id);
                            //删除到账登记确认审核
                            DbOpe_crm_contract_receiptregister_achievement_audit.Instance.DeleteContractReceiptRegisterAchievementAuditByContractReceiptRegisterId(id);
                            //删除到账登记审核
                            DbOpe_crm_contract_receiptregister_audit.Instance.DeleteReceiptRegisterAuditByContractReceiptRegisterId(id);
                            //删除到账登记项目
                            DbOpe_crm_contract_receiptregister_projectinfo_achievement.Instance.DeleteContractReceiptRegisterProjectInfoAchievementByContractReceiptRegisterId(id);
                            //该合同暂时不在到账登记中显示
                            DbOpe_crm_contract.Instance.UpdateIsNotShowReceiptRegister(receiptregister.ContractId, true);

                            DbOpe_crm_contract_receiptregister_congratulations.Instance.DeleteContractReceiptRegisterCongratulationsByContractReceiptRegisterId(id);
                        }
                        else
                        {
                            throw new ApiException("当前到账登记状态下，不可以删除");
                        }
                    }
                    if (type == 3)
                    {
                        int ToBeRegistered = EnumRegisterState.ToBeRegistered.ToInt();
                        Db_v_searchcontractreceiptregister contractreceiptregister = DbOpe_crm_contract_receiptregister.Instance.GetContractreceiptregisterByContractIdAndTypeAndState(id, 3, ToBeRegistered);
                        if (contractreceiptregister != null)
                        {
                            //该合同暂时不在到账登记中显示
                            DbOpe_crm_contract.Instance.UpdateIsNotShowReceiptRegister(id, true);
                        }
                        else
                        {
                            throw new ApiException("当前到账登记状态下，不可以删除");
                        }
                    }
                });
            }
            else
            {
                throw new ApiException("登记类型不正确，不可以删除");
            }
        }

        /// <summary>
        /// 获取合同到账登记状态字典
        /// </summary>
        public List<Dictionary_Out> GetReceiptRegisterStateList()
        {
            //临时
            List<FormInterfaceName> list = DbOpe_sys_form.Instance.GetFormIdsByUserId(UserId);
            List<int?> state = new List<int?>();
            List<FormInterfaceName> addPermissionList = list.Where(r => r.ControllerName == "ContractReceiptRegister" && (r.MethodName == "AddReceiptRegister" || r.MethodName == "UpdateReceiptRegister")).ToList();
            if (addPermissionList.Count() > 0)
            {
                state.Add(EnumRegisterState.Submit.ToInt());
                state.Add(EnumRegisterState.Refuse.ToInt());
                state.Add(EnumRegisterState.MatchingPendingRegistration.ToInt());
                state.Add(EnumRegisterState.Confirmed.ToInt());
                state.Add(EnumRegisterState.Questionable.ToInt());
                state.Add(EnumRegisterState.Reminder.ToInt());
                //state.Add(EnumRegisterState.ToBeRegistered.ToInt());
                state.Add(EnumRegisterState.PartMatchingPendingRegistration.ToInt());
                state.Add(EnumRegisterState.ManualMatchingPendingRegistration.ToInt());
            }
            List<FormInterfaceName> auditPermissionList = list.Where(r => r.ControllerName == "ContractReceiptRegister" && r.MethodName == "AuditContractReceiptRegister").ToList();
            if (auditPermissionList.Count() > 0)
            {
                state.Add(EnumRegisterState.Submit.ToInt());
                state.Add(EnumRegisterState.Pass.ToInt());
                state.Add(EnumRegisterState.Refuse.ToInt());
                // state.Add(EnumRegisterState.Confirmed.ToInt());
                // state.Add(EnumRegisterState.Questionable.ToInt());
                state.Add(EnumRegisterState.Locked.ToInt());
            }
            List<FormInterfaceName> lockPermissionList = list.Where(r => r.ControllerName == "ContractReceiptRegister" && (r.MethodName == "LockContractReceiptRegisterAchievement" || r.MethodName == "UnLockContractReceiptRegisterAchievement")).ToList();
            if (lockPermissionList.Count() > 0)
            {
                state.Add(EnumRegisterState.Submit.ToInt());
                state.Add(EnumRegisterState.Pass.ToInt());
                state.Add(EnumRegisterState.Refuse.ToInt());
                state.Add(EnumRegisterState.MatchingPendingRegistration.ToInt());
                state.Add(EnumRegisterState.Confirmed.ToInt());
                state.Add(EnumRegisterState.Questionable.ToInt());
                state.Add(EnumRegisterState.Reminder.ToInt());
                state.Add(EnumRegisterState.Locked.ToInt());
                //state.Add(EnumRegisterState.ToBeRegistered.ToInt());
                state.Add(EnumRegisterState.PartMatchingPendingRegistration.ToInt());
                state.Add(EnumRegisterState.ManualMatchingPendingRegistration.ToInt());
            }
            state = state.Distinct().ToList();
            //临时

            List<Dictionary_Out> result = DbOpe_sys_dictionary.Instance.GetDictionaryByParentValue("RegisterState");
            result = result.Where(r => state.Contains(r.Value.ToInt())).OrderBy(r => r.CreateDate).ThenBy(r => r.Value).ToList();
            return result;
        }

        private decimal GetProjectInfoPerformanceDeduction(List<ReceiptRegisterProjectInfoAchievementInfo_Performance> list, int type)
        {
            if (list.Where(r => r.Type == type).Count() == 0)
            {
                return 0;
            }
            else
            {
                return list.Where(r => r.Type == type).Sum(r => r.PerformanceDeduction).ToDecimal();
            }
        }

        private decimal GetPerformanceDeduction(List<ReceiptRegisterAchievementInfo_Performance> list, int ProductType)
        {
            if (list.Where(r => r.ProductType == ProductType).Count() == 0)
            {
                return 0;
            }
            else
            {
                return list.Where(r => r.ProductType == ProductType).Sum(r => r.PerformanceDeduction).ToDecimal();
            }
        }

        //重新计算到账登记业绩
        public void ReCalculatePerformance(string receiptRegisterId)
        {
            DbOpe_crm_contract_receiptregister.Instance.TransDeal(() =>
            {
                Db_crm_contract_receiptregister receiptregister = DbOpe_crm_contract_receiptregister.Instance.GetDataById(receiptRegisterId);
                List<string> CollectionInfoIds = DbOpe_crm_contract_receipt_details.Instance.GetCollectionInfoIdListByContractReceiptRegisterId(receiptRegisterId);

                //到账金额
                decimal ArrivalAmount = DbOpe_crm_collectioninfo.Instance.GetArrivalAmountByIds(CollectionInfoIds);

                List<ReceiptRegisterProjectInfoAchievementInfo_Performance> ReceiptRegisterProjectInfoAchievement = DbOpe_crm_contract_receiptregister_projectinfo_achievement.Instance.GetReceiptRegisterContractProjectInfoPerformance(receiptRegisterId);
                //获取项目扣除
                decimal RefundItems = GetProjectInfoPerformanceDeduction(ReceiptRegisterProjectInfoAchievement, EnumProjectinfoType.RefundItems.ToInt()); //ReceiptRegisterProjectInfoAchievement.Where(r => r.Type == EnumProjectinfoType.RefundItems.ToInt()).Sum(r => r.PerformanceDeduction);
                decimal ReturnPerformance = GetProjectInfoPerformanceDeduction(ReceiptRegisterProjectInfoAchievement, EnumProjectinfoType.ReturnPerformance.ToInt());//ReceiptRegisterProjectInfoAchievement.Where(r => r.Type == EnumProjectinfoType.ReturnPerformance.ToInt()).Sum(r => r.PerformanceDeduction);
                decimal Others = GetProjectInfoPerformanceDeduction(ReceiptRegisterProjectInfoAchievement, EnumProjectinfoType.Others.ToInt()); //ReceiptRegisterProjectInfoAchievement.Where(r => r.Type == EnumProjectinfoType.Others.ToInt()).Sum(r => r.PerformanceDeduction);

                List<ReceiptRegisterAchievementInfo_Performance> ReceiptRegisterAchievement = DbOpe_crm_contract_receiptregister_achievement.Instance.GetReceiptRegisterContractProjectInfoPerformance(receiptRegisterId);
                //获取产品扣除
                decimal DBPerformanceDeduction = GetPerformanceDeduction(ReceiptRegisterAchievement, EnumProductType.DandB.ToInt()); //ReceiptRegisterAchievement.Where(r => r.ProductType == EnumProductType.DandB.ToInt()).Sum(r => r.PerformanceDeduction);
                decimal GlobalSearchPerformanceDeduction = GetPerformanceDeduction(ReceiptRegisterAchievement, EnumProductType.Global.ToInt()); //ReceiptRegisterAchievement.Where(r => r.ProductType == EnumProductType.Global.ToInt()).Sum(r => r.PerformanceDeduction);
                decimal OtherPerformanceDeduction = GetPerformanceDeduction(ReceiptRegisterAchievement, EnumProductType.Other.ToInt()); //ReceiptRegisterAchievement.Where(r => r.ProductType == EnumProductType.Other.ToInt()).Sum(r => r.PerformanceDeduction);

                ////销售业绩：到账金额-退还项目+返还业绩
                //decimal SalesPerformance = ArrivalAmount - RefundItems + ReturnPerformance;
                ////扣除业绩：自动计算签约产品中扣除业绩之和,返还业绩做减法
                //decimal DeductPerformance = addReceiptRegisterIn.ReceiptRegisterAchievement.Sum(r=>r.PerformanceDeduction) + addReceiptRegisterIn.ReceiptRegisterProjectInfoAchievement.Sum(r=>r.PerformanceDeduction) - 2 * addReceiptRegisterIn.ReceiptRegisterProjectInfoAchievement.Where(r=> r.Type == EnumProjectinfoType.ReturnPerformance.ToInt()).Sum(r => r.PerformanceDeduction);
                ////有效业绩：销售业绩-邓白氏扣除-环球搜扣除-其他(其他扣除+其他项目)
                //decimal EffectivePerformance = SalesPerformance - DBPerformanceDeduction - GlobalSearchPerformanceDeduction - (Others + OtherPerformanceDeduction);

                //获取业绩规则系数
                Db_crm_contract contract = DbOpe_crm_contract.Instance.GetContractById(receiptregister.ContractId.ToString());
                Db_sys_comparam_achi comparam_achi = DbOpe_sys_comparam_achi.Instance.GetComparamAchisByUserId(contract.Issuer);
                //decimal ratio = 1;
                string ratio = "";
                if (comparam_achi != null)
                {
                    //ratio = comparam_achi.PerformanceCoefficient.Value;
                    ratio = comparam_achi.PerformanceCoefficient;
                }

                //新公式
                //销售业绩：到账金额 - 退还项目 - 不计入业绩金额
                decimal NonPerformanceAmount = receiptregister.NonPerformanceAmount ?? 0;
                decimal SalesPerformance = Math.Floor(ArrivalAmount - RefundItems - NonPerformanceAmount);
                //扣除业绩：签约产品扣除业绩之和
                decimal DeductPerformance = ReceiptRegisterAchievement.Count == 0 ? 0 : ReceiptRegisterAchievement.Sum(r => r.PerformanceDeduction.Value);
                //有效业绩：销售业绩 - 扣除业绩 - 其他项目 + 返还业绩
                decimal EffectivePerformanceBase = Math.Floor((SalesPerformance - DeductPerformance - Others + ReturnPerformance));// * ratio; 20240705 取消系数相乘//20240704调整销售业绩基础值，两数值对调

                decimal EffectivePerformance = Math.Floor(SalesPerformance - DeductPerformance - Others + ReturnPerformance);
                //decimal EffectivePerformanceRatio = ratio;
                string EffectivePerformanceRatio = ratio;

                DbOpe_crm_contract_receiptregister.Instance.UpdateData(r => new Db_crm_contract_receiptregister { SalesPerformance = SalesPerformance, DeductPerformance = DeductPerformance, EffectivePerformance = EffectivePerformance, EffectivePerformanceBase = EffectivePerformanceBase, EffectivePerformanceRatio = EffectivePerformanceRatio }, r => r.Id == receiptRegisterId);
            });
        }

        /// <summary>
        /// 恭喜确认到账登记
        /// </summary>
        public void CongratulationsReceiptRegister(string id)
        {
            DbOpe_crm_contract.Instance.TransDeal(() =>
            {
                int Congratulations = EnumCongratulations.Congratulations.ToInt();
                Db_crm_contract_receiptregister receiptregister = DbOpe_crm_contract_receiptregister.Instance.GetDataById(id);
                if (receiptregister == null)
                {
                    throw new ApiException("该到账登记信息不存在");
                }
                else if (receiptregister.Congratulations == Congratulations)
                {
                    throw new ApiException("该到账登记信息已恭喜确认");
                }
                DbOpe_crm_contract_receiptregister.Instance.UpdateData(r => new Db_crm_contract_receiptregister { Congratulations = Congratulations, CongratulationsDate = DateTime.Now }, id);
            });
        }

        /// <summary>
        /// 撤销恭喜确认到账登记
        /// </summary>
        public void RevokeCongratulationsReceiptRegister(string id)
        {
            DbOpe_crm_contract.Instance.TransDeal(() =>
            {
                int NoCongratulations = EnumCongratulations.NoCongratulations.ToInt();
                Db_crm_contract_receiptregister receiptregister = DbOpe_crm_contract_receiptregister.Instance.GetDataById(id);
                if (receiptregister == null)
                {
                    throw new ApiException("该到账登记信息不存在");
                }
                else if (receiptregister.Congratulations == NoCongratulations)
                {
                    throw new ApiException("该到账登记信息未恭喜确认");
                }
                DbOpe_crm_contract_receiptregister.Instance.UpdateData(r => new Db_crm_contract_receiptregister { Congratulations = NoCongratulations, CongratulationsDate = null }, id);
            });
        }

        public List<CongratulationsProductInfo_Out> GetCongratulationsProductInfoList(string ContractId)
        {
            return DbOpe_sys_congratulations_productinfo.Instance.GetCongratulationsProductInfoList(ContractId);
        }

        public List<Congratulations_Out> GetCongratulationsList(CongratulationsList_In congratulationsListIn)
        {
            ContractSimple_Out contract = DbOpe_crm_contract.Instance.GetContractInfoByContractId(congratulationsListIn.ContractId);
            //直属中队名
            //string OrgRegimentName = StringUtil.IsNullOrEmpty(contract.OrgRegimentName) ? (StringUtil.IsNullOrEmpty(contract.OrgBrigadeName) ? contract.OrgDivisionName : contract.OrgBrigadeName) : contract.OrgRegimentName;
            //修改 2025年8月4日 修改为取登记时合同归属人所属的队伍
            string OrgRegimentName = DbOpe_sys_user.Instance.GetUserOrgName(contract.Issuer);
            //出单人名字
            string IssuerName = contract.IssuerName;

            List<ProductInfo_Out> receiptregister = DbOpe_crm_contract_receiptregister.Instance.GetContractReceiptRegisterByContractIdAndBelongingMonth(congratulationsListIn.ContractId, contract.Issuer);

            List<Congratulations_Out> resultList = new List<Congratulations_Out>();
            List<ProductInfo_Out> contractProductList = DbOpe_crm_contract_productinfo.Instance.GetContractProductInfoByContractId(congratulationsListIn.ContractId, false);
            foreach (ProductInfo_Out contractProduct in contractProductList)
            {
                if (contractProduct.ProductType == EnumProductType.DandB.ToInt() || contractProduct.ProductType == EnumProductType.Gtis.ToInt() || contractProduct.ProductType == EnumProductType.Combination.ToInt())
                {
                    Congratulations_Out result = new Congratulations_Out();
                    Db_sys_congratulations_productinfo congratulationsProductinfo = DbOpe_sys_congratulations_productinfo.Instance.GetData(r => (r.ProductId == contractProduct.ProductId || r.ProductId == contractProduct.ParentProductId));
                    if (congratulationsProductinfo != null)
                    {
                        string ContractProductInfoId = "";
                        string ContractProductinfoPriceTotal = "";

                        int PriceMode = EnumPriceMode.Fixed.ToInt();
                        int Currency = EnumCurrency.CNY.ToInt();
                        //Db_crm_product_price price = DbOpe_crm_product_price.Instance.GetData(r => r.Deleted == false && r.ProductId == congratulationsProductinfo.ProductId && r.Currency == Currency && r.PriceMode == PriceMode);
                        Db_crm_product product = DbOpe_crm_product.Instance.GetData(r => r.Deleted == false && r.Id == congratulationsProductinfo.ProductId);

                        ContractProductinfoPriceTotal = (product.CNYTypical * contractProduct.OpeningYears).Value.ToString("0.##");

                        if (contractProduct.ChildNode != null)
                        {
                            ContractProductInfoId = contractProduct.ChildNode.Max(s => s.Id);
                            //ContractProductinfoPriceTotal = contractProduct.ChildNode.Max(s => s.ParentContractProductinfoPriceTotal).ToString();
                        }
                        else
                        {
                            ContractProductInfoId = contractProduct.Id;
                            //ContractProductinfoPriceTotal = contractProduct.ContractProductinfoPriceTotal.ToString();
                        }

                        result.ContractProductInfoId = new Guid(ContractProductInfoId);
                        result.ProductType = contractProduct.ProductType;
                        result.Type = contractProduct.ProductType;
                        result.CongratulationsProductinfoId = congratulationsProductinfo.Id;
                        result.CongratulationsProductinfoName = congratulationsProductinfo.Name;
                        result.YearNum = contractProduct.OpeningYears;
                        result.Amount = ContractProductinfoPriceTotal;

                        string YearNum = "";
                        if (result.YearNum > 1)
                        {
                            if (result.YearNum == 2)
                            {
                                YearNum = "两年";
                            }
                            if (result.YearNum != 2)
                            {
                                YearNum = Tools.NumToChinese(result.YearNum.Value.ToString()) + "年";
                            }
                        }

                        string Copies = "";
                        int CopiesCount = receiptregister.Where(r => r.ProductType == contractProduct.ProductType).ToList().Count;
                        if (CopiesCount > 0)
                        {
                            string xh = "";
                            for (int i = 0; i < CopiesCount + 1; i++)
                            {
                                xh += "[鲜花]";
                            }
                            if (contractProduct.ProductType == EnumProductType.Gtis.ToInt())
                            {
                                Copies = "本月到账第" + Tools.NumToChinese((CopiesCount + 1).ToString()) + "单VIP套餐" + xh;
                            }
                            else
                            {
                                Copies = xh;
                            }
                            result.Copies = CopiesCount + 1;
                        }
                        else
                        {
                            Copies = "[鲜花]";
                            result.Copies = 1;
                        }

                        //if (contractProduct.ProductType == EnumProductType.DandB.ToInt())
                        //{
                        //    result.Describe = "恭喜" + OrgRegimentName + IssuerName + "到账" + result.CongratulationsProductinfoName + result.Amount + "套餐" + YearNum + "！" + Copies;
                        //}
                        //else
                        //{
                        //    result.Describe = "恭喜" + OrgRegimentName + IssuerName + "到账" + result.Amount + result.CongratulationsProductinfoName + YearNum + "！" + Copies;
                        //}

                        string UnitPrice = product.CNYTypical.Value.ToString("0.##");
                        string Amount = result.Amount;
                        if (result.YearNum == 1)
                        {
                            Amount = "";
                        }

                        result.Describe = congratulationsProductinfo.ContentTemplate
                    .Replace("{OrgRegimentName}", OrgRegimentName)
                    .Replace("{IssuerName}", IssuerName)
                    .Replace("{Amount}", Amount)
                    .Replace("{Name}", result.CongratulationsProductinfoName)
                    .Replace("{YearNum}", YearNum)
                    .Replace("{Copies}", Copies)
                    .Replace("{UnitPrice}", UnitPrice);

                        resultList.Add(result);
                    }
                }
            }
            return resultList;
        }

        public string GetCongratulationsString(CongratulationsString_In congratulationsStringIn)
        {
            ContractSimple_Out contract = DbOpe_crm_contract.Instance.GetContractInfoByContractId(congratulationsStringIn.ContractId);
            //直属中队名
            string OrgRegimentName = StringUtil.IsNullOrEmpty(contract.OrgRegimentName) ? (StringUtil.IsNullOrEmpty(contract.OrgBrigadeName) ? contract.OrgDivisionName : contract.OrgBrigadeName) : contract.OrgRegimentName;
            //出单人名字
            string IssuerName = contract.IssuerName;

            List<ProductInfo_Out> receiptregister = DbOpe_crm_contract_receiptregister.Instance.GetContractReceiptRegisterByContractIdAndBelongingMonth(congratulationsStringIn.ContractId, contract.Issuer);

            List<CongratulationsProductInfo_Out> CongratulationsProductInfoList = DbOpe_sys_congratulations_productinfo.Instance.GetCongratulationsProductInfoList(congratulationsStringIn.ContractId);

            CongratulationsProductInfo_Out congratulationsProductinfo = CongratulationsProductInfoList.Where(r => r.Id == congratulationsStringIn.CongratulationsProductinfoId).First();

            string YearNum = "";
            if (congratulationsStringIn.YearNum > 1)
            {
                if (congratulationsStringIn.YearNum == 2)
                {
                    YearNum = "两年";
                }
                if (congratulationsStringIn.YearNum != 2)
                {
                    YearNum = Tools.NumToChinese(congratulationsStringIn.YearNum.Value.ToString()) + "年";
                }
            }

            string Copies = "";
            //int CopiesCount = receiptregister.Where(r => r.ProductType == congratulationsProductinfo.ProductType).ToList().Count;
            int CopiesCount = congratulationsStringIn.Copies.Value;
            if (CopiesCount > 0)
            {
                string xh = "";
                for (int i = 0; i < CopiesCount; i++)
                {
                    xh += "[鲜花]";
                }
                if (congratulationsProductinfo.ProductType == EnumProductType.Gtis.ToInt())
                {
                    Copies = "本月到账第" + Tools.NumToChinese((CopiesCount).ToString()) + "单VIP套餐" + xh;
                }
                else
                {
                    Copies = xh;
                }
            }
            else
            {
                Copies = "[鲜花]";
            }

            int Amount = 0;
            if (congratulationsStringIn.Amount.IsNotNullOrEmpty())
            {
                Amount = congratulationsStringIn.Amount.ToInt();
            }
            int YearNumI = 1;
            if (congratulationsStringIn.YearNum != null)
            {
                YearNumI = congratulationsStringIn.YearNum.Value;
            }

            string UnitPrice = (Amount / YearNumI).ToString("0.##");

            string AmountStr = congratulationsStringIn.Amount;
            if (YearNumI == 1)
            {
                AmountStr = "";
            }

            return congratulationsProductinfo.ContentTemplate
                    .Replace("{OrgRegimentName}", OrgRegimentName)
                    .Replace("{IssuerName}", IssuerName)
                    .Replace("{Amount}", AmountStr)// congratulationsStringIn.Amount)
                    .Replace("{Name}", congratulationsProductinfo.Name)
                    .Replace("{YearNum}", YearNum)
                    .Replace("{Copies}", Copies)
                    .Replace("{UnitPrice}", UnitPrice);
            //if (congratulationsProductinfo.ProductType == EnumProductType.DandB.ToInt())
            //{

            //    return "恭喜" + OrgRegimentName + IssuerName + "到账" + congratulationsProductinfo.Name + congratulationsStringIn.Amount + "套餐" + YearNum + "！" + Copies;
            //    //return "恭喜" + OrgRegimentName + IssuerName + "到账" + congratulationsStringIn.Amount + congratulationsProductinfo.Name + YearNum + "！" + Copies;
            //}
            //else
            //{
            //    return "恭喜" + OrgRegimentName + IssuerName + "到账" + congratulationsStringIn.Amount + congratulationsProductinfo.Name + YearNum + "！" + Copies;
            //}
        }

        /// <summary>
        /// 到账业绩导出
        /// </summary>
        /// <param name="searchContractReceiptRegisterListIn"></param>
        /// <returns></returns>
        public Stream DownloadContractReceiptRegisterList(SearchContractReceiptRegisterList_In searchContractReceiptRegisterListIn)
        {
            //SearchContractReceiptRegisterList_Sta_Out sta = DbOpe_crm_contract_receiptregister.Instance.SearchContractReceiptRegisterSta(searchContractReceiptRegisterListIn);
            //List<SearchContractReceiptRegisterList_Sta_Out> liststa = new List<SearchContractReceiptRegisterList_Sta_Out>();
            //liststa.Add(sta);
            //var expliststa = liststa.MappingTo<List<DownloadContractReceiptRegisterList_Sta_Out>>();
            //List<SearchContractReceiptRegisterList_Out> list = DbOpe_crm_contract_receiptregister.Instance.SearchContractReceiptRegisterList(searchContractReceiptRegisterListIn);
            //var explist = list.MappingTo<List<DownloadContractReceiptRegisterList_Out>>();
            //ExcelExporter exp = new ExcelExporter();
            //exp.Append(expliststa, "1").SeparateByRow().AppendHeaders().Append(explist,"2");
            List<SearchContractReceiptRegisterList_Out> list = DbOpe_crm_contract_receiptregister.Instance.SearchContractReceiptRegisterList(searchContractReceiptRegisterListIn);
            var explist = list.MappingTo<List<DownloadContractReceiptRegisterList_Out>>();
            //ExcelExporter exp = new ExcelExporter();
            //exp.Append(explist, "2");
            //var expBytes = await exp.ExportAppendDataAsByteArray();            
            ExcelExporterNPOI exp = new ExcelExporterNPOI();
            var expBytes = exp.ExportAsByteArray(explist);
            return new MemoryStream(expBytes);
        }

        public List<Coupon_Out> GetCouponList(CouponList_In couponListIn)
        {
            ContractSimple_Out contract = DbOpe_crm_contract.Instance.GetContractInfoByContractId(couponListIn.ContractId);
            List<Coupon_Out> resultList = new List<Coupon_Out>();
            List<ProductInfo_Out> contractProductList = DbOpe_crm_contract_productinfo.Instance.GetContractProductInfoByContractId(couponListIn.ContractId, false);
            foreach (ProductInfo_Out contractProduct in contractProductList)
            {
                if (contractProduct.ProductType == EnumProductType.Gtis.ToInt() || contractProduct.ProductType == EnumProductType.Combination.ToInt())
                {
                    Coupon_Out result = new Coupon_Out();
                    result.YearNum = contractProduct.OpeningYears;
                    result.CouponCount = contractProduct.OpeningYears;
                    resultList.Add(result);
                }
            }
            return resultList;
        }

        public GetGlobalSearchUnitList_Out GetGlobalSearchUnitByContractIdAndCollectioninfoId(GetGlobalSearchUnitList_In globalSearchUnitIn)
        {
            ContractSimple_Out contract = DbOpe_crm_contract.Instance.GetContractInfoByContractId(globalSearchUnitIn.ContractId);
            Db_crm_collectioninfo collectioninfo = DbOpe_crm_collectioninfo.Instance.GetDataById(globalSearchUnitIn.CollectioninfoId);
            List<ProductInfo_Out> contractProductList = DbOpe_crm_contract_productinfo.Instance.GetContractProductInfoByContractId(globalSearchUnitIn.ContractId, false);
            List<ProductInfo_Out> GlobalList = contractProductList.Where(r => r.ProductType == EnumProductType.Global.ToInt()).ToList();
            decimal NumberMonthsReceived = 0;
            if (GlobalList.Count > 0)
            {
                //到账月数 = 到账金额/（合同金额/合同年份*12）
                int OpeningYears = GlobalList.First().OpeningYears.Value;
                decimal? NumberMonths = 0;
                if (contract.Currency == EnumCurrency.CNY.ToInt())
                {
                    decimal ArrivalAmount = collectioninfo.ArrivalAmount == null ? 0 : collectioninfo.ArrivalAmount.Value;
                    NumberMonths = ArrivalAmount / (contract.ContractAmount / (OpeningYears * 12));
                }
                else
                {
                    decimal FCArrivalAmount = collectioninfo.FCArrivalAmount == null ? 0 : collectioninfo.FCArrivalAmount.Value;
                    NumberMonths = FCArrivalAmount / (contract.FCContractAmount / (OpeningYears * 12));
                }

                if (NumberMonths < 5)
                {
                    NumberMonthsReceived = 0;
                }
                else if (NumberMonths >= 5 && NumberMonths < 12)
                {
                    NumberMonthsReceived = decimal.Parse("0.5");
                }
                else if (NumberMonths >= 12 && NumberMonths < 24)
                {
                    NumberMonthsReceived = decimal.Parse("1");
                }
                else if (NumberMonths >= 24 && NumberMonths < 36)
                {
                    NumberMonthsReceived = decimal.Parse("2");
                }
                else if (NumberMonths >= 36 && NumberMonths < 48)
                {
                    NumberMonthsReceived = decimal.Parse("3");
                }
                else
                {
                    NumberMonthsReceived = decimal.Parse("3");
                }
            }
            return new GetGlobalSearchUnitList_Out { Name = NumberMonthsReceived.ToString(), Value = NumberMonthsReceived };
        }


        public SalesRulesResult AddGlobalSearchUnitHistory(AddGlobalSearchUnitHis_In addGlobalSearchUnit)
        {
            SalesRulesResult result = new SalesRulesResult();

            result = DbOpe_crm_contract_receiptregister_globalunit.Instance.AddGlobalSearchUnitHistory(addGlobalSearchUnit);


            result.affectedRow = DbOpe_crm_contract_receiptregister_globalunit.Instance.SaveQueues();
            result.state = 0;

            return result;
        }

        public SalesRulesResult AddIsAllowInvoicingRemark(AddAllowInvoicing_In addAllowInvoicing_In)
        {
            SalesRulesResult result = new SalesRulesResult();
            result = DbOpe_crm_contract_receiptregister_allowIncoicinghis.Instance.AddIsAllowInvoicingRemark(addAllowInvoicing_In);


            result.affectedRow = DbOpe_crm_contract_receiptregister_globalunit.Instance.SaveQueues();
            result.state = 0;

            return result;
        }

        /// <summary>
        /// 获取合同充值金额信息和建议分配
        /// </summary>
        /// <param name="getContractRechargeInfoIn">请求参数</param>
        /// <returns>充值金额信息和建议分配</returns>
        public GetContractRechargeInfo_Out GetContractRechargeInfo(GetContractRechargeInfo_In getContractRechargeInfoIn)
        {
            var result = new GetContractRechargeInfo_Out();

            // 获取合同总充值金额（合同中SaleWits充值产品的总金额）
            result.TotalRechargeAmount = BLL_Contract.Instance.GetContractRechargeAmount(getContractRechargeInfoIn.ContractId);

            // 获取已登记的充值金额（只包括已生效的登记记录） 
            // 待复核、待确认、拒绝、未确认、已确认、有疑问、已锁定
            // 等状态才算已登记
            var validStates = new List<int>
            {
                EnumRegisterState.Submit.ToInt(),
                EnumRegisterState.Pass.ToInt(),
                EnumRegisterState.Refuse.ToInt(),
                EnumRegisterState.UnConfirmed.ToInt(),
                EnumRegisterState.Confirmed.ToInt(),
                EnumRegisterState.Questionable.ToInt(),
                EnumRegisterState.Locked.ToInt()
            };

            var registeredReceipts = DbContext.Crm2Db.Queryable<Db_v_searchcontractreceiptregister_sta>()
                .Where(r => r.ContractId == getContractRechargeInfoIn.ContractId &&
                             validStates.Contains(r.State.Value))
                .ToList();

            // 如果提供了CollectionId，排除当前到账对应的登记记录（用于修改登记场景）
            if (!string.IsNullOrEmpty(getContractRechargeInfoIn.CollectionId))
            {
                // 查找当前到账对应的登记记录
                var currentReceiptRegister = DbOpe_crm_contract_receipt_details.Instance
                    .GetDataList(d => d.CollectionInfoId == getContractRechargeInfoIn.CollectionId && d.Deleted == false)
                    .Select(d => d.ContractReceiptRegisterId)
                    .FirstOrDefault();

                if (!string.IsNullOrEmpty(currentReceiptRegister))
                {
                    registeredReceipts = registeredReceipts.Where(r => r.Id != currentReceiptRegister).ToList();
                }
            }

            result.RegisteredRechargeAmount = registeredReceipts.Sum(r => r.RechargeAmount.HasValue ? r.RechargeAmount.Value : 0);

            // 计算剩余未登记充值金额
            result.RemainingRechargeAmount = Math.Max(0, result.TotalRechargeAmount - result.RegisteredRechargeAmount);

            // 如果提供了收款信息ID，计算本次到账中应该分配的充值金额
            if (!string.IsNullOrEmpty(getContractRechargeInfoIn.CollectionId))
            {
                // 直接通过收款信息ID获取到账金额
                var collectionInfo = DbOpe_crm_collectioninfo.Instance
                    .GetDataList(c => c.Id == getContractRechargeInfoIn.CollectionId && c.Deleted == false)
                    .FirstOrDefault();

                decimal totalArrivalAmount = collectionInfo?.ArrivalAmount ?? 0;

                // 本次到账中的充值金额 = 取较小值(本次到账总金额, 剩余未登记充值金额)
                result.SuggestedRechargeAmount = Math.Min(totalArrivalAmount, result.RemainingRechargeAmount);

                // 本次到账中的不计入业绩金额 = 本次到账中的充值金额
                result.SuggestedNonPerformanceAmount = result.SuggestedRechargeAmount;
            }
            else
            {
                result.SuggestedRechargeAmount = null;
                result.SuggestedNonPerformanceAmount = null;
            }

            return result;
        }

    }
}
