using CRM2_API.DAL.DbCommon;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using LgyUtil;
using SqlSugar;
using System;
using System.Linq;

namespace CRM2_API.DAL.DbModelOpe.Crm2
{
    /// <summary>
    /// 合同表操作 - 服务整合相关方法
    /// </summary>
    public partial class DbOpe_crm_contract
    {
        /// <summary>
        /// 获取合同已开通服务数量（整合慧思服务）
        /// 将GTIS、VIP、环球搜、SaleWits、慧思学院整合为一个慧思服务计算
        /// 基于v_productserviceinfostatenum视图的逻辑实现
        /// </summary>
        /// <param name="contractId">合同ID</param>
        /// <returns>已开通服务数量</returns>
        public int ContractProductServiceInfoOpenedNumIntegrated(string contractId)
        {
            try
            {
                // 定义慧思服务包含的产品类型
                var witsServiceTypes = new[]
                {
                    EnumProductType.Gtis.ToInt(),
                    EnumProductType.Vip.ToInt(),
                    EnumProductType.Global.ToInt(),
                    EnumProductType.SalesWits.ToInt(),
                    EnumProductType.GlobalWitsSchool.ToInt()
                };

                // 获取合同中所有服务信息
                var allServices = Db.Queryable<Db_v_productserviceinfo>()
                    .Where(x => x.ContractId == contractId)
                    .Select(x => new { x.ProductType, x.ApplState })
                    .ToList();

                if (!allServices.Any())
                {
                    return 0;
                }

                // 分离慧思服务和独立服务
                var witsServices = allServices.Where(s => s.ProductType.HasValue && witsServiceTypes.Contains(s.ProductType.Value)).ToList();
                var independentServices = allServices.Where(s => s.ProductType.HasValue && !witsServiceTypes.Contains(s.ProductType.Value)).ToList();

                int openedCount = 0;

                // 慧思服务：只要没有申请中的服务（ApplState = 1），且至少有一个开通的服务（ApplState = 2），就算作1个慧思服务
                var witsOpenedServices = witsServices.Where(s => s.ApplState == 2).ToList();
                var witsPendingServices = witsServices.Where(s => s.ApplState == 1).ToList();
                
                // 只要没有申请中的服务，且至少有一个开通的服务，就算作已开通
                if (!witsPendingServices.Any() && witsOpenedServices.Any())
                {
                    openedCount += 1;
                }

                // 独立服务：按实际开通数量计算
                var independentOpenedServices = independentServices.Where(s => s.ApplState == 2).ToList();
                openedCount += independentOpenedServices.Count;

                LogUtil.AddLog($"合同{contractId}已开通服务数量（整合后）: {openedCount}，" +
                              $"慧思服务: {(witsOpenedServices.Any() ? 1 : 0)}({witsOpenedServices.Count}/{witsServices.Count}), " +
                              $"独立服务: {independentOpenedServices.Count}");

                return openedCount;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取合同已开通服务数量异常，合同ID: {contractId}, 错误: {ex}");
                return 0;
            }
        }

        /// <summary>
        /// 获取合同服务开通详细统计（整合慧思服务）
        /// </summary>
        /// <param name="contractId">合同ID</param>
        /// <returns>服务开通统计信息</returns>
        public ContractServiceOpeningStats_Out GetContractServiceOpeningStatsIntegrated(string contractId)
        {
            try
            {
                // 获取合同中所有服务信息
                var allServices = Db.Queryable<Db_v_productserviceinfo>()
                    .Where(x => x.ContractId == contractId)
                    .Select(x => new { x.ProductType, x.ApplState })
                    .ToList();

                if (!allServices.Any())
                {
                    return new ContractServiceOpeningStats_Out
                    {
                        ContractId = contractId,
                        TotalServiceCount = 0,
                        OpenedServiceCount = 0,
                        PendingServiceCount = 0,
                        HasWitsService = false,
                        WitsServiceOpeningStatus = "未申请"
                    };
                }

                // 定义慧思服务包含的产品类型
                var witsServiceTypes = new[]
                {
                    EnumProductType.Gtis.ToInt(),
                    EnumProductType.Vip.ToInt(),
                    EnumProductType.Global.ToInt(),
                    EnumProductType.SalesWits.ToInt(),
                    EnumProductType.GlobalWitsSchool.ToInt()
                };

                // 分离慧思服务和独立服务
                var witsServices = allServices.Where(s => witsServiceTypes.Contains(s.ProductType.Value)).ToList();
                var independentServices = allServices.Where(s => !witsServiceTypes.Contains(s.ProductType.Value)).ToList();

                // 计算慧思服务状态
                bool hasWitsService = witsServices.Any();
                string witsServiceStatus = "未申请";
                bool isWitsServiceOpened = false;

                if (hasWitsService)
                {
                    var openedWitsServices = witsServices.Where(s => s.ApplState == EnumProcessStatus.Pass.ToInt()).ToList();
                    var pendingWitsServices = witsServices.Where(s => s.ApplState == EnumProcessStatus.Submit.ToInt()).ToList();

                    if (openedWitsServices.Any())
                    {
                        isWitsServiceOpened = true;
                        if (openedWitsServices.Count == witsServices.Count)
                        {
                            witsServiceStatus = "全部开通";
                        }
                        else
                        {
                            witsServiceStatus = $"部分开通({openedWitsServices.Count}/{witsServices.Count})";
                        }
                    }
                    else if (pendingWitsServices.Any())
                    {
                        witsServiceStatus = "审核中";
                    }
                    else
                    {
                        witsServiceStatus = "待开通";
                    }
                }

                // 计算独立服务统计
                var openedIndependentServices = independentServices.Where(s => s.ApplState == EnumProcessStatus.Pass.ToInt()).Count();
                var pendingIndependentServices = independentServices.Where(s => s.ApplState == EnumProcessStatus.Submit.ToInt()).Count();

                // 计算总体统计
                int totalServiceCount = (hasWitsService ? 1 : 0) + independentServices.Count;
                int openedServiceCount = (isWitsServiceOpened ? 1 : 0) + openedIndependentServices;
                int pendingServiceCount = (hasWitsService && !isWitsServiceOpened && witsServices.Any(s => s.ApplState == EnumProcessStatus.Submit.ToInt()) ? 1 : 0) + pendingIndependentServices;

                return new ContractServiceOpeningStats_Out
                {
                    ContractId = contractId,
                    TotalServiceCount = totalServiceCount,
                    OpenedServiceCount = openedServiceCount,
                    PendingServiceCount = pendingServiceCount,
                    HasWitsService = hasWitsService,
                    WitsServiceOpeningStatus = witsServiceStatus,
                    IndependentServiceCount = independentServices.Count,
                    OpenedIndependentServiceCount = openedIndependentServices
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取合同服务开通统计异常，合同ID: {contractId}, 错误: {ex}");
                return new ContractServiceOpeningStats_Out
                {
                    ContractId = contractId,
                    TotalServiceCount = 0,
                    OpenedServiceCount = 0,
                    PendingServiceCount = 0,
                    HasWitsService = false,
                    WitsServiceOpeningStatus = "查询异常"
                };
            }
        }
    }

    /// <summary>
    /// 合同服务开通统计信息
    /// </summary>
    public class ContractServiceOpeningStats_Out
    {
        /// <summary>
        /// 合同ID
        /// </summary>
        public string ContractId { get; set; }

        /// <summary>
        /// 总服务数量（整合后）
        /// </summary>
        public int TotalServiceCount { get; set; }

        /// <summary>
        /// 已开通服务数量（整合后）
        /// </summary>
        public int OpenedServiceCount { get; set; }

        /// <summary>
        /// 待开通服务数量（整合后）
        /// </summary>
        public int PendingServiceCount { get; set; }

        /// <summary>
        /// 是否包含慧思服务
        /// </summary>
        public bool HasWitsService { get; set; }

        /// <summary>
        /// 慧思服务开通状态描述
        /// </summary>
        public string WitsServiceOpeningStatus { get; set; }

        /// <summary>
        /// 独立服务数量
        /// </summary>
        public int IndependentServiceCount { get; set; }

        /// <summary>
        /// 已开通独立服务数量
        /// </summary>
        public int OpenedIndependentServiceCount { get; set; }
    }
}
