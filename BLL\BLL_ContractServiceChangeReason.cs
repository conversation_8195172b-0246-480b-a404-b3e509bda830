using CRM2_API.BLL.Common;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.System;
using LgyUtil;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CRM2_API.BLL
{
    /// <summary>
    /// 合同服务变更原因业务逻辑
    /// </summary>
    public class BLL_ContractServiceChangeReason : BaseBLL<BLL_ContractServiceChangeReason>
    {
        /// <summary>
        /// 根据合同ID获取可用的服务变更原因
        /// 基于主合同下所有增项合同的产品和申请状态，动态返回可用的变更原因选项
        /// </summary>
        /// <param name="contractId">合同ID</param>
        /// <returns>可用的服务变更原因列表</returns>
        public List<ApplGtisInfo_OUT_ChangeReason> GetAvailableServiceChangeReasons(string contractId)
        {
            try
            {
                if (string.IsNullOrEmpty(contractId))
                {
                    throw new ApiException("合同ID不能为空");
                }

                // 获取所有基础变更原因（根据条件判断）
                var availableReasons = GetBaseServiceChangeReasons(contractId);

                // 获取主合同ID（如果当前是增项合同，需要找到主合同）
                var mainContractId = GetMainContractId(contractId);

                // 获取主合同下所有增项合同中可申请的相关产品
                // 包括：未申请(NULL/0)、拒绝(3)、作废(4)的产品
                var availableProducts = GetAllContractProductsUnderMainContract(mainContractId)
                    .Where(p => p.ApplState == null ||
                               p.ApplState == 0 ||
                               p.ApplState == (int)EnumProcessStatus.Refuse ||
                               p.ApplState == (int)EnumProcessStatus.Void)
                    .ToList();

                // 根据可申请产品添加对应的动态变更原因
                var dynamicReasons = GetDynamicServiceChangeReasons(availableProducts);
                availableReasons.AddRange(dynamicReasons);

                LogUtil.AddLog($"获取合同{contractId}可用服务变更原因成功，" +
                              $"主合同: {mainContractId}，基础原因: {availableReasons.Count - dynamicReasons.Count}，" +
                              $"动态原因: {dynamicReasons.Count}，可申请产品: {availableProducts.Count}，总计: {availableReasons.Count}");

                return availableReasons;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取合同可用服务变更原因异常，合同ID: {contractId}, 错误: {ex}");
                throw;
            }
        }

        /// <summary>
        /// 获取基础服务变更原因（根据条件判断是否可用）
        /// </summary>
        /// <param name="contractId">合同ID</param>
        /// <returns>基础变更原因列表</returns>
        private List<ApplGtisInfo_OUT_ChangeReason> GetBaseServiceChangeReasons(string contractId)
        {
            var baseReasons = new List<ApplGtisInfo_OUT_ChangeReason>();

            // 固定可用的基础变更原因
            var alwaysAvailableReasons = new[]
            {
                EnumGtisServiceChangeProject.ApplyResidualService,
                EnumGtisServiceChangeProject.ChangeServiceContent,
                EnumGtisServiceChangeProject.OtherReason
            };

            foreach (var reasonEnum in alwaysAvailableReasons)
            {
                baseReasons.Add(new ApplGtisInfo_OUT_ChangeReason
                {
                    ChangeReasonEnum = reasonEnum,
                    ChangeReasonEnumName = reasonEnum.GetEnumDescription()
                });
            }

            // 条件性可用的变更原因
            // 个人服务天数延期：需要检查服务是否在6个月内
            if (CanUsePersonalServiceDays(contractId))
            {
                baseReasons.Add(new ApplGtisInfo_OUT_ChangeReason
                {
                    ChangeReasonEnum = EnumGtisServiceChangeProject.DelayPersonalServiceDays,
                    ChangeReasonEnumName = EnumGtisServiceChangeProject.DelayPersonalServiceDays.GetEnumDescription()
                });
            }

            // 优惠券延期：需要检查是否有可用优惠券
            if (CanUseCoupons(contractId))
            {
                baseReasons.Add(new ApplGtisInfo_OUT_ChangeReason
                {
                    ChangeReasonEnum = EnumGtisServiceChangeProject.DelayCoupons,
                    ChangeReasonEnumName = EnumGtisServiceChangeProject.DelayCoupons.GetEnumDescription()
                });
            }

            return baseReasons;
        }

        /// <summary>
        /// 检查是否可以使用个人服务天数延期
        /// 条件：服务截止时间在6个月内
        /// </summary>
        /// <param name="contractId">合同ID</param>
        /// <returns>是否可以使用个人服务天数</returns>
        private bool CanUsePersonalServiceDays(string contractId)
        {
            try
            {
                // 获取合同的GTIS服务信息
                var gtisService = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisServiceInfoByContractId(contractId);

                // 根据在服信息的服务截止时间进行判断
                // 服务连续或者中断期在6个月内的，可以使用个人服务天数
                if (gtisService != null && gtisService.ServiceCycleEnd.HasValue &&
                    gtisService.ServiceCycleEnd.Value.AddMonths(6) > DateTime.Now.Date)
                {
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"检查个人服务天数可用性异常，合同ID: {contractId}, 错误: {ex}");
                return false;
            }
        }

        /// <summary>
        /// 检查是否可以使用优惠券延期
        /// 条件：客户有可用的优惠券
        /// </summary>
        /// <param name="contractId">合同ID</param>
        /// <returns>是否可以使用优惠券</returns>
        private bool CanUseCoupons(string contractId)
        {
            try
            {
                // 获取合同信息
                var contract = DbOpe_crm_contract.Instance.QueryByPrimaryKey(contractId);
                if (contract == null) return false;

                // 检查客户是否有可用的优惠券
                var availableCoupons = DbOpe_crm_customer_coupon.Instance.GetCouponDetailByContractId(contractId);

                return availableCoupons != null && availableCoupons.Count > 0;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"检查优惠券可用性异常，合同ID: {contractId}, 错误: {ex}");
                return false;
            }
        }

        /// <summary>
        /// 获取主合同ID（如果当前是增项合同，返回主合同ID；否则返回自身）
        /// </summary>
        /// <param name="contractId">合同ID</param>
        /// <returns>主合同ID</returns>
        public string GetMainContractId(string contractId)
        {
            var contract = DbOpe_crm_contract.Instance.GetData(c => c.Id == contractId);
            if (contract == null)
            {
                throw new ApiException($"未找到合同信息，合同ID: {contractId}");
            }

            // 如果有父合同ID，说明是增项合同，返回父合同ID
            return string.IsNullOrEmpty(contract.ParentContractId) ? contractId : contract.ParentContractId;
        }

        /// <summary>
        /// 获取主合同下所有增项合同中的相关产品信息
        /// 只查询SalesWits、充值、新增资源三种产品类型，减少不必要的数据库访问
        /// </summary>
        /// <param name="mainContractId">主合同ID</param>
        /// <returns>相关产品信息列表</returns>
        private List<ContractProductInfo> GetAllContractProductsUnderMainContract(string mainContractId)
        {
            // 优化后的查询方式：只查询相关产品类型，减少不必要的数据库访问

            // 使用现有的查询方式，避免直接访问Db属性
            var result = new List<ContractProductInfo>();

            // 先获取符合条件的增项合同
            var addItemContracts = DbOpe_crm_contract.Instance.GetDataList(c =>
                c.ParentContractId == mainContractId &&
                c.Deleted != true &&
                (c.ContractStatus == (int)EnumContractStatus.Pass || c.ContractStatus == (int)EnumContractStatus.AutoPass)
            );

            // 获取相关产品类型的合同产品
            var relevantProductTypes = new[] {
                EnumProductType.SalesWits.ToInt(),
                EnumProductType.AddCredit.ToInt(),
                EnumProductType.AdditionalResource.ToInt()
            };

            foreach (var contract in addItemContracts)
            {
                var contractProducts = DbOpe_crm_contract_productinfo.Instance.GetDataList(cp =>
                    cp.ContractId == contract.Id && cp.Deleted != true);

                foreach (var cp in contractProducts)
                {
                    var product = DbOpe_crm_product.Instance.GetData(p => p.Id == cp.ProductId);
                    if (product != null && relevantProductTypes.Contains(product.ProductType ?? 0))
                    {
                        result.Add(new ContractProductInfo
                        {
                            ContractProductId = cp.Id,
                            ContractId = cp.ContractId,
                            ProductId = cp.ProductId,
                            ProductType = product.ProductType ?? 0,
                            ProductName = product.ProductName,
                            ApplState = cp.ApplState,
                            SubAccountsNum = cp.SubAccountsNum
                        });
                    }
                }
            }

            // 设置固定属性
            foreach (var item in result)
            {
                item.ServiceState = null;
                item.IsMainContract = false;
            }

            return result;
        }



        /// <summary>
        /// 根据可申请的产品信息获取动态变更原因
        /// 包括未申请、拒绝、作废状态的产品
        /// </summary>
        /// <param name="availableProducts">可申请的产品信息列表</param>
        /// <returns>动态变更原因列表</returns>
        private List<ApplGtisInfo_OUT_ChangeReason> GetDynamicServiceChangeReasons(List<ContractProductInfo> availableProducts)
        {
            var dynamicReasons = new List<ApplGtisInfo_OUT_ChangeReason>();
            var productTypes = availableProducts.Select(p => p.ProductType).Distinct().ToList();

            // 检查开通SaleWits：为每个SalesWits产品(10)生成独立的变更原因
            var salesWitsProducts = availableProducts.Where(p => p.ProductType == EnumProductType.SalesWits.ToInt()).ToList();
            foreach (var product in salesWitsProducts)
            {
                dynamicReasons.Add(new ApplGtisInfo_OUT_ChangeReason
                {
                    ChangeReasonEnum = EnumGtisServiceChangeProject.OpenSaleWits,
                    ChangeReasonEnumName = EnumGtisServiceChangeProject.OpenSaleWits.GetEnumDescription(),
                    ContractProductId = product.ContractProductId,
                    ProductName = product.ProductName
                });
            }

            // 检查SalesWits充值：为每个充值产品(11)生成独立的变更原因
            var addCreditProducts = availableProducts.Where(p => p.ProductType == EnumProductType.AddCredit.ToInt()).ToList();
            foreach (var product in addCreditProducts)
            {
                dynamicReasons.Add(new ApplGtisInfo_OUT_ChangeReason
                {
                    ChangeReasonEnum = EnumGtisServiceChangeProject.SalesWitsRecharge,
                    ChangeReasonEnumName = EnumGtisServiceChangeProject.SalesWitsRecharge.GetEnumDescription(),
                    ContractProductId = product.ContractProductId,
                    ProductName = product.ProductName
                });
            }

            // 检查SalesWits新增子账号：为每个新增资源产品(12)生成独立的变更原因
            var additionalResourceProducts = availableProducts.Where(p => p.ProductType == EnumProductType.AdditionalResource.ToInt()).ToList();
            foreach (var product in additionalResourceProducts)
            {
                dynamicReasons.Add(new ApplGtisInfo_OUT_ChangeReason
                {
                    ChangeReasonEnum = EnumGtisServiceChangeProject.SalesWitsAddAccount,
                    ChangeReasonEnumName = EnumGtisServiceChangeProject.SalesWitsAddAccount.GetEnumDescription() + "("+(product.SubAccountsNum??0)+"个)",
                    ContractProductId = product.ContractProductId,
                    ProductName = product.ProductName
                });
            }

            return dynamicReasons;
        }




    }

    /// <summary>
    /// 合同产品信息
    /// </summary>
    public class ContractProduct_Info
    {
        public string ProductId { get; set; }
        public int ProductType { get; set; }
        public string ProductName { get; set; }
        public int? ApplState { get; set; }
        public int? ServiceState { get; set; }
    }

    /// <summary>
    /// 合同产品信息（用于动态变更原因判断）
    /// </summary>
    public class ContractProductInfo
    {
        /// <summary>
        /// 合同产品ID
        /// </summary>
        public string ContractProductId { get; set; }

        /// <summary>
        /// 合同ID
        /// </summary>
        public string ContractId { get; set; }

        /// <summary>
        /// 产品ID
        /// </summary>
        public string ProductId { get; set; }

        /// <summary>
        /// 产品类型
        /// </summary>
        public int ProductType { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 申请状态
        /// </summary>
        public int? ApplState { get; set; }

        /// <summary>
        /// 服务状态
        /// </summary>
        public int? ServiceState { get; set; }

        /// <summary>
        /// 是否为主合同的产品
        /// </summary>
        public bool IsMainContract { get; set; }

        /// <summary>
        /// 子账号数
        /// </summary>
        public int? SubAccountsNum { get; set; }
    }
}
