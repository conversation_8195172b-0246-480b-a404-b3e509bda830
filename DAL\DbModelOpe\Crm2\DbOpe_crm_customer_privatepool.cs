using CRM2_API.BLL;
using CRM2_API.BLL.RemindInfo;
using CRM2_API.BLL.TrackingRecord;
using CRM2_API.Common.Cache;
using CRM2_API.DAL.DbCommon;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModel.Gtis;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.ControllersViewModel.TrackingRecord;
using CRM2_API.Model.System;
using DocumentFormat.OpenXml.ExtendedProperties;
using DocumentFormat.OpenXml.Presentation;
using DocumentFormat.OpenXml.Spreadsheet;
using JiebaNet.Segmenter.Common;
using NPOI.HSSF.Record.Aggregates;
using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Logical;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using Parlot.Fluent;
using Quartz;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using static CRM2_API.Common.Cache.RedisCache;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.BLLModel.Enum.MessageCenterEnumOption;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;

namespace CRM2_API.DAL.DbModelOpe.Crm2
{
    /// <summary>
    /// crm_customer_privatepool表操作
    /// </summary>
    public class DbOpe_crm_customer_privatepool : DbOperateCrm2Ex<Db_crm_customer_privatepool, DbOpe_crm_customer_privatepool>
    {
        public List<string> GetPrivateCustomerIdsByContractNum(string userId, string ContractNum)
        {
            var list = Db.Queryable<Db_crm_contract>()
                .LeftJoin<Db_crm_customer_subcompany>((r, cs) => r.FirstParty == cs.Id)
                .LeftJoin<Db_crm_customer_privatepool>((r, cs, p) => cs.CustomerId == p.CustomerId)
                .Where((r, cs, p) => r.Deleted == false && cs.Deleted == (int)EnumCustomerDel.NotDel)
                .Where((r, cs, p) => p.UserId == userId)
                .Where((r, cs, p) => cs.IsHistory == false)
                .Where((r, cs, p) => p.State == (int)EnumCustomerPrivateRelease.Not)
                .Where((r, cs, p) => r.ContractNum == ContractNum)
                .Select((r, cs, p) => p.CustomerId)
                .ToList();
            return list;
        }

        public List<GetContractNumList_Out> GetPrivateCustomerIdListByContractNum(string userId, string ContractNum)
        {
            var list = Db.Queryable<Db_crm_contract>()
                .LeftJoin<Db_crm_customer_subcompany>((r, cs) => r.FirstParty == cs.Id)
                .LeftJoin<Db_crm_customer_privatepool>((r, cs, p) => cs.CustomerId == p.CustomerId)
                .Where((r, cs, p) => r.Deleted == false && cs.Deleted == (int)EnumCustomerDel.NotDel)
                .Where((r, cs, p) => p.UserId == userId)
                .Where((r, cs, p) => cs.IsHistory == false)
                .Where((r, cs, p) => p.State == (int)EnumCustomerPrivateRelease.Not)
                .Where((r, cs, p) => r.ContractNum == ContractNum)
                .Where((r, cs, p) => r.ContractType != (int)EnumContractType.AddItem)
                .Select((r, cs, p) => new GetContractNumList_Out
                {
                    ContractId = r.Id,
                    ContractNum = r.ContractNum,
                    CustomerId = p.CustomerId,
                    FirstParty = r.FirstParty,
                    FirstPartyName = cs.CompanyName
                })
                .ToList();
            return list;
        }

        public List<GetContractNumList_Out> GetPrivateCustomerIdsByUserId(string userId)
        {
            var list = Db.Queryable<Db_crm_contract>()
                .LeftJoin<Db_crm_customer_subcompany>((r, cs) => r.FirstParty == cs.Id)
                .LeftJoin<Db_crm_customer_privatepool>((r, cs, p) => cs.CustomerId == p.CustomerId)
                .Where((r, cs, p) => r.Deleted == false && cs.Deleted == (int)EnumCustomerDel.NotDel)
                .Where((r, cs, p) => p.UserId == userId)
                .Where((r, cs, p) => cs.IsHistory == false)
                .Where((r, cs, p) => p.State == (int)EnumCustomerPrivateRelease.Not)
                .Where((r, cs, p) => !SqlFunc.IsNullOrEmpty(r.ContractNum))
                //.Where((r, cs, p) => r.ContractNum != null && r.ContractNum != "")
                .Where((r, cs, p) => SqlFunc.Subqueryable<Db_crm_contract>().Where((c) => c.RenewalContractNum == r.ContractNum && c.ContractStatus != 7 && c.Deleted == false && c.NewContractId == null).NotAny())
                .OrderByDescending((r, cs, p) => r.SigningDate)
                .Select((r, cs, p) => new GetContractNumList_Out
                {
                    ContractId = r.Id,
                    ContractNum = r.ContractNum,
                    CustomerId = p.CustomerId,
                    FirstPartyName = cs.CompanyName,
                    FirstParty = r.FirstParty
                })
                .ToList();
            return list;
        }

        public List<GetContractNumList_Out> GetPrivateCustomerIdsByUserIdNoSelf(string userId, string ContractId)
        {
            var list = Db.Queryable<Db_crm_contract>()
                .LeftJoin<Db_crm_customer_subcompany>((r, cs) => r.FirstParty == cs.Id)
                .LeftJoin<Db_crm_customer_privatepool>((r, cs, p) => cs.CustomerId == p.CustomerId)
                .Where((r, cs, p) => r.Deleted == false && cs.Deleted == (int)EnumCustomerDel.NotDel)
                .Where((r, cs, p) => p.UserId == userId)
                .Where((r, cs, p) => cs.IsHistory == false)
                .Where((r, cs, p) => p.State == (int)EnumCustomerPrivateRelease.Not)
                .Where((r, cs, p) => !SqlFunc.IsNullOrEmpty(r.ContractNum))
                //.Where((r, cs, p) => r.ContractNum != null && r.ContractNum != "")
                .Where((r, cs, p) => SqlFunc.Subqueryable<Db_crm_contract>().Where((c) => c.Id != ContractId && c.RenewalContractNum == r.ContractNum && c.ContractStatus != 7 && c.Deleted == false && c.NewContractId == null).NotAny())
                .Select((r, cs, p) => new GetContractNumList_Out
                {
                    ContractId = r.Id,
                    ContractNum = r.ContractNum,
                    CustomerId = p.CustomerId,
                    FirstPartyName = cs.CompanyName,
                    FirstParty = r.FirstParty
                })
                .ToList();
            return list;
        }

        public int GetProtectWarningDays()
        {
            return 7;
        }
        public int GetServiceWarningDays()
        {
            return 15;
        }
        /// <summary>
        /// 补齐私有池60天保护期
        /// </summary>
        /// <param name="customerId"></param>
        /// <param name="days"></param>
        public void FillInDays(string customerId, int days = 60)
        {
            var dtDay = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd"));
            PrivateCustomerSimpleInfo poolData = null;
            if (CheckInPool(customerId, ref poolData))
            {
                if (poolData.ProtectionDeadline != null && (poolData.ProtectionDeadline.Value - dtDay).TotalDays < days)
                {
                    poolData.ProtectionDeadline = dtDay.AddDays(days);
                    Update(poolData);
                }
            }
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="enumRemindType"></param>
        /// <returns></returns>
        public ISugarQueryable<PrivateCustomerSimpleInfo> ProtectNearlyReleaseQuery(string userId, EnumRemindType enumRemindType)
        {
            var dtDayEnd = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd 23:59:59"));
            var dtDay = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd"));
            var query = Queryable
                .LeftJoin<Db_crm_customer_subcompany>((privatePool, company) => privatePool.CustomerId == company.CustomerId && company.IsMain == (int)EnumCustomerCompanyMain.Main)
                .LeftJoin<Db_crm_customer>((privatePool, company, customer) => company.CustomerId == customer.Id)
                .LeftJoin<Db_sys_user>((privatePool, company, customer, user) => privatePool.UserId == user.Id)
                .LeftJoin<Db_v_customer_service_nearlyend>((privatePool, company, customer, user, serviceEnd) => company.CustomerId == serviceEnd.CustomerId)
                .Where((privatePool, company, customer, user, serviceEnd) => privatePool.UserId == userId)
                .Where((privatePool, company, customer, user, serviceEnd) => privatePool.State == (int)EnumCustomerPrivateRelease.Not)
                .Where((privatePool, company, customer, user, serviceEnd) => privatePool.Deleted == (int)EnumCustomerDel.NotDel
                    && company.Deleted == (int)EnumCustomerDel.NotDel
                    && customer.Deleted == (int)EnumCustomerDel.NotDel)
                .Where((privatePool, company, customer, user, serviceEnd) => company.IsHistory == false && customer.IsHistory == false)
                .Where((privatePool, company, customer, user, serviceEnd) => company.IsValid == (int)EnumCompanyValid.VALID && customer.IsValid == (int)EnumCustomerValid.VALID)
                .WhereIF(enumRemindType == EnumRemindType.SaveCustomerNearlyRelease, (privatePool, company, customer, user, serviceEnd) =>
                    customer.TrackingStage != (int)EnumTrackingStage.Received
                    && privatePool.ProtectionDeadline != null
                    && SqlFunc.DateDiff(DateType.Day, dtDay, privatePool.ProtectionDeadline.Value) <= GetProtectWarningDays())
                .WhereIF(enumRemindType == EnumRemindType.SignCustomerNearlyRelease, (privatePool, company, customer, user, serviceEnd) =>
                    customer.TrackingStage == (int)EnumTrackingStage.Received
                    && privatePool.ProtectionDeadline != null
                    && SqlFunc.DateDiff(DateType.Day, dtDay, privatePool.ProtectionDeadline.Value) <= GetProtectWarningDays())
                .WhereIF(enumRemindType == EnumRemindType.ServiceNearlyEnd, (privatePool, company, customer, user, serviceEnd) =>
                   serviceEnd.ServiceCycleEnd != null
                    && SqlFunc.DateDiff(DateType.Day, dtDay, serviceEnd.ServiceCycleEnd.Value) <= GetServiceWarningDays()
                    )
                //增加Ignore操作,忽略的内容不进行查询
                .WhereIF(enumRemindType == EnumRemindType.SaveCustomerNearlyRelease, (privatePool, company, customer, user, serviceEnd) =>
                    !SqlFunc.Subqueryable<Db_sys_message_ignore>()
                        .Where(i => i.IgnoreMessageLocalType == (int)EnumMessageLocalType.SaveCustomerNearlyRelease)
                        .Where(i => i.IsDelete == 0)
                        .Where(i => i.UserId == user.Id)
                        .Where(i => i.IgnoreMessageTypeToId == customer.Id).Any()
                    )
                .WhereIF(enumRemindType == EnumRemindType.SignCustomerNearlyRelease, (privatePool, company, customer, user, serviceEnd) =>
                    !SqlFunc.Subqueryable<Db_sys_message_ignore>()
                        .Where(i => i.IgnoreMessageLocalType == (int)EnumMessageLocalType.SignCustomerNearlyRelease)
                        .Where(i => i.IsDelete == 0)
                        .Where(i => i.UserId == user.Id)
                        .Where(i => i.IgnoreMessageTypeToId == customer.Id).Any()
                    )
                .WhereIF(enumRemindType == EnumRemindType.ServiceNearlyEnd, (privatePool, company, customer, user, serviceEnd) =>
                    !SqlFunc.Subqueryable<Db_sys_message_ignore>()
                        .Where(i => i.IgnoreMessageLocalType == (int)EnumMessageLocalType.ServiceNearlyEnd)
                        .Where(i => i.IsDelete == 0)
                        .Where(i => i.UserId == user.Id)
                        .Where(i => i.IgnoreMessageTypeToId == customer.Id).Any()
                    )
                .Select((privatePool, company, customer, user, serviceEnd) => new PrivateCustomerSimpleInfo()
                {
                    UserName = user.Name,
                    MainCompanyId = company.Id,
                    CustomerName = company.CompanyName,
                    CustomerNum = customer.CustomerNum
                }, true).MergeTable();
            return query;
        }
        /// <summary>
        /// 检测客户保护时间,超时的自动释放到公有池(11.9 有审核中的不释放)
        /// 20240722 自动释放到临时池
        /// 20240806 到账客户不释放（只有进了保留库 再到期才释放） 
        /// </summary>
        public void ExcuteProtectCheck()
        {
            var user = "";
            DateTime dt = DateTime.Now;
            TransDeal(() =>
            {
                var blackkUserList = Db.Queryable<Db_crm_customer_blacklistuser>().Where(e => e.Deleted == false).Select(e => e.UserId).ToList();
                var query = Queryable
                 .LeftJoin<Db_crm_customer>((privatePool, customer) => privatePool.CustomerId == customer.Id)
                 .LeftJoin<Db_crm_customer_subcompany>((privatePool, customer, company) => privatePool.CustomerId == company.CustomerId && company.IsMain == (int)EnumCustomerCompanyMain.Main)
                 .LeftJoin<Db_sys_user>((privatePool, customer, company, user) => user.Id == privatePool.UserId)
                 .Where((privatePool, customer, company, user) => privatePool.State == (int)EnumCustomerPrivateRelease.Not)
                 .Where((privatePool, customer, company, user) => customer.IsMerge == (int)EnumCustomerMerge.NotMerge)
                 // 20240806 到账客户不释放（只有进了保留库 再到期才释放） 
                 .Where((privatePool, customer, company, user) => customer.TrackingStage != (int)EnumTrackingStage.Received)
                 .Where((privatePool, customer, company, user) => privatePool.Deleted == (int)EnumCustomerDel.NotDel && customer.Deleted == (int)EnumCustomerDel.NotDel && company.Deleted == (int)EnumCustomerDel.NotDel)
                 .Where((privatePool, customer, company, user) => privatePool.ProtectionDeadline < dt)
                 .WhereIF((blackkUserList != null || blackkUserList.Count > 0), privatePool => !blackkUserList.Contains(privatePool.UserId))
                 .Select((privatePool, customer, company, user) => privatePool)
                 .ToList();
                var customerIds = query.Select(q => q.CustomerId).Distinct().ToList();
                var lockList = DbOpe_crm_customer.Instance.CheckCustomerLock(customerIds, false);
                var auditList = BLL_Customer.Instance.CheckAuditInProcess(customerIds, false);
                var opList = new List<string>();
                foreach (var customerId in customerIds)
                {
                    if (!lockList.Contains(customerId) && !auditList.Contains(customerId))
                    {
                        opList.Add(customerId);
                    }
                }
                foreach (var customerId in opList)
                {
                    LogUtil.AddLog(customerId + "过期自动释放");
                    try
                    {
                        TransDeal(() =>
                        {
                            var userId = query.Find(c => c.CustomerId == customerId)?.UserId;
                            if (userId != null)
                            {
                                MoveToTemp(new List<string>() { customerId }, userId, true, true);
                                DbOpe_crm_customer_org_log.Instance.ReleaseCustomerLog(customerId);
                            }

                        });
                    }
                    catch (Exception e)
                    {
                        LogUtil.AddLog(customerId + "过期自动释放失败：" + e.Message);
                    }
                }

            });
        }
        /// <summary>
        /// 获取员工离职后客户保留天数
        /// </summary>
        private int GetLeaveUserProtectDay()
        {
            return 7;
        }

        /// <summary>
        /// 检测离职员工的客户，是否已经在规定时间内分配完毕，如果未分配完毕，则自动释放到公有池
        /// </summary>
        public void ExcuteLeaveUserProtectCheck()
        {
            LogUtil.AddLog($"定时任务执行：ExcuteLeaveUserProtectCheck");
            var user = "";
            DateTime dt = DateTime.Now;
            TransDeal(() =>
            {
                var query = Queryable
                 .LeftJoin<Db_crm_customer>((privatePool, customer) => privatePool.CustomerId == customer.Id)
                 .LeftJoin<Db_crm_customer_subcompany>((privatePool, customer, company) => privatePool.CustomerId == company.CustomerId && company.IsMain == (int)EnumCustomerCompanyMain.Main)
                 .LeftJoin<Db_sys_user>((privatePool, customer, company, user) => user.Id == privatePool.UserId)
                 .Where((privatePool, customer, company, user) => privatePool.State == (int)EnumCustomerPrivateRelease.Not)
                 .Where((privatePool, customer, company, user) => customer.IsMerge == (int)EnumCustomerMerge.NotMerge)
                 .Where((privatePool, customer, company, user) => privatePool.Deleted == (int)EnumCustomerDel.NotDel && customer.Deleted == (int)EnumCustomerDel.NotDel && company.Deleted == (int)EnumCustomerDel.NotDel)
                 .Where((privatePool, customer, company, user) => user.UpdateDate != null && SqlFunc.DateDiff(DateType.Day, user.UpdateDate.Value, dt) > GetLeaveUserProtectDay())
                 .Where((privatePool, customer, company, user) => !user.UserStatus)
                 .Select((privatePool, customer, company, user) => privatePool)
                 .ToList();
                var customerIds = query.Select(q => q.CustomerId).Distinct().ToList();
                BLL_Customer.Instance.CancelAuditInProcess(customerIds, false);
                foreach (var customerId in customerIds)
                {
                    LogUtil.AddLog(customerId + "过期自动释放");
                    try
                    {
                        TransDeal(() =>
                        {
                            var userId = query.Find(c => c.CustomerId == customerId)?.UserId;
                            if (userId != null)
                            {
                                MoveToTemp(new List<string>() { customerId }, userId, true, true);
                                DbOpe_crm_customer_org_log.Instance.ReleaseCustomerLog(customerId);
                            }

                        });
                    }
                    catch (Exception e)
                    {
                        LogUtil.AddLog(customerId + "过期自动释放失败：" + e.Message);
                    }
                }

            });
        }


        /// <summary>
        /// 验证用户私有客户保留数
        /// </summary>
        /// <exception cref="Exception"></exception>
        public void CheckMaxSavePrivateCustomer(string userId)
        {
            if (GetMaxSavePrivateCustomerNumLeft(userId) <= 0)
            {
                throw new ApiException("已超过可保留客户数");
            }
        }
        /// <summary>
        /// 获取用户私有客户保留数
        /// </summary>
        /// <exception cref="Exception"></exception>
        public int GetMaxSavePrivateCustomerNumLeft(string userId)
        {
            var user = DbOpe_sys_user.Instance.GetDataById(userId);
            var userMaxSaveCustomer = Math.Max(user.MaxSaveCustomer, user.MaxSaveCustomerMannualInput);
            return userMaxSaveCustomer - GetPrivateSaveCustomerQuery(userId).MergeTable().Select(p => p.CustomerId).Distinct().Count();
        }
        /// <summary>
        /// 获取当前保留(且未签约)客户数（未签合同或者合同超过保护期）  （到账的才能不占用保留客户数）  20240712 保密的到账也站名额
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public ISugarQueryable<Db_crm_customer_privatepool> GetPrivateSaveCustomerQuery(string userId = "")
        {
            var dtDay = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd"));
            return Queryable
             .LeftJoin<Db_crm_customer>((privatePool, customer) => privatePool.CustomerId == customer.Id)
             .LeftJoin<Db_v_contract_secret>((privatePool, customer, secret) => privatePool.CustomerId == secret.CustomerId)
             .Where((privatePool, customer, secret) => privatePool.State == (int)EnumCustomerPrivateRelease.Not)
             .Where((privatePool, customer, secret) => customer.IsMerge == (int)EnumCustomerMerge.NotMerge)
             .Where((privatePool, customer, secret) => privatePool.Deleted == (int)EnumCustomerDel.NotDel && customer.Deleted == (int)EnumCustomerDel.NotDel)
             .WhereIF(!SqlFunc.IsNullOrEmpty(userId), (privatePool, customer, secret) => privatePool.UserId == userId)
             //.Where((privatePool, customer, secret)=> customer.TrackingStage != (int)EnumTrackingStage.SignedContract && customer.TrackingStage != (int)EnumTrackingStage.Received)
             //4.26 到账的才能不占用保留客户数
             .Where((privatePool, customer, secret) => (customer.TrackingStage != (int)EnumTrackingStage.Received)
             || (customer.TrackingStage == (int)EnumTrackingStage.Received && !SqlFunc.IsNullOrEmpty(secret.CustomerId)))
             .Select((privatePool, customer, secret) => privatePool);
        }

        /// <summary>
        /// 获取当前保留(且未签约)客户数（未签合同或者合同超过保护期）  （到账的才能不占用保留客户数）  20240712 保密的到账也站名额
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public ISugarQueryable<Db_crm_customer_privatepool> GetPrivateSaveCustomerQueryNoSecret(string userId = "")
        {
            var dtDay = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd"));
            return Queryable
             .LeftJoin<Db_crm_customer>((privatePool, customer) => privatePool.CustomerId == customer.Id)
             .Where((privatePool, customer) => privatePool.State == (int)EnumCustomerPrivateRelease.Not)
             .Where((privatePool, customer) => customer.IsMerge == (int)EnumCustomerMerge.NotMerge)
             .Where((privatePool, customer) => privatePool.Deleted == (int)EnumCustomerDel.NotDel && customer.Deleted == (int)EnumCustomerDel.NotDel)
             .WhereIF(!SqlFunc.IsNullOrEmpty(userId), (privatePool, customer) => privatePool.UserId == userId)
             .Where((privatePool, customer) => (customer.TrackingStage != (int)EnumTrackingStage.Received)
             || (customer.TrackingStage == (int)EnumTrackingStage.Received))
             .Select((privatePool, customer) => privatePool);
        }

        /// <summary>
        /// 获取当前签约的客户数（签合同且合同在保护期内）
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public int GetSignPrivateCustomerNum(string userId)
        {
            // var dtDay = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd"));
            return Queryable
             .LeftJoin<Db_crm_customer>((privatePool, customer) => privatePool.CustomerId == customer.Id)
             .Where((privatePool, customer) => privatePool.State == (int)EnumCustomerPrivateRelease.Not)
             .Where((privatePool, customer) => customer.IsMerge == (int)EnumCustomerMerge.NotMerge)
             .Where((privatePool, customer) => privatePool.Deleted == (int)EnumCustomerDel.NotDel && customer.Deleted == (int)EnumCustomerDel.NotDel)
             .Where((privatePool, customer) => privatePool.UserId == userId)
             .Where((privatePool, customer) => customer.TrackingStage == (int)EnumTrackingStage.SignedContract || customer.TrackingStage == (int)EnumTrackingStage.Received)
             .Select((privatePool, customer) => privatePool)
             .Count();
        }
        /// <summary>
        /// 获取当前到账的客户数
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public int GetRecievePrivateCustomerNum(string userId)
        {
            var dtDay = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd"));
            return Queryable
             .LeftJoin<Db_crm_customer>((privatePool, customer) => privatePool.CustomerId == customer.Id)
             .Where((privatePool, customer) => privatePool.State == (int)EnumCustomerPrivateRelease.Not)
             .Where((privatePool, customer) => customer.IsMerge == (int)EnumCustomerMerge.NotMerge)
             .Where((privatePool, customer) => privatePool.Deleted == (int)EnumCustomerDel.NotDel && customer.Deleted == (int)EnumCustomerDel.NotDel)
             .Where((privatePool, customer) => privatePool.UserId == userId)
             .Where((privatePool, customer) => customer.TrackingStage == (int)EnumTrackingStage.Received)
             .Select((privatePool, customer) => privatePool)
             .Count();
        }

        public bool checkFreeze(string customerId, string userId)
        {
            var Freeze = false;
            var disableDays = GetCustomerDisableDays();
            int leftDay = 0;
            var notRelease = false;
            PrivateCustomerSimpleInfo lastRecord = new PrivateCustomerSimpleInfo();
            //获取私有池历史
            var hasHistory = DbOpe_crm_customer_privatepool.Instance.CheckUserCustomerHasHistory(customerId, userId, ref leftDay, ref lastRecord, ref notRelease);
            if (hasHistory)
            {
                //验证冻结时间
                if (lastRecord.ReleaseTime != null && DateTime.Now.Subtract(lastRecord.ReleaseTime.Value).Days < disableDays)
                {
                    Freeze = true;
                }
            }
            return Freeze;
        }
        /// <summary>
        /// 获取系统冻结时间
        /// </summary>
        /// <returns></returns>
        private int GetCustomerDisableDays()
        {
            return DbOpe_sys_comparam.Instance.GetComparams("PrivateReleaseClaimDays");
        }
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public Dictionary<string, GetMaxSavePrivateCustomerNumLeft_OUT> GetMaxSavePrivateCustomerNumLeft()
        {
            var dtDay = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd"));
            var r = GetPrivateSaveCustomerQueryNoSecret();
            return r.MergeTable()
                .GroupBy(privatePool => privatePool.UserId)
                .Select(privatePool => new
                {
                    UserId = privatePool.UserId,
                    SaveCustomerNumber = SqlFunc.AggregateCount(privatePool.Id)
                })
                .MergeTable()
                .RightJoin<Db_sys_user>((t, su) => t.UserId.Equals(su.Id) && su.Deleted == false && su.UserStatus == true)
                .Select((t, su) => new GetMaxSavePrivateCustomerNumLeft_OUT()
                {
                    UserId = su.Id,
                    LeftCustomerNumber =
                    //SqlFunc.IF(SqlFunc.IsNullOrEmpty(t.SaveCustomerNumber)&&!SqlFunc.HasNumber(su.MaxSaveCustomerMannualInput))
                    //.Return(su.MaxSaveCustomer)
                    //.ElseIF(!SqlFunc.IsNullOrEmpty(t.SaveCustomerNumber) && !SqlFunc.HasNumber(su.MaxSaveCustomerMannualInput))
                    //.Return(su.MaxSaveCustomer - t.SaveCustomerNumber)
                    ////下面都是hasnumber
                    SqlFunc.IF(SqlFunc.IsNullOrEmpty(t.SaveCustomerNumber) && su.MaxSaveCustomer >= su.MaxSaveCustomerMannualInput)
                    .Return(su.MaxSaveCustomer)
                    .ElseIF(!SqlFunc.IsNullOrEmpty(t.SaveCustomerNumber) && su.MaxSaveCustomer >= su.MaxSaveCustomerMannualInput)
                    .Return(su.MaxSaveCustomer - t.SaveCustomerNumber)
                    //下面都是 su.MaxSaveCustomer < su.MaxSaveCustomerMannualInput
                    .ElseIF(!SqlFunc.IsNullOrEmpty(t.SaveCustomerNumber))
                    .Return(su.MaxSaveCustomerMannualInput - t.SaveCustomerNumber)
                    .End(su.MaxSaveCustomerMannualInput)
                }).ToList().ToDictionary(k => k.UserId, v => v);
        }
        /// <summary>
        /// 获取用户当日已领取数
        /// </summary>
        /// <exception cref="Exception"></exception>
        public int GetTodayGetCustomerNum(string userId)
        {
            var query = Queryable
                 .Where(c => c.Deleted == (int)EnumCustomerDel.NotDel)
                 .Where(c => c.UserId == userId)
                 .Where(c => c.CreateType == (int)EnumCustomerPrivateCreateType.GetPublic)
                 .Where(c => c.CollectionTime != null && SqlFunc.DateIsSame(c.CollectionTime, DateTime.Now))
                 .Count();
            return query;
        }
        /// <summary>
        /// 检查客户是否在池子里
        /// </summary>
        /// <param name="customerId"></param>
        /// <param name="userId"></param>
        /// <param name="poolData"></param>
        /// <param name="containsDeleted"></param>
        /// <param name="containsRelease"></param>
        /// <returns></returns>
        public bool CheckInPool(string customerId, ref PrivateCustomerSimpleInfo poolData, string userId = "", bool containsDeleted = false, bool containsRelease = false)
        {
            poolData = Queryable
                .LeftJoin<Db_crm_customer_subcompany>((privatePool, company) => privatePool.CustomerId == company.CustomerId && company.IsMain == (int)EnumCustomerCompanyMain.Main)
                .LeftJoin<Db_crm_customer>((privatePool, company, customer) => company.CustomerId == customer.Id)
                .LeftJoin<Db_sys_user>((privatePool, company, customer, user) => privatePool.UserId == user.Id)
                .Where((privatePool, company, customer, user) => privatePool.CustomerId == customerId)
                .WhereIF(!StringUtil.IsNullOrEmpty(userId), (privatePool, company, customer, user) => privatePool.UserId == userId)
                .WhereIF(!containsDeleted, (privatePool, company, customer, user) => privatePool.Deleted == (int)EnumCustomerDel.NotDel)
                .WhereIF(!containsRelease, (privatePool, company, customer, user) => privatePool.State == (int)EnumCustomerPrivateRelease.Not)
                .Select((privatePool, company, customer, user) => new PrivateCustomerSimpleInfo()
                {
                    Id = privatePool.Id,
                    UserName = user.Name,
                    MainCompanyId = company.Id,
                    CustomerName = company.CompanyName,
                    CustomerNum = customer.CustomerNum
                }, true).First();
            return poolData != null;
        }
        /// <summary>
        /// 批量检查客户是否在池子里
        /// </summary>
        /// <param name="customerIds"></param>
        /// <param name="notInCustomerIds"></param>
        /// <param name="userId"></param>
        /// <param name="containsDeleted"></param>
        /// <param name="containsRelease"></param>
        /// <returns></returns>
        public bool CheckInPool(List<string> customerIds, ref List<string> notInCustomerIds, string userId = "", bool containsDeleted = false, bool containsRelease = false)
        {
            var poolDatas = Queryable
                .LeftJoin<Db_crm_customer_subcompany>((privatePool, company) => privatePool.CustomerId == company.CustomerId && company.IsMain == (int)EnumCustomerCompanyMain.Main)
                .LeftJoin<Db_crm_customer>((privatePool, company, customer) => company.CustomerId == customer.Id)
                .LeftJoin<Db_sys_user>((privatePool, company, customer, user) => privatePool.UserId == user.Id)
                .Where((privatePool, company, customer, user) => SqlFunc.ContainsArray(customerIds, privatePool.CustomerId))
                .WhereIF(!StringUtil.IsNullOrEmpty(userId), (privatePool, company, customer, user) => privatePool.UserId == userId)
                .WhereIF(!containsDeleted, (privatePool, company, customer, user) => privatePool.Deleted == (int)EnumCustomerDel.NotDel)
                .WhereIF(!containsRelease, (privatePool, company, customer, user) => privatePool.State == (int)EnumCustomerPrivateRelease.Not)
                .Select((privatePool, company, customer, user) => customer.Id)
                .Distinct()
                .ToList();
            notInCustomerIds = customerIds.MappingTo<List<string>>();
            notInCustomerIds.RemoveAll(c => poolDatas.Contains(c));
            return notInCustomerIds.Count == 0;
        }
        /// <summary>
        /// 检查客户是否在池子里
        /// </summary>
        /// <param name="companyName"></param>
        /// <param name="poolData"></param>
        /// <returns></returns>
        public bool CheckInPool(string companyName, ref CheckInPrivatePoolData poolData)
        {
            poolData = Queryable
                .LeftJoin<Db_crm_customer_subcompany>((privatePool, company) => privatePool.CustomerId == company.CustomerId)
                .LeftJoin<Db_crm_customer>((privatePool, company, customer) => company.CustomerId == customer.Id)
                .LeftJoin<Db_sys_user>((privatePool, company, customer, user) => privatePool.UserId == user.Id)
                .Where((privatePool, company, customer, user) => company.CompanyName == companyName)
                .Where((privatePool, company, customer, user) => company.Deleted == (int)EnumCustomerDel.NotDel &&
                        company.IsHistory == false &&
                        company.IsValid == (int)EnumCompanyValid.VALID)
                .Where((privatePool, company, customer, user) => privatePool.Deleted == (int)EnumCustomerDel.NotDel)
                .Where((privatePool, company, customer, user) => privatePool.State == (int)EnumCustomerPrivateRelease.Not)
                .Select((privatePool, company, customer, user) => new CheckInPrivatePoolData()
                {
                    UserId = privatePool.UserId,
                    CompanyId = company.Id,
                }, true).First();
            return poolData != null;
        }
        /// <summary>
        /// 根据查询条件获取用户私有池客户信息列表
        /// </summary>
        /// <param name="queryCustomer_IN"></param>
        /// <param name="userId"></param>
        /// <param name="total"></param>
        public List<QueryCustomer_OUT> SearchPrivatePoolCustomerList(QueryCustomer_IN queryCustomer_IN, string userId, ref int total)
        {
            int pD = DbOpe_crm_customer_privatepool.Instance.GetProtectWarningDays();
            int sD = DbOpe_crm_customer_privatepool.Instance.GetServiceWarningDays();
            var maxDelayTimes = GetMaxDelayTimes();
            var dt = DateTime.Now;
            var userInfo = DbOpe_sys_user.Instance.GetUserById(userId);
            var dtDay = DateTime.Parse(dt.ToString("yyyy-MM-dd"));
            var dtDayEnd = DateTime.Parse(dt.ToString("yyyy-MM-dd 23:59:59"));
            var weekEnd = dt.AddDays(1 - Convert.ToInt32(dt.DayOfWeek.ToString("d"))).AddDays(6).ToString("yyyy-MM-dd");
            var weekStart = dt.AddDays(1 - Convert.ToInt32(dt.DayOfWeek.ToString("d"))).ToString("yyyy-MM-dd");

            #region 处理首页跳转过来的时间条件
            DateSpanParams indexP = null;
            if (queryCustomer_IN.EnumIndexRedirectType == EnumIndexRedirectType.Span_SignCustomer)
            {
                indexP = BLL_Statistics.Instance.TransUserIndexStatisticsDate(new UserIndexStatisticsDate()
                {
                    EnumStatisticsDateSpanSelect = queryCustomer_IN.EnumStatisticsDateSpanSelect,
                    SelectYear = queryCustomer_IN.SelectYear
                });
            }
            #endregion

            bool noProductsStateQuery = true;

            List<QueryCustomer_OUT> r = new List<QueryCustomer_OUT>();
            r = Queryable
                .LeftJoin<Db_crm_customer_subcompany>((privatePool, company) => privatePool.CustomerId == company.CustomerId && company.IsMain == (int)EnumCustomerCompanyMain.Main)
                .LeftJoin<Db_crm_customer>((privatePool, company, customer) => company.CustomerId == customer.Id)
                .LeftJoinIF<Db_v_customerproductserviceinfostatus>(!noProductsStateQuery, (privatePool, company, customer, productstate) => company.CustomerId == productstate.CustomerId)
                .LeftJoin<Db_v_customer_service_nearlyend>((privatePool, company, customer, productstate, serviceEnd) => company.CustomerId == serviceEnd.CustomerId)
                //.WhereIF(!string.IsNullOrEmpty(queryCustomer_IN.CustomerName), (privatePool, company, customer, productstate, serviceEnd) => company.CompanyName.Contains(queryCustomer_IN.CustomerName))
                .WhereIF(!string.IsNullOrEmpty(queryCustomer_IN.CustomerName), (privatePool, company, customer, productstate, serviceEnd) =>
                    SqlFunc.Subqueryable<Db_crm_customer_subcompany>().Where(c =>
                    c.CustomerId == company.CustomerId
                    && c.CompanyName.Contains(queryCustomer_IN.CustomerName)
                    && c.Deleted == (int)EnumCustomerDel.NotDel
                    && c.IsHistory == false
                    && c.IsValid == (int)EnumCompanyValid.VALID
                    ).Any()
                )
                //.WhereIF(!string.IsNullOrEmpty(queryCustomer_IN.Contacts), (privatePool, company, customer, productstate, serviceEnd) => company.Contacts.Contains(queryCustomer_IN.Contacts))
                .WhereIF(!string.IsNullOrEmpty(queryCustomer_IN.CustomerNum), (privatePool, company, customer, productstate, serviceEnd) => customer.CustomerNum == queryCustomer_IN.CustomerNum)
                .WhereIF(!ArrayUtil.IsNullOrEmpty(queryCustomer_IN.TrackingStage), (privatePool, company, customer, productstate, serviceEnd) => SqlFunc.ContainsArray(queryCustomer_IN.TrackingStage, customer.TrackingStage))
                .WhereIF(!ArrayUtil.IsNullOrEmpty(queryCustomer_IN.CustomerLevel), (privatePool, company, customer, productstate, serviceEnd) => SqlFunc.ContainsArray(queryCustomer_IN.CustomerLevel, customer.CustomerLevel))
                .WhereIF(!ArrayUtil.IsNullOrEmpty(queryCustomer_IN.CustomerSource), (privatePool, company, customer, productstate, serviceEnd) => SqlFunc.ContainsArray(queryCustomer_IN.CustomerSource, customer.CustomerSource))
                .WhereIF(!ArrayUtil.IsNullOrEmpty(queryCustomer_IN.PeerData), (privatePool, company, customer, productstate, serviceEnd) => SqlFunc.ContainsArray(queryCustomer_IN.PeerData, customer.PeerData))
                .WhereIF(queryCustomer_IN.Country != null, (privatePool, company, customer, productstate, serviceEnd) => company.Country == queryCustomer_IN.Country)
                .WhereIF(queryCustomer_IN.Province != null, (privatePool, company, customer, productstate, serviceEnd) => company.Province == queryCustomer_IN.Province)
                .WhereIF(queryCustomer_IN.City != null, (privatePool, company, customer, productstate, serviceEnd) => company.City == queryCustomer_IN.City)
                .WhereIF(!StringUtil.IsNullOrEmpty(queryCustomer_IN.Address), (privatePool, company, customer, productstate, serviceEnd) => SqlFunc.Contains(company.Address, queryCustomer_IN.Address))
                .WhereIF(!ArrayUtil.IsNullOrEmpty(queryCustomer_IN.ProductId), (privatePool, company, customer, productstate, serviceEnd) =>
                            SqlFunc.Subqueryable<Db_crm_contract>()
                            .LeftJoin<Db_crm_contract_productinfo>((contract, contractProduct) => contract.Id == contractProduct.ContractId)
                            .LeftJoin<Db_crm_customer_subcompany>((contract, contractProduct, firstParty) => firstParty.Id == contract.FirstParty)
                            .Where((contract, contractProduct, firstParty) =>
                            privatePool.CustomerId == firstParty.CustomerId &&
                            SqlFunc.ContainsArray(queryCustomer_IN.ProductId, contractProduct.ProductId)
                            && contractProduct.Deleted == false
                            && contract.Deleted == false)
                            .Any()
                )
                .WhereIF(!string.IsNullOrEmpty(queryCustomer_IN.Contacts), (privatePool, company, customer, productstate, serviceEnd) =>
                            SqlFunc.Subqueryable<Db_crm_customer_subcompany_contacts>().Where((contact) =>
                            SqlFunc.Contains(contact.Contacts, queryCustomer_IN.Contacts)
                            && contact.CompanyId == company.Id
                            && contact.Deleted == (int)EnumCustomerDel.NotDel)
                            .Any())
                .WhereIF(queryCustomer_IN.CustomerSign == EnumCustomerSign.SignAndRecieve, (privatePool, company, customer, productstate, serviceEnd) =>
                    customer.TrackingStage == (int)EnumTrackingStage.Received
                )
                .WhereIF(queryCustomer_IN.CustomerSign == EnumCustomerSign.Protect, (privatePool, company, customer, productstate, serviceEnd) =>
                    customer.TrackingStage != (int)EnumTrackingStage.Received
                )
                .WhereIF(!ArrayUtil.IsNullOrEmpty(queryCustomer_IN.ServiceState) && !noProductsStateQuery, (privatePool, company, customer, productstate, serviceEnd) => SqlFunc.ContainsArray(queryCustomer_IN.ServiceState, productstate.ProductServiceInfoStatus))
                .WhereIF(queryCustomer_IN.enumCustomerPrivateQueryListType == EnumCustomerPrivateQueryListType.Today, (privatePool, company, customer, productstate, serviceEnd) =>
                (privatePool.CollectionTime != null && SqlFunc.DateIsSame(privatePool.CollectionTime.Value, dtDay))
                || SqlFunc.DateIsSame(privatePool.CreateDate.Value, dtDay)
                )
                .WhereIF(queryCustomer_IN.enumCustomerPrivateQueryListType == EnumCustomerPrivateQueryListType.Week, (privatePool, company, customer, productstate, serviceEnd) =>
                (privatePool.CollectionTime != null && SqlFunc.Between(privatePool.CollectionTime.Value, weekStart, weekEnd))
                || SqlFunc.Between(privatePool.CreateDate.Value, weekStart, weekEnd)
                )
                .WhereIF(queryCustomer_IN.enumCustomerPrivateQueryListType == EnumCustomerPrivateQueryListType.NearlyRelease, (privatePool, company, customer, productstate, serviceEnd) =>
                (privatePool.ProtectionDeadline != null && SqlFunc.DateDiff(DateType.Day, dtDay, privatePool.ProtectionDeadline.Value) <= 15)
                //|| SqlFunc.DateDiff(DateType.Day, privatePool.CreateDate.Value, dtDay) <= 15
                )
                .WhereIF(queryCustomer_IN.enumCustomerPrivateQueryListType == EnumCustomerPrivateQueryListType.NotUpdate15,
                (privatePool, company, customer, productstate, serviceEnd) =>
                           !SqlFunc.Subqueryable<Db_crm_trackingrecord>()
                            .Where(item => privatePool.CustomerId == item.CustomerId
                                && privatePool.UserId == item.UserId
                                && ((item.CreateDate != null && SqlFunc.DateDiff(DateType.Day, dtDay, item.CreateDate.Value) <= 15)
                                || (item.UpdateDate != null && SqlFunc.DateDiff(DateType.Day, dtDay, item.UpdateDate.Value) <= 15))
                                && item.Deleted == false)
                            .Any()
                            &&
                            !SqlFunc.Subqueryable<Db_crm_schedule>()
                            .Where(item => privatePool.CustomerId == item.CustomerId
                                && privatePool.UserId == item.UserId
                                && ((item.CreateDate != null && SqlFunc.DateDiff(DateType.Day, dtDay, item.CreateDate.Value) <= 15)
                                || (item.UpdateDate != null && SqlFunc.DateDiff(DateType.Day, dtDay, item.UpdateDate.Value) <= 15))
                                && item.Deleted == false)
                            .Any()
                            &&
                            (privatePool.CollectionTime != null && SqlFunc.DateDiff(DateType.Day, privatePool.CollectionTime.Value, dtDay) >= 15)
                            || SqlFunc.DateDiff(DateType.Day, privatePool.CreateDate.Value, dtDay) >= 15
                )
                .Where((privatePool, company, customer, productstate, serviceEnd) => privatePool.State == (int)EnumCustomerPrivateRelease.Not)
                .Where((privatePool, company, customer, productstate, serviceEnd) =>
                    privatePool.Deleted == (int)EnumCustomerDel.NotDel
                    && company.Deleted == (int)EnumCustomerDel.NotDel
                    && customer.Deleted == (int)EnumCustomerDel.NotDel
                )
                .Where((privatePool, company, customer, productstate, serviceEnd) => customer.IsValid == (int)EnumCustomerValid.VALID)
                .Where((privatePool, company, customer, productstate, serviceEnd) => customer.IsMerge == (int)EnumCustomerMerge.NotMerge)
                .Where((privatePool, company, customer, productstate, serviceEnd) => privatePool.UserId == userId)
                .Select((privatePool, company, customer, productstate, serviceEnd) => new QueryCustomer()
                {
                    Id = customer.Id,
                    CreditCode = company.CreditCode,
                    CustomerName = company.CompanyName,
                    Contacts = company.Contacts,
                    ContactWay = company.ContactWay,
                    CustomerNum = customer.CustomerNum,
                    //ServiceState = productstate?.ProductServiceInfoStatus,
                    ProtectionDeadline = privatePool.ProtectionDeadline == null ? "" : privatePool.ProtectionDeadline.Value.ToString("yyyy-MM-dd 00:00"),
                    //CollectionTime = privatePool.CollectionTime == null ? "" : privatePool.CollectionTime.Value.ToString("yyyy-MM-dd HH:mm"),
                    CollectionTime = privatePool.CollectionTime == null ? customer.CreateDate.Value.ToString("yyyy-MM-dd HH:mm") : privatePool.CollectionTime.Value.ToString("yyyy-MM-dd HH:mm"),
                    CreateDate = customer.CreateDate == null ? "" : customer.CreateDate.Value.ToString("yyyy-MM-dd HH:mm"),
                    ProductName = "产品信息",
                    UserId = privatePool.UserId,
                    IsSupplementary = (EnumNeedSupplement)customer.IsSupplementary,
                    ProtectionDeadlineDt = privatePool.ProtectionDeadline == null ? DateTime.MaxValue : privatePool.ProtectionDeadline.Value,
                    ServiceEndDt = serviceEnd.ServiceCycleEnd == null ? DateTime.MaxValue : serviceEnd.ServiceCycleEnd.Value,
                    ServiceEnd = serviceEnd.ServiceCycleEnd == null ? "" : serviceEnd.ServiceCycleEnd.Value.ToString("yyyy-MM-dd 00:00"),
                    MainCompanyId = company.Id,
                    CreditType = company.CreditType,
                    Country = company.Country
                },
                true)
                .MergeTable()
                .WhereIF(queryCustomer_IN.CollectionTimeStart != null, it => it.CollectionTime != null && queryCustomer_IN.CollectionTimeStart != null && SqlFunc.ToDateShort(queryCustomer_IN.CollectionTimeStart.Value) <= SqlFunc.ToDateShort(it.CollectionTime))
                .WhereIF(queryCustomer_IN.CollectionTimeEnd != null, it => it.CollectionTime != null && queryCustomer_IN.CollectionTimeEnd != null && SqlFunc.ToDateShort(queryCustomer_IN.CollectionTimeEnd.Value) >= SqlFunc.ToDateShort(it.CollectionTime))
                .SelectMergeTable(mit => new QueryCustomer_OUT
                {
                    Id = mit.Id.SelectAll(),
                    NearlyRealese = SqlFunc.DateDiff(DateType.Day, dtDay, mit.ProtectionDeadlineDt) <= pD,
                    NearlyServiceEnd = SqlFunc.DateDiff(DateType.Day, dtDay, mit.ServiceEndDt) <= sD,
                    //ProtectLeftDays = SqlFunc.DateDiff(DateType.Day, dtDay, mit.ProtectionDeadlineDt),
                    //ServiceLeftDays = mit.ServiceEndDt == DateTime.MaxValue ? null : SqlFunc.DateDiff(DateType.Day, dtDay, mit.ServiceEndDt),
                    DelayTimesLeft = SqlFunc.IIF(maxDelayTimes - mit.DelayTimes > 0, maxDelayTimes - mit.DelayTimes, 0),
                    ProtectLeftDays = SqlFunc.IIF(SqlFunc.DateDiff(DateType.Day, dtDay, mit.ProtectionDeadlineDt) > 0, SqlFunc.DateDiff(DateType.Day, dtDay, mit.ProtectionDeadlineDt), 0),
                    ServiceLeftDays = SqlFunc.IIF(
                    mit.ServiceEndDt != DateTime.MaxValue && (SqlFunc.DateDiff(DateType.Day, dtDay, mit.ServiceEndDt) > 0),
                    SqlFunc.DateDiff(DateType.Day, dtDay, mit.ServiceEndDt),
                    0)
                })
                .MergeTable()
                .WhereIF(queryCustomer_IN.EnumRemindType == EnumRemindType.SaveCustomerNearlyRelease, MergeTable =>
                    MergeTable.NearlyRealese == true && MergeTable.TrackingStage != EnumTrackingStage.Received
                     && SqlFunc.Subqueryable<Db_sys_message_ignore>().Where(i => i.UserId == userId
                        && i.IgnoreMessageTypeToId == MergeTable.Id
                        && i.IgnoreMessageLocalType == (int)EnumMessageLocalType.SaveCustomerNearlyRelease
                        && i.IsDelete != 1
                        ).NotAny()
                )
                .WhereIF(queryCustomer_IN.EnumRemindType == EnumRemindType.SignCustomerNearlyRelease, MergeTable =>
                    MergeTable.NearlyRealese == true && MergeTable.TrackingStage == EnumTrackingStage.Received
                     && SqlFunc.Subqueryable<Db_sys_message_ignore>().Where(i => i.UserId == userId
                        && i.IgnoreMessageTypeToId == MergeTable.Id
                        && i.IgnoreMessageLocalType == (int)EnumMessageLocalType.SignCustomerNearlyRelease
                        && i.IsDelete != 1
                        ).NotAny()
                )
                .WhereIF(queryCustomer_IN.EnumRemindType == EnumRemindType.ServiceNearlyEnd, MergeTable =>
                    MergeTable.NearlyServiceEnd == true
                     && SqlFunc.Subqueryable<Db_sys_message_ignore>().Where(i => i.UserId == userId
                        && i.IgnoreMessageTypeToId == MergeTable.Id
                        && i.IgnoreMessageLocalType == (int)EnumMessageLocalType.ServiceNearlyEnd
                        && i.IsDelete != 1
                        ).NotAny()
                )
                //首页跳转
                .WhereIF(queryCustomer_IN.EnumIndexRedirectType == EnumIndexRedirectType.Span_SignCustomer, MergeTable =>
                    MergeTable.CollectionTime != null
                    && indexP.DateStart != null && SqlFunc.ToDateShort(indexP.DateStart.Value) <= SqlFunc.ToDateShort(MergeTable.CollectionTime)
                    && indexP.DateEnd != null && SqlFunc.ToDateShort(indexP.DateEnd.Value) >= SqlFunc.ToDateShort(MergeTable.CollectionTime)
                )
                //首页跳转
                .WhereIF(queryCustomer_IN.EnumIndexRedirectType == EnumIndexRedirectType.Now_RecieveCustomer, MergeTable =>
                    MergeTable.TrackingStage == EnumTrackingStage.Received
                )
                //首页跳转
                .WhereIF(queryCustomer_IN.EnumIndexRedirectType == EnumIndexRedirectType.Now_SaveCustomer, MergeTable =>
                    MergeTable.TrackingStage != EnumTrackingStage.Received
                )
                .OrderByIF(StringUtil.IsNullOrEmpty(queryCustomer_IN.SortField), MergeTable => new { MergeTable.NearlyRealese, MergeTable.NearlyServiceEnd }, OrderByType.Desc)
                .OrderByIF(queryCustomer_IN.SortField == "ServiceEnd", MergeTable => SqlFunc.IIF(SqlFunc.IsNullOrEmpty(MergeTable.ServiceEnd), 1, 0))  //ServiceEnd排序时 将null值排序到最后（没服务）
                .OrderByPropertyName(queryCustomer_IN.SortField, queryCustomer_IN.IsDESC ? OrderByType.Desc : OrderByType.Asc)
                .OrderByIF(StringUtil.IsNullOrEmpty(queryCustomer_IN.SortField), "ProtectionDeadline asc")
                //相同时间mysql排序会有问题，需要加一列id
                .OrderBy(MergeTable => MergeTable.Id)
                .Mapper(MergeTable =>
                {
                    MergeTable.UserName = userInfo.Name;
                    MergeTable.OrgName = userInfo.OrganizationName;
                    MergeTable.TrackingStageName = MergeTable.TrackingStage.GetEnumDescription();
                    MergeTable.CustomerSourceName = MergeTable.CustomerSource.GetEnumDescription();
                    MergeTable.PeerDataName = MergeTable.PeerData.GetEnumDescription();
                    MergeTable.CustomerLevelName = MergeTable.CustomerLevel.GetEnumDescription();
                    MergeTable.CustomerNatureName = MergeTable.CustomerNature.GetEnumDescription();
                    MergeTable.CustomerSizeName = MergeTable.CustomerSize.GetEnumDescription();
                    MergeTable.ServiceStateName = ((EnumCustomerProductServiceState)(MergeTable.ServiceState == null ? 1 : MergeTable.ServiceState)).GetEnumDescription();
                    MergeTable.AvatarImage = userInfo.AvatarImage;
                    MergeTable.CountryName = LocalCache.LC_Address.CountryCache.FirstOrDefault(c => c.Id == MergeTable.Country)?.Name ?? "";
                    //if (StringUtil.IsNotNullOrEmpty(MergeTable.ProtectionDeadline))
                    //{
                    //    var ProtectionDeadlineDate = DateTime.Parse(MergeTable.ProtectionDeadline);
                    //    MergeTable.NearlyRealese = ProtectionDeadlineDate.Subtract(dtDay).Days + 1 < 15;
                    //}
                    //else
                    //{
                    //    MergeTable.NearlyRealese = false;
                    //}

                })
                .ToPageList(queryCustomer_IN.PageNumber, queryCustomer_IN.PageSize, ref total);

            Db.ThenMapper(r, item =>
            {
                item.SubCompanys = Db.Queryable<Db_crm_customer_subcompany>()
                .Where(it => it.Deleted == (int)EnumCustomerDel.NotDel && it.IsMain == (int)EnumCustomerCompanyMain.Sub)
                .Select(it => new CompanySimple_OUT() { Id = it.Id, CompanyName = it.CompanyName, CustomerName = it.CompanyName, CreditCode = it.CreditCode, CustomerId = it.CustomerId, Contacts = it.Contacts })
                .SetContext(x => x.CustomerId, () => item.Id, item).ToList();
                item.CustomerIndustry = Db.Queryable<Db_crm_customer_subcompany_mainbusiness>()
                .LeftJoin<Db_sys_customerindustry>((d, dic) => d.CustomerIndustry == dic.Id)
                .Select((d, dic) => new { SubCompanyId = d.SubCompanyId, CustomerIndustryName = dic.Name })
                .SetContext(d => d.SubCompanyId, () => item.MainCompanyId, item)
                .ToList()
                .Select(it => it.CustomerIndustryName)
                .Distinct()
                .JoinToString(",");
                item.RelatedCompanies = Db.Queryable<Db_crm_customer_subcompany_related>()
                .Where(it => it.Deleted == false && it.IsValid == (int)EnumCompanyValid.VALID)
                .Select(it => new SubCompanyRelated_OUT() { Id = it.Id, CompanyName = it.CompanyName, CreditType = it.CreditType, CreditCode = it.CreditCode, CompanyId = it.CompanyId, InValidTime = it.InValidTime, IsValid = it.IsValid, CreateDate = it.CreateDate })
                .SetContext(x => x.CompanyId, () => item.MainCompanyId, item).ToList();
                item.HasContractProduct = Db.Queryable<Db_crm_customer_subcompany>()
                .InnerJoin<Db_crm_contract>((s, c) => c.FirstParty == s.Id)
                .Where((s, c) => s.Deleted == (int)EnumCustomerDel.NotDel && s.IsValid == (int)EnumCompanyValid.VALID && c.Deleted == false)
                .Select((s, c) => new { Id = c.Id, CustomerId = s.CustomerId })
                .SetContext(s => s.CustomerId, () => item.Id, item)
                .Any();
            });
            List<string> subCompanyIds = r.Select(c => c.MainCompanyId).ToList();
            foreach (var item in r)
            {
                subCompanyIds = subCompanyIds.Concat(item.SubCompanys.Select(it => it.Id)).Distinct().ToList();
            }
            var tds = Db.Queryable<Db_crm_trackingrecord>()
                    .LeftJoin<Db_crm_customer_subcompany>((t, s) => t.CustomerSubCompanyId == s.Id)
                    .Where((t, s) => SqlFunc.ContainsArray(subCompanyIds, t.CustomerSubCompanyId))
                    .Where((t, s) => t.Deleted == false && s.Deleted == (int)EnumCustomerDel.NotDel)
                    .Where((t, s) =>
                    t.CustomerDataSource == EnumCustomerDataSource.Private
                    || (t.CustomerDataSource == EnumCustomerDataSource.Temporary && t.IsVisible == 1)
                    || (t.CustomerDataSource == EnumCustomerDataSource.Temporary && t.UserId == userId)
                    )
                    .Select((t, s) => new TrackingRecordSimple_OUT
                    {
                        UpdateDate = t.UpdateDate == null ? t.CreateDate : t.UpdateDate,
                        CustomerId = s.CustomerId,
                        Remark = t.Remark
                    }).MergeTable()
                    .Select(it => new TrackingRecordSimple_OUT
                    {
                        index = SqlFunc.RowNumber($"{it.UpdateDate} desc", it.CustomerId),
                        CustomerId = it.CustomerId,
                        Remark = it.Remark
                    }).MergeTable().Where(it => it.index == 1).ToList();
            foreach (var item in r)
            {
                var t = tds.Find(t => t.CustomerId == item.Id);
                if (t != null)
                {
                    item.TrackingDetail = t.Remark;
                }
            }
            DbOpe_sys_messagecenterdetail.Instance.GetUserToDoRelate(queryCustomer_IN.EnumRemindType);
            return r;
        }
        /// <summary>
        /// 移出到用户的临时池
        /// </summary>
        /// <param name="customerIds"></param>
        /// <param name="userId"></param>
        /// <param name="overwritten"></param>
        /// <param name="auto"></param>
        public void MoveToTemp(List<string> customerIds, string userId, bool overwritten = false, bool auto = false)
        {
            /*
             * 前置条件：检测当前客户记录是否存在已签约成功、到账的客户，若存在则无法释放,并返回无法释放的原因，若不存在则可正常释放。(后补)
                需要判断临时池是不是已经有这个客户（未删除未释放的）
                如果有要看参数里是否覆盖临时池信息
                
                移动到临时池相当于
                1、添加一个临时客户
                2、原客户释放到公有池
                3、临时池添加新临时客户的记录
                4、如果覆盖临时池记录，要将原临时池记录删除(临时客户和临时池记录一起删除)
                5、恢复相关公司在临时池的有效性
             */
            if (customerIds.Count == 0)
            {
                return;
            }
            var dt = DateTime.Now;
            //取私有池信息
            var privateExistCustomers = GetPrivateCustomerInfo(customerIds, userId);
            //取临时池信息
            var tempExist = DbOpe_crm_customer_temporarypool.Instance.GetTempCustomerInfo(customerIds, userId);
            List<Db_crm_customer_temporarypool> addTemps = new List<Db_crm_customer_temporarypool>();
            List<string> delTempCustomerIds = new List<string>();
            List<CheckCompanyUniqueInfo> checkCompanies = new List<CheckCompanyUniqueInfo>();
            foreach (var customer in privateExistCustomers)
            {
                List<CheckCompanyUniqueInfo> relatedCompanys = new List<CheckCompanyUniqueInfo>();
                //验证私有客户状态
                if (customer.Deleted == (int)EnumCustomerDel.Del || customer.ReleaseType != (int)EnumCustomerPrivateRelease.Not)
                {
                    throw new ApiException(customer.CustomerName + "已被释放或删除，不能被移动到临时池");
                }
                //验证临时池是否已存在这个客户
                var existTempData = tempExist.Find(c => c.CustomerId == customer.CustomerId);
                if (existTempData == null)
                {
                    //复制一个临时客户
                    Db_crm_customer_temporary temporaryCustomer = DbOpe_crm_customer_temporary.Instance.CopyToTempCustomer(customer.CustomerId, ref relatedCompanys, userId);
                    //添加临时池记录
                    addTemps.Add(new Db_crm_customer_temporarypool()
                    {
                        Id = Guid.NewGuid().ToString(),
                        UserId = userId,
                        CustomerId = temporaryCustomer.CustomerId,
                        CustomerTemporaryId = temporaryCustomer.Id,
                        PrivatePoolId = customer.Id,
                        CollectionTime = dt,
                        CreateType = (int)EnumCustomerTempCreateType.PrivateMove,
                        State = (int)EnumCustomerTempRemove.Not
                    });
                }
                else
                {
                    if (!overwritten)
                    {
                        throw new ApiException(customer.CustomerName + "在临时池已存在");
                    }
                    //复制一个临时客户
                    Db_crm_customer_temporary temporaryCustomer = DbOpe_crm_customer_temporary.Instance.CopyToTempCustomer(customer.CustomerId, ref relatedCompanys, userId);
                    //删除原有的临时客户记录
                    delTempCustomerIds.Add(existTempData.CustomerTemporaryId);
                    //添加临时池记录
                    addTemps.Add(new Db_crm_customer_temporarypool()
                    {
                        Id = Guid.NewGuid().ToString(),
                        UserId = userId,
                        CustomerId = temporaryCustomer.CustomerId,
                        CustomerTemporaryId = temporaryCustomer.Id,
                        PrivatePoolId = customer.Id,
                        CollectionTime = dt,
                        CreateType = (int)EnumCustomerTempCreateType.PrivateMove,
                        State = (int)EnumCustomerTempRemove.Not
                    });
                }
                //私有池状态更新
                customer.State = (int)EnumCustomerPrivateRelease.Temp;
                customer.ReleaseType = auto ? (int)EnumCustomerPrivateReleaseType.Auto : (int)EnumCustomerPrivateReleaseType.Manual;
                customer.ReleaseTime = dt;
                customer.ReleaseReason = EnumCustomerPrivateRelease.Temp.GetEnumDescription();
                //添加受影响公司代码记录
                checkCompanies = checkCompanies.Concat(relatedCompanys).ToList();
            }
            //添加临时池记录
            DbOpe_crm_customer_temporarypool.Instance.Insert(addTemps);
            //删除已存在的临时客户及记录
            DbOpe_crm_customer_temporarypool.Instance.DelTemporaryCustomer(delTempCustomerIds, userId);
            //私有池状态更新
            Update(privateExistCustomers.MappingTo<List<Db_crm_customer_privatepool>>());
            //释放到公有池
            DbOpe_crm_customer_publicpool.Instance.ReturnToPublic(customerIds, userId);
            //刷新临时池公司相关的有效信息  
            DbOpe_crm_customer_subcompany_temporary.Instance.RefreshVaildCompanys(checkCompanies, EnumCompanyValid.VALID);
            //240811 恢复作废的采购主体关联提醒
            BLL_Customer.Instance.ReactiveCompanyRelated(checkCompanies);
        }
        /// <summary>
        /// 释放到公有池
        /// </summary>
        /// <param name="customerIds"></param>
        /// <param name="releaseReason"></param>
        /// <param name="userId"></param>
        /// <param name="auto"></param>
        public void MoveToPublic(List<string> customerIds, string releaseReason, string userId, bool auto = false)
        {
            /*
             将客户信息从私有池释放到公有池。
            在公有池添加数据。
            对私有池数据做状态变更，记录释放时间以及释放原因，绑定公有池数据。
            10.17如果是自动释放的话，不做user的判断，直接取释放客户的user
            * 前置条件：检测当前客户记录是否存在已签约成功、到账的客户，若存在则无法释放,并返回无法释放的原因，若不存在则可正常释放。(后补)
             */
            if (customerIds.Count == 0)
            {
                return;
            }
            var dt = DateTime.Now;
            string checkUserId = !auto ? userId : "";
            //释放到公有池 返回ID
            var publicDatas = DbOpe_crm_customer_publicpool.Instance.ReturnToPublic(customerIds, checkUserId, true);
            //取私有池信息 （这里其实有可能取到重复的私有池数据，垃圾数据问题）
            var privateExistCustomers = GetPrivateCustomerInfo(customerIds, checkUserId);
            List<CheckCompanyUniqueInfo> checkCompanies = new List<CheckCompanyUniqueInfo>();
            foreach (var customer in privateExistCustomers)
            {
                List<CheckCompanyUniqueInfo> relatedCompanys = new List<CheckCompanyUniqueInfo>();
                var companys = DbOpe_crm_customer_subcompany.Instance.GetCustomerCompanys(customer.CustomerId);
                relatedCompanys = companys.MappingTo<List<CheckCompanyUniqueInfo>>();
                //私有池状态更新
                if (auto)
                {
                    customer.ReleaseType = (int)EnumCustomerPrivateReleaseType.Auto;
                }
                else
                {
                    customer.ReleaseType = (int)EnumCustomerPrivateReleaseType.Manual;
                }
                customer.PublicPoolId = publicDatas.Find(c => c.CustomerId == customer.Id)?.Id;
                customer.State = (int)EnumCustomerPrivateRelease.Release;
                customer.ReleaseTime = dt;
                customer.ReleaseReason = releaseReason;
                checkCompanies = checkCompanies.Concat(relatedCompanys).ToList();
            }
            //私有池状态更新
            Update(privateExistCustomers.MappingTo<List<Db_crm_customer_privatepool>>());
            //刷新临时池公司相关的有效信息  
            DbOpe_crm_customer_subcompany_temporary.Instance.RefreshVaildCompanys(checkCompanies, EnumCompanyValid.VALID);
            //240811 恢复作废的采购主体关联提醒
            BLL_Customer.Instance.ReactiveCompanyRelated(checkCompanies);
        }

        /// <summary>
        /// 判断是否还可以延期
        /// </summary>
        public void CheckDelayEnable(List<string> customerIds, string userId, ref List<PrivateCustomerDelayInfo> delayInfos, bool containsHistoryReleased = false)
        {
            var leftSaveNums = GetMaxSavePrivateCustomerNumLeft(userId);
            if (leftSaveNums < 0)
            {
                throw new ApiException("已超过可保留客户数,无法进行延期操作");
            }
            var maxDelayTimes = GetMaxDelayTimes();
            var privateExistCustomers = GetPrivateCustomerInfo(customerIds, userId, false, containsHistoryReleased);
            delayInfos = privateExistCustomers.MappingTo<List<PrivateCustomerDelayInfo>>();
            foreach (var customer in delayInfos)
            {
                if (!containsHistoryReleased && (customer.Deleted == (int)EnumCustomerDel.Del || customer.State != (int)EnumCustomerPrivateRelease.Not))
                {
                    throw new ApiException(customer.CustomerName + "已被释放或删除，不能延期");
                }
                else
                {
                    var totalDelayTimes = GetHistoryDelayTimes(customer.CustomerId, userId);
                    if (totalDelayTimes >= maxDelayTimes)
                    {
                        throw new ApiException(customer.CustomerName + "超过最大延期次数");
                    }
                    customer.TotalDelayTimes = totalDelayTimes;
                }
            }
        }
        /// <summary>
        /// 获取用户私有池客户信息
        /// </summary>
        /// <returns></returns>
        public List<PrivateCustomerSimpleInfo> GetPrivateCustomerInfo(List<string> customerIds, string userId = "", bool containsDeleted = false, bool containsHistory = false, bool containsMerge = false)
        {
            return Queryable
                .LeftJoin<Db_crm_customer_subcompany>((privatePool, company) => privatePool.CustomerId == company.CustomerId && company.IsMain == (int)EnumCustomerCompanyMain.Main)
                .LeftJoin<Db_crm_customer>((privatePool, company, customer) => company.CustomerId == customer.Id)
                .LeftJoin<Db_sys_user>((privatePool, company, customer, user) => privatePool.UserId == user.Id)
                .Where((privatePool, company, customer, user) => SqlFunc.ContainsArray(customerIds, privatePool.CustomerId))
                .WhereIF(!string.IsNullOrEmpty(userId), (privatePool, company, customer, user) => privatePool.UserId == userId)
                .WhereIF(!containsDeleted, (privatePool, company, customer, user) => privatePool.Deleted == (int)EnumCustomerDel.NotDel)
                .WhereIF(!containsHistory, (privatePool, company, customer, user) => privatePool.State == (int)EnumCustomerPrivateRelease.Not)
                .WhereIF(!containsMerge, (privatePool, company, customer, user) => customer.IsMerge == (int)EnumCustomerMerge.NotMerge)
                .Select((privatePool, company, customer, user) => new PrivateCustomerSimpleInfo()
                {
                    UserName = user.Name,
                    MainCompanyId = company.Id,
                    CustomerName = company.CompanyName,
                    CustomerNum = customer.CustomerNum,
                    TrackingStage = customer.TrackingStage
                }, true).ToList();
        }

        /// <summary>
        /// 获取用户私有池客户信息
        /// </summary>
        /// <returns></returns>
        public List<PrivateCustomerInfo> GetPrivateCustomerInfo(string customerName, string userId = "", bool containsDeleted = false, bool containsHistory = false, bool containsMerge = false)
        {
            return Queryable
                .LeftJoin<Db_crm_customer_subcompany>((privatePool, company) => privatePool.CustomerId == company.CustomerId && company.IsMain == (int)EnumCustomerCompanyMain.Main)
                .LeftJoin<Db_crm_customer>((privatePool, company, customer) => company.CustomerId == customer.Id)
                .LeftJoin<Db_sys_user>((privatePool, company, customer, user) => privatePool.UserId == user.Id)
                .WhereIF(!string.IsNullOrEmpty(customerName), (privatePool, company, customer, user) => company.CompanyName.Contains(customerName))
                .WhereIF(!string.IsNullOrEmpty(userId), (privatePool, company, customer, user) => privatePool.UserId == userId)
                .WhereIF(!containsDeleted, (privatePool, company, customer, user) => privatePool.Deleted == (int)EnumCustomerDel.NotDel)
                .WhereIF(!containsHistory, (privatePool, company, customer, user) => privatePool.State == (int)EnumCustomerPrivateRelease.Not)
                .WhereIF(!containsMerge, (privatePool, company, customer, user) => customer.IsMerge == (int)EnumCustomerMerge.NotMerge)
                .Select((privatePool, company, customer, user) => new PrivateCustomerInfo()
                {
                    id = customer.Id,
                    CustomerName = company.CompanyName,
                }, true).ToList();
        }

        /// <summary>
        /// 检查用户私有池客户有效性(未删除且未释放)
        /// </summary>
        /// <param name="customerIds"></param>
        /// <param name="userId"></param>
        /// <param name="privateCustomerInfos"></param>
        public void CheckUserPrivateCustomerValid(List<string> customerIds, string userId, ref List<PrivateCustomerSimpleInfo> privateCustomerInfos)
        {
            privateCustomerInfos = GetPrivateCustomerInfo(customerIds, userId);
            foreach (var customer in privateCustomerInfos)
            {
                if (!(customer.Deleted == (int)EnumCustomerDel.NotDel && customer.State == (int)EnumCustomerPrivateRelease.Not))
                {
                    throw new ApiException("操作失败:" + customer.CustomerName + "已被释放或删除");
                }
            }
        }
        /// <summary>
        /// 执行延期操作(延期次数+1),并且进行记录
        /// 20250509 剩余保护期超过一年的，不进行延期
        /// </summary>
        /// <returns></returns>
        public void ExcuteDelay(List<PrivateCustomerDelayInfo> delayInfos, string userId, int delayDays, bool addDelayTiems = true)
        {
            var poolIds = delayInfos.Select(i => i.Id).ToList();
            var currentDatas = GetDataList(d => SqlFunc.ContainsArray(poolIds, d.Id));
            foreach (var delayInfo in delayInfos)
            {
                var currentData = currentDatas.Find(d => d.Id == delayInfo.Id);
                if (currentData == null)
                {
                    continue;
                }
                if (currentData.ProtectionDeadline.Value > DateTime.Now.AddYears(1))
                {
                    // 剩余保护期超过一年的，不进行延期
                    if (delayInfos.Count == 1)
                    {
                        //单个客户记录的操作，直接提示信息
                        throw new ApiException(delayInfo.CustomerName + "剩余保护期超过一年，无需延期");
                    }
                    continue;
                }

                Updateable.SetColumns(c => c.ProtectionDeadline == c.ProtectionDeadline.Value.AddDays(delayDays))
                .SetColumnsIF(addDelayTiems, c => c.DelayTimes == c.DelayTimes + 1)
                .Where(c => c.UserId == userId)
                .Where(c => c.Id == delayInfo.Id).ExecuteCommand();

                DbOpe_crm_customer_privatepool_delay.Instance.Insert(new Db_crm_customer_privatepool_delay()
                {
                    Id = Guid.NewGuid().ToString(),
                    UserId = userId,
                    CustomerId = delayInfo.CustomerId,
                    CustomerPrivatePoolId = delayInfo.Id,
                    CreateUser = userId,
                    CreateDate = DateTime.Now,
                    ExecuteDelayDate = DateTime.Now,
                    TimeExpand = delayDays,
                    ProtectionDeadlineCopy = currentData.ProtectionDeadline.Value.AddDays(delayDays),
                    DelayTimes = delayInfo.TotalDelayTimes //第几次延期
                });
            }
        }
        /// <summary>
        /// 获取用户历史延期次数（将所有历史的延期次数求和）
        /// </summary>
        /// <returns></returns>
        public int GetHistoryDelayTimes(string customerId, string userId)
        {
            //获取历史信息
            var historyPrivateList = GetHistoryPrivateCustomerInfo(customerId, userId);
            return historyPrivateList.Sum(p => p.DelayTimes);
        }
        /// <summary>
        /// 看看在私有池有没有过相同客户,获取历史信息(包括当前领取未释放的)
        /// </summary>
        /// <param name="customerId"></param>
        /// <param name="userId"></param>
        public List<PrivateCustomerSimpleInfo> GetHistoryPrivateCustomerInfo(string customerId, string userId)
        {
            //查找客户（包括关联的历史客户）的私有池历史记录
            List<PrivateCustomerSimpleInfo> privateCustomerInfos = GetPrivateCustomerInfo(new List<string>() { customerId }, userId, false, true, true);
            return privateCustomerInfos;
        }
        /// <summary>
        /// 读取系统规定的最大延期次数
        /// </summary>
        /// <returns></returns>
        public int GetMaxDelayTimes()
        {
            return DbOpe_sys_comparam.Instance.GetComparams("PrivatePostponementsNum");
        }
        /// <summary>
        /// 判断用户是否从公有池领取过指定客户，并返回剩余的保护天数
        /// </summary>
        /// <param name="customerId"></param>
        /// <param name="userId"></param>
        /// <param name="leftProtectDay"></param>
        /// <param name="latestPrivateRecord"></param>
        /// <param name="notRelease"></param>
        /// <returns></returns>
        public bool CheckUserCustomerHasHistory(string customerId, string userId, ref int leftProtectDay, ref PrivateCustomerSimpleInfo latestPrivateRecord, ref bool notRelease)
        {
            leftProtectDay = 0;
            latestPrivateRecord = null;
            var privateExists = DbOpe_crm_customer_privatepool.Instance.GetHistoryPrivateCustomerInfo(customerId, userId);
            if (privateExists.Count == 0)
            {
                return false;
            }
            else
            {
                var notReleaseData = privateExists.Find(p => p.ReleaseType == (int)EnumCustomerPrivateReleaseType.Not);
                var leftDays = 0;
                if (notReleaseData != null)
                {
                    //未释放
                    notRelease = true;
                    latestPrivateRecord = notReleaseData;
                    leftDays = latestPrivateRecord.ProtectionDeadline.Value.Subtract(DateTime.Now).Days;
                }
                else
                {
                    notRelease = false;
                    //二次/多次领取(已释放)
                    privateExists.Sort((x, y) =>
                    {
                        if (x.ProtectionDeadline == null)
                        {
                            return -1;
                        }
                        else if (y.ProtectionDeadline == null)
                        {
                            return 1;
                        }
                        else
                        {
                            return -x.ProtectionDeadline.Value.CompareTo(y.ProtectionDeadline.Value);
                        }

                    });
                    //获取最后一次时，剩余的保护天数
                    privateExists.Sort((a, b) => b.ReleaseTime.Value.CompareTo(a.ReleaseTime.Value));
                    latestPrivateRecord = privateExists[0];
                    leftDays = latestPrivateRecord.ProtectionDeadline.Value.Subtract(latestPrivateRecord.ReleaseTime.Value).Days;
                }
                if (leftDays < 0)
                {
                    leftProtectDay = 0;
                }
                else
                {
                    leftProtectDay = leftDays;
                }
                return true;
            }
        }

        /// <summary>
        /// 处理移交/从临时池移动到私有池 继承之前的保护截止日期，过期则判断并进行延期操作，记录延期日志
        /// </summary>
        /// <param name="customerIds"></param>
        /// <param name="opUserId"></param>
        /// <param name="targetUserId"></param>
        /// <param name="protectDate"></param>
        /// <param name="delayDays"></param>
        /// <param name="createType"></param>
        /// <param name="protectNotChange">保护截止日直接取protectDate，不进行继承之前日期或者重新计算等操作</param>
        public List<Db_crm_customer_privatepool> MoveToUserPrivate(List<string> customerIds, string opUserId, string targetUserId, DateTime protectDate, int delayDays, EnumCustomerPrivateCreateType createType, bool protectNotChange = false)
        {
            DateTime dt = DateTime.Now;
            List<Db_crm_customer_privatepool> returnList = new List<Db_crm_customer_privatepool>();
            //判断对方是否没领取过同名的客户
            foreach (string customerId in customerIds)
            {
                var leftDays = 0;
                PrivateCustomerSimpleInfo lastRecord = new PrivateCustomerSimpleInfo();
                var notRelease = false;
                if (!CheckUserCustomerHasHistory(customerId, targetUserId, ref leftDays, ref lastRecord, ref notRelease) || protectNotChange)
                {
                    //首次领取 或者保护截止日直接取protectDate
                    var d = new Db_crm_customer_privatepool()
                    {
                        Id = Guid.NewGuid().ToString(),
                        UserId = targetUserId,
                        CustomerId = customerId,
                        CollectionTime = dt,
                        ProtectionDeadline = protectDate,
                        State = (int)EnumCustomerPrivateRelease.Not,
                        CreateType = (int)createType,
                        CreateDate = dt,
                        CreateUser = opUserId
                    };
                    DbOpe_crm_customer_privatepool.Instance.Insert(d);
                    returnList.Add(d);
                }
                else
                {
                    //不是首次领取，判断是否需要延期
                    if (notRelease)
                    {
                        throw new ApiException("错误:目标私有池存在未释放的相同客户");
                    }
                    if (leftDays <= 0)
                    {
                        List<PrivateCustomerDelayInfo> delayInfos = new List<PrivateCustomerDelayInfo>();
                        //需要延期，验证还有没有延期次数
                        DbOpe_crm_customer_privatepool.Instance.CheckDelayEnable(new List<string>() { customerId }, targetUserId, ref delayInfos, true);
                        //240802 注意 上面这个delayInfos是已经释放的记录 不能用上面这个delayInfos
                        //添加记录 (保护截止日取当天 后面再进行一次延期)
                        var d = new Db_crm_customer_privatepool()
                        {
                            Id = Guid.NewGuid().ToString(),
                            UserId = targetUserId,
                            CustomerId = customerId,
                            CollectionTime = dt,
                            ProtectionDeadline = dt,
                            State = (int)EnumCustomerPrivateRelease.Not,
                            CreateType = (int)createType,
                            CreateDate = dt,
                            CreateUser = opUserId
                        };
                        DbOpe_crm_customer_privatepool.Instance.Insert(d);
                        returnList.Add(d);
                        //执行延期操作
                        PrivateCustomerDelayInfo delayInfo = d.MappingTo<PrivateCustomerDelayInfo>();
                        var dHisInfo = delayInfos.FirstOrDefault();
                        if (dHisInfo != null)
                        {
                            delayInfo.TotalDelayTimes = dHisInfo.TotalDelayTimes;
                        }
                        else
                        {
                            delayInfo.TotalDelayTimes = 0;
                        }
                        DbOpe_crm_customer_privatepool.Instance.ExcuteDelay(new List<PrivateCustomerDelayInfo>() { delayInfo }, targetUserId, delayDays, true);
                    }
                    else
                    {
                        var d = new Db_crm_customer_privatepool()
                        {
                            Id = Guid.NewGuid().ToString(),
                            UserId = targetUserId,
                            CustomerId = customerId,
                            CollectionTime = dt,
                            ProtectionDeadline = dt.AddDays(leftDays + 1),
                            State = (int)EnumCustomerPrivateRelease.Not,
                            CreateType = (int)createType,
                            CreateDate = dt,
                            CreateUser = opUserId
                        };
                        //添加记录 (保护截止日从当前向后顺延剩余的天数)
                        DbOpe_crm_customer_privatepool.Instance.Insert(d);
                        returnList.Add(d);
                    }
                }
                //2024年9月3日 领取客户增加一条新的跟踪记录 作为统计使用
                //获取当前客户主公司信息
                var mainCom = DbOpe_crm_customer_subcompany.Instance.GetCustomerMainCompany(customerId);
                //同步客户跟踪记录
                BLL_TrackingRecord.Instance.InternalAddOrModifyTrackingRecord(customerId, mainCom.Id, "", "", EnumTrackingStage.CollectFromPool, 1, true, true, "", EnumCustomerDataSource.Private, true);

            }
            return returnList;
        }
        /// <summary>
        /// 团队私有池列表 （9.19 只有团队的管理员查这个才有结果，普通销售查是空的  9.22 签约后的这里就不让看了,自己的也看不到）
        /// 0729 增加团队离职人员所有客户的查询
        /// </summary>
        /// <param name="queryTeamCustomer_IN"></param>
        /// <param name="userId"></param>
        /// <param name="total"></param>
        /// <returns></returns>
        public List<QueryTeamCustomer_OUT> SearchTeamPoolCustomerList(QueryTeamCustomer_IN queryTeamCustomer_IN, string userId, ref int total)
        {
            var dt = DateTime.Now;
            var dtDay = DateTime.Parse(dt.ToString("yyyy-MM-dd"));
            var weekEnd = dt.AddDays(1 - Convert.ToInt32(dt.DayOfWeek.ToString("d"))).AddDays(6).ToString("yyyy-MM-dd");
            var weekStart = dt.AddDays(1 - Convert.ToInt32(dt.DayOfWeek.ToString("d"))).ToString("yyyy-MM-dd");
            var user = DbOpe_sys_user.Instance.GetDataById(userId);
            if (user.UserType != (int)EnumUserType.Manager)
            {
                return new List<QueryTeamCustomer_OUT>() { };
            }
            //userOrg 直属组织
            var userOrg = user.OrganizationId;

            //验证超级权限
            bool superRole = BLL_Role.Instance.CheckSuperUser();
            bool managerRole = BLL_Role.Instance.CheckSalesManagerUser();
            //验证组织参数权限并且添加到组织过滤条件数组里
            List<string> OrgDivisionId = new List<string>();
            List<string> OrgBrigadeId = new List<string>();
            List<string> OrgRegimentId = new List<string>();
            List<string> teamLeaders = new List<string>();
            List<string> leaveUsers = new List<string>();
            //20240719 除了直属组织下人员外，还可以看直属下级组织的队长的客户
            if (queryTeamCustomer_IN.Organization != null)
            {
                //20240716 除了特殊权限 其他人只能看直属组织userOrg
                if (!superRole && !managerRole)
                {
                    //获取当前用户的直属队
                    var managedOrgs = DbOpe_sys_organization.Instance.GetDataList(o => o.ParentId == userOrg).Select(o => o.Id).ToList();
                    foreach (var selectOrg in queryTeamCustomer_IN.Organization)
                    {
                        if (!managedOrgs.Contains(selectOrg) && queryTeamCustomer_IN.Organization[0] != userOrg)
                        {
                            throw new ApiException("无组织权限");
                        }
                    }
                    //获取直属队队长
                    teamLeaders = DbOpe_sys_user.Instance.GetDataList(u =>
                        SqlFunc.ContainsArray(managedOrgs, u.OrganizationId)
                        && u.UserType == (int)EnumUserType.Manager
                        && u.Deleted == false)
                    .Select(u => u.Id)
                    .ToList();
                    //获取团队内的所有离职人员(停用) 团队里离职的要加上直属队队长
                    leaveUsers = DbOpe_sys_user.Instance.GetDataList(u =>
                        (u.OrganizationId == userOrg
                        && u.UserStatus == false
                        && u.Deleted == false)
                        || (SqlFunc.ContainsArray(managedOrgs, u.OrganizationId)
                        && u.UserType == (int)EnumUserType.Manager
                        && u.UserStatus == false
                        && u.Deleted == false
                        )).Select(u => u.Id)
                    .ToList();
                }
                else
                {
                    foreach (var pOrg in queryTeamCustomer_IN.Organization)
                    {
                        var pOrgInfo = DbOpe_sys_organization.Instance.GetData(d => d.Id == pOrg);
                        switch (pOrgInfo.OrgType)
                        {
                            case EnumOrgType.BattleTeam:
                                OrgDivisionId.Add(pOrg);
                                break;
                            case EnumOrgType.Battalion:
                                OrgBrigadeId.Add(pOrg);
                                break;
                            default:
                                OrgRegimentId.Add(pOrg);
                                break;
                        }
                    }
                }
                //var orgObjList = DbOpe_sys_organization.Instance.GetSelfAndSubOrgListByOrgId(userOrg);
                ////orgList 直属+下属组织
                //var orgList = orgObjList.Select(o => o.Id).ToList();
                ////childOrgs 下属组织
                //var childOrgs = orgList.Where(o => o != userOrg).ToList();
                //foreach (var pOrg in queryTeamCustomer_IN.Organization)
                //{
                //    if (!superRole && !managerRole && !orgList.Contains(pOrg))
                //    {
                //        throw new ApiException("无组织权限");
                //    }
                //    else
                //    {
                //        var pOrgInfo = DbOpe_sys_organization.Instance.GetData(d => d.Id == pOrg);
                //        switch (pOrgInfo.OrgType)
                //        {
                //            case EnumOrgType.BattleTeam:
                //                OrgDivisionId.Add(pOrg);
                //                break;
                //            case EnumOrgType.Battalion:
                //                OrgBrigadeId.Add(pOrg);
                //                break;
                //            default:
                //                OrgRegimentId.Add(pOrg);
                //                break;
                //        }
                //    }
                //}
            }
            else
            {
                if (!superRole && !managerRole)
                {
                    //获取当前用户的直属队
                    var managedOrgs = DbOpe_sys_organization.Instance.GetDataList(o => o.ParentId == userOrg).Select(o => o.Id).ToList();
                    //获取直属队队长
                    teamLeaders = DbOpe_sys_user.Instance.GetDataList(u =>
                        SqlFunc.ContainsArray(managedOrgs, u.OrganizationId)
                        && u.UserType == (int)EnumUserType.Manager
                        && u.Deleted == false)
                    .Select(u => u.Id)
                    .ToList();
                    //获取团队内的所有离职人员(停用) 团队里离职的要加上直属队队长
                    leaveUsers = DbOpe_sys_user.Instance.GetDataList(u =>
                        (u.OrganizationId == userOrg
                        && u.UserStatus == false
                        && u.Deleted == false)
                        || (SqlFunc.ContainsArray(managedOrgs, u.OrganizationId)
                        && u.UserType == (int)EnumUserType.Manager
                        && u.UserStatus == false
                        && u.Deleted == false
                        ))
                    .Select(u => u.Id)
                    .ToList();
                }
                else
                {
                    //var orgInfo = DbOpe_sys_organization.Instance.GetData(d => d.Id == userOrg);
                    //switch (orgInfo.OrgType)
                    //{
                    //    case EnumOrgType.BattleTeam:
                    //        OrgDivisionId.Add(userOrg);
                    //        break;
                    //    case EnumOrgType.Battalion:
                    //        OrgBrigadeId.Add(userOrg);
                    //        break;
                    //    default:
                    //        OrgRegimentId.Add(userOrg);
                    //        break;
                    //}
                }
            }
            var orgExp = Expressionable.Create<Db_crm_customer_privatepool, Db_crm_customer_subcompany, Db_crm_customer, Db_v_userwithorg, Db_v_customerproductserviceinfostatus, Db_crm_customer_org_log>();
            orgExp.OrIF(!ArrayUtil.IsNullOrEmpty(OrgDivisionId), (privatePool, company, customer, user, productstate, log) => SqlFunc.ContainsArray(OrgDivisionId, log.OrgDivisionId));
            orgExp.OrIF(!ArrayUtil.IsNullOrEmpty(OrgBrigadeId), (privatePool, company, customer, user, productstate, log) => SqlFunc.ContainsArray(OrgBrigadeId, log.OrgBrigadeId));
            orgExp.OrIF(!ArrayUtil.IsNullOrEmpty(OrgRegimentId), (privatePool, company, customer, user, productstate, log) => SqlFunc.ContainsArray(OrgRegimentId, log.OrgRegimentId));

            var r = Queryable
                .LeftJoin<Db_crm_customer_subcompany>((privatePool, company) => privatePool.CustomerId == company.CustomerId && company.IsMain == (int)EnumCustomerCompanyMain.Main)
                .LeftJoin<Db_crm_customer>((privatePool, company, customer) => company.CustomerId == customer.Id)
                .LeftJoin<Db_v_userwithorg>((privatePool, company, customer, user) => user.Id == privatePool.UserId)
                .LeftJoin<Db_v_customerproductserviceinfostatus>((privatePool, company, customer, user, productstate) => company.CustomerId == productstate.CustomerId)
                .LeftJoin<Db_crm_customer_org_log>((privatePool, company, customer, user, productstate, log) => privatePool.CustomerId == log.CustomerId && log.IsValid == (int)EnumCustomerLogValid.Valid && log.Deleted == (int)EnumCustomerDel.NotDel)
                .WhereIF(!string.IsNullOrEmpty(queryTeamCustomer_IN.UserName), (privatePool, company, customer, user, productstate, log) => user.Name.Contains(queryTeamCustomer_IN.UserName))
                //是否排除自己 默认排除  false不排除
                .WhereIF(queryTeamCustomer_IN.OtherFlag == null || queryTeamCustomer_IN.OtherFlag.Value, (privatePool, company, customer, user, productstate, log) => privatePool.UserId != userId)
                .WhereIF(managerRole || superRole, orgExp.ToExpression())
                //240723 不是管理员不能看到0000000组织的客户
                .WhereIF(!managerRole && !superRole, (privatePool, company, customer, user, productstate, log) => (user.OrganizationId != Guid.Empty.ToString() && user.OrganizationId == userOrg) || SqlFunc.ContainsArray(teamLeaders, privatePool.UserId))
                .WhereIF(!string.IsNullOrEmpty(queryTeamCustomer_IN.CustomerNum), (privatePool, company, customer, user, productstate, log) => customer.CustomerNum == queryTeamCustomer_IN.CustomerNum)
                .WhereIF(!string.IsNullOrEmpty(queryTeamCustomer_IN.CustomerName), (privatePool, company, customer, user, productstate, log) => SqlFunc.Contains(company.CompanyName, queryTeamCustomer_IN.CustomerName))
                .WhereIF(!ArrayUtil.IsNullOrEmpty(queryTeamCustomer_IN.TrackingStage), (privatePool, company, customer, user, productstate, log) => SqlFunc.ContainsArray(queryTeamCustomer_IN.TrackingStage, customer.TrackingStage))
                .WhereIF(!ArrayUtil.IsNullOrEmpty(queryTeamCustomer_IN.CustomerLevel), (privatePool, company, customer, user, productstate, log) => SqlFunc.ContainsArray(queryTeamCustomer_IN.CustomerLevel, customer.CustomerLevel))
                .WhereIF(!ArrayUtil.IsNullOrEmpty(queryTeamCustomer_IN.CustomerSource), (privatePool, company, customer, user, productstate, log) => SqlFunc.ContainsArray(queryTeamCustomer_IN.CustomerSource, customer.CustomerSource))
                .WhereIF(!ArrayUtil.IsNullOrEmpty(queryTeamCustomer_IN.PeerData), (privatePool, company, customer, user, productstate, log) => SqlFunc.ContainsArray(queryTeamCustomer_IN.PeerData, customer.PeerData))
                .WhereIF(!ArrayUtil.IsNullOrEmpty(queryTeamCustomer_IN.ProductId), (privatePool, company, customer, user, productstate, log) =>
                            SqlFunc.Subqueryable<Db_crm_contract>()
                            .LeftJoin<Db_crm_contract_productinfo>((contract, contractProduct) => contract.Id == contractProduct.ContractId)
                            .LeftJoin<Db_crm_customer_subcompany>((contract, contractProduct, firstParty) => firstParty.Id == contract.FirstParty)
                            .Where((contract, contractProduct, firstParty) =>
                            privatePool.CustomerId == firstParty.CustomerId &&
                            SqlFunc.ContainsArray(queryTeamCustomer_IN.ProductId, contractProduct.ProductId)
                            && contractProduct.Deleted == false
                            && contract.Deleted == false)
                            .Any()
                )
                .WhereIF(!ArrayUtil.IsNullOrEmpty(queryTeamCustomer_IN.ServiceState), (privatePool, company, customer, user, productstate, log) => SqlFunc.ContainsArray(queryTeamCustomer_IN.ServiceState, productstate.ProductServiceInfoStatus) || (queryTeamCustomer_IN.ServiceState.Contains(EnumCustomerProductServiceState.NONE) && SqlFunc.IsNullOrEmpty(productstate.ProductServiceInfoStatus)))
                .WhereIF(queryTeamCustomer_IN.CollectionTimeStart != null, (privatePool, company, customer, user, productstate, log) => privatePool.CollectionTime != null && queryTeamCustomer_IN.CollectionTimeStart != null && SqlFunc.ToDateShort(queryTeamCustomer_IN.CollectionTimeStart.Value) <= SqlFunc.ToDateShort(privatePool.CollectionTime.Value))
                .WhereIF(queryTeamCustomer_IN.CollectionTimeEnd != null, (privatePool, company, customer, user, productstate, log) => privatePool.CollectionTime != null && queryTeamCustomer_IN.CollectionTimeEnd != null && SqlFunc.ToDateShort(queryTeamCustomer_IN.CollectionTimeEnd.Value) >= SqlFunc.ToDateShort(privatePool.CollectionTime.Value))
                .WhereIF(queryTeamCustomer_IN.enumCustomerTeamQueryListType == EnumCustomerTeamQueryListType.Today, (privatePool, company, customer, user, productstate, log) =>
                (privatePool.CollectionTime != null && SqlFunc.DateIsSame(privatePool.CollectionTime.Value, dtDay))
                || SqlFunc.DateIsSame(privatePool.CreateDate.Value, dtDay)
                )
                .WhereIF(queryTeamCustomer_IN.enumCustomerTeamQueryListType == EnumCustomerTeamQueryListType.Week, (privatePool, company, customer, user, productstate, log) =>
                (privatePool.CollectionTime != null && SqlFunc.Between(privatePool.CollectionTime.Value, weekStart, weekEnd))
                || SqlFunc.Between(privatePool.CreateDate.Value, weekStart, weekEnd)
                )
                .WhereIF(queryTeamCustomer_IN.enumCustomerTeamQueryListType == EnumCustomerTeamQueryListType.NotUpdate15,
                (privatePool, company, customer, user, productstate, log) =>
                           !SqlFunc.Subqueryable<Db_crm_trackingrecord>()
                            .Where(item => privatePool.CustomerId == item.CustomerId
                                && privatePool.UserId == item.UserId
                                && ((item.CreateDate != null && SqlFunc.DateDiff(DateType.Day, dtDay, item.CreateDate.Value) <= 15)
                                || (item.UpdateDate != null && SqlFunc.DateDiff(DateType.Day, dtDay, item.UpdateDate.Value) <= 15))
                                && item.Deleted == false)
                            .Any()
                            &&
                            !SqlFunc.Subqueryable<Db_crm_schedule>()
                            .Where(item => privatePool.CustomerId == item.CustomerId
                                && privatePool.UserId == item.UserId
                                && ((item.CreateDate != null && SqlFunc.DateDiff(DateType.Day, dtDay, item.CreateDate.Value) <= 15)
                                || (item.UpdateDate != null && SqlFunc.DateDiff(DateType.Day, dtDay, item.UpdateDate.Value) <= 15))
                                && item.Deleted == false)
                            .Any()
                            &&
                            (privatePool.CollectionTime != null && SqlFunc.DateDiff(DateType.Day, privatePool.CollectionTime.Value, dtDay) >= 15)
                            || SqlFunc.DateDiff(DateType.Day, privatePool.CreateDate.Value, dtDay) >= 15
                )
                .WhereIF(queryTeamCustomer_IN.enumCustomerTeamQueryListType == EnumCustomerTeamQueryListType.Leave, (privatePool, company, customer, user, productstate, log) =>
                    SqlFunc.ContainsArray(leaveUsers, privatePool.UserId)
                )
                .Where((privatePool, company, customer, user, productstate, log) => privatePool.State == (int)EnumCustomerPrivateRelease.Not)
                .Where((privatePool, company, customer, user, productstate, log) =>
                 privatePool.Deleted == (int)EnumCustomerDel.NotDel
                    && company.Deleted == (int)EnumCustomerDel.NotDel
                    && customer.Deleted == (int)EnumCustomerDel.NotDel
                )
                .Where((privatePool, company, customer, user, productstate, log) => customer.IsValid == (int)EnumCustomerValid.VALID)
                .Where((privatePool, company, customer, user, productstate, log) => customer.IsMerge == (int)EnumCustomerMerge.NotMerge)
                //签约成功/到账成功 不予显示 (高级管理除外)   240729  离职的显示全部客户
                .WhereIF(!superRole, (privatePool, company, customer, user, productstate, log) => (customer.TrackingStage != (int)EnumTrackingStage.SignedContract && customer.TrackingStage != (int)EnumTrackingStage.Received) || SqlFunc.ContainsArray(leaveUsers, privatePool.UserId))
                .Select((privatePool, company, customer, user, productstate, log) => new QueryTeamCustomer_OUT()
                {
                    Id = customer.Id,
                    CreditCode = company.CreditCode,
                    CustomerName = company.CompanyName,
                    UserId = privatePool.UserId,
                    UserName = user.UserWithOrgFullName,
                    CustomerNum = customer.CustomerNum,
                    ProtectionDeadline = privatePool.ProtectionDeadline == null ? "" : privatePool.ProtectionDeadline.Value.ToString("yyyy-MM-dd HH:mm"),
                    //CollectionTime = privatePool.CollectionTime == null ? "" : privatePool.CollectionTime.Value.ToString("yyyy-MM-dd HH:mm"),
                    CreateDate = customer.CreateDate == null ? "" : customer.CreateDate.Value.ToString("yyyy-MM-dd HH:mm"),
                    CollectionTime = privatePool.CollectionTime == null ? customer.CreateDate.Value.ToString("yyyy-MM-dd HH:mm") : privatePool.CollectionTime.Value.ToString("yyyy-MM-dd HH:mm"),
                    OrgName = user.OrgFullName,
                    ProductName = "产品信息",
                    OrgId = user.OrganizationId,
                    ServiceState = productstate.ProductServiceInfoStatus,
                    Self = privatePool.UserId == userId,
                    IsSupplementary = (EnumNeedSupplement)customer.IsSupplementary,
                    AvatarImage = user.AvatarImage,
                    MainCompanyId = company.Id
                },
                true)
                .OrderByPropertyName(queryTeamCustomer_IN.SortField, queryTeamCustomer_IN.IsDESC ? OrderByType.Desc : OrderByType.Asc)
                .OrderByIF(StringUtil.IsNullOrEmpty(queryTeamCustomer_IN.SortField), "CollectionTime desc")
                //相同时间mysql排序会有问题，需要加一列id
                .OrderBy("Id desc")
                .Mapper(it =>
                {
                    ////9.22 取消掩码处理
                    ////9.19 根据 自己客户/直属/下属组织，分别加权限处理
                    //if (it.UserId == user.Id)
                    //{
                    //    //自己的客户 不需要特殊处理
                    //}
                    //else if (it.OrgId == userOrg)
                    //{
                    //    //直属组织下客户 联系信息隐藏，客户名字可以看
                    //    //这里本来也不返回联系信息，所以暂时不用处理了
                    //}
                    //else
                    //{
                    //    //下属组织 联系信息隐藏，客户名字** 处理
                    //    it.CustomerName = CustomerNameHidden(it.CustomerName);
                    //}
                    it.TrackingStageName = ((EnumTrackingStage)it.TrackingStage).GetEnumDescription();
                    it.CustomerSourceName = ((EnumCustomerSource)it.CustomerSource).GetEnumDescription();
                    it.CustomerLevelName = it.CustomerLevel == null ? EnumCustomerLevel.Unknow.GetEnumDescription() : ((EnumCustomerLevel)it.CustomerLevel.Value).GetEnumDescription();
                    it.PeerDataName = it.PeerData == null ? EnumPeerData.Unknow.GetEnumDescription() : ((EnumPeerData)it.PeerData.Value).GetEnumDescription();
                    it.ServiceStateName = it.ServiceState == null ? EnumCustomerProductServiceState.NONE.GetEnumDescription() : ((EnumCustomerProductServiceState)it.ServiceState.Value).GetEnumDescription();
                })
                .ToPageList(queryTeamCustomer_IN.PageNumber, queryTeamCustomer_IN.PageSize, ref total);
            Db.ThenMapper(r, item =>
            {
                item.SubCompanys = Db.Queryable<Db_crm_customer_subcompany>()
                .Where(it => it.Deleted == (int)EnumCustomerDel.NotDel && it.IsMain == (int)EnumCustomerCompanyMain.Sub)
                .Select(it => new CompanySimple_OUT() { Id = it.Id, CompanyName = it.CompanyName, CustomerName = it.CompanyName, CreditCode = it.CreditCode, CustomerId = it.CustomerId, Contacts = it.Contacts })
                .SetContext(x => x.CustomerId, () => item.Id, item).ToList();
                item.RelatedCompanies = Db.Queryable<Db_crm_customer_subcompany_related>()
                .Where(it => it.Deleted == false && it.IsValid == (int)EnumCompanyValid.VALID)
                .Select(it => new SubCompanyRelated_OUT() { Id = it.Id, CompanyName = it.CompanyName, CreditType = it.CreditType, CreditCode = it.CreditCode, CompanyId = it.CompanyId, InValidTime = it.InValidTime, IsValid = it.IsValid, CreateDate = it.CreateDate })
                .SetContext(x => x.CompanyId, () => item.MainCompanyId, item).ToList();
                item.HasContractProduct = Db.Queryable<Db_crm_customer_subcompany>()
               .InnerJoin<Db_crm_contract>((s, c) => c.FirstParty == s.Id)
               .Where((s, c) => s.Deleted == (int)EnumCustomerDel.NotDel && s.IsValid == (int)EnumCompanyValid.VALID && c.Deleted == false)
               .Select((s, c) => new { Id = c.Id, CustomerId = s.CustomerId })
               .SetContext(s => s.CustomerId, () => item.Id, item)
               .Any();

            });
            //#region 用户查看公有/其他人客户数量限制
            //if (queryTeamCustomer_IN.OtherFlag == null || queryTeamCustomer_IN.OtherFlag.Value)
            //{
            //    RedisCache.UserCustomerLimit.CheckLimit(userId, r.Select(r => r.Id).ToList());
            //}
            //else
            //{
            //    foreach (var customer in r)
            //    {
            //        if (customer.UserId != userId)
            //        {
            //            RedisCache.UserCustomerLimit.CheckLimit(userId, customer.Id);
            //        }
            //    }
            //}
            //#endregion
            return r;

        }
        /// <summary>
        /// 预验客户
        /// </summary>
        /// <param name="customerName"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        public PreInspectionCustomerResult_OUT PreInspectionCustomerList(string customerName, string userId)
        {
            /*
             根据查询条件获取预验客户信息列表。（查找当前客户是否存在，是在公有池，还是私有池。）
            (不收数据权限约束。)（一个客户只能查到一条，不查历史的，只查在用的。）
             */
            PreInspectionCustomerResult_OUT result = new PreInspectionCustomerResult_OUT();
            List<PreInspectionCustomer_OUT> r = new List<PreInspectionCustomer_OUT>();
            string msg = "";
            List<string> keyWords = new List<string>();
            List<PreInspectionCustomer> companys = DbOpe_crm_customer_subcompany.Instance.PreInspectionCustomerCompanys(customerName, userId, ref msg, ref keyWords);
            var customerIds = companys.Select(company => company.CustomerId).ToList();
            List<PrivateCustomerSimpleInfo> privateCustomerInfos = GetPrivateCustomerInfo(customerIds).FindAll(c => c.State == (int)EnumCustomerPrivateRelease.Not);
            List<PublicCustomerSimpleInfo> publicCustomerInfos = DbOpe_crm_customer_publicpool.Instance.GetPublicCustomerInfo(customerIds);
            var blackUser = Db.Queryable<Db_crm_customer_blacklistuser>().Select(b => b.UserId).ToList();
            //判断这里面的客户是不是保密的
            var secretCustomerIds = Db.Queryable<Db_v_contract_secret>().Where(s => SqlFunc.ContainsArray(customerIds, s.CustomerId)).Select(s => s.CustomerId).Distinct().ToList();
            foreach (PreInspectionCustomer company in companys)
            {
                PreInspectionCustomer_OUT preInspectionCustomer_OUT = company.MappingTo<PreInspectionCustomer_OUT>();
                preInspectionCustomer_OUT.CompanyId = company.MatchCompanyId;
                var privateExist = privateCustomerInfos.FindAll(p => p.CustomerId == company.CustomerId);
                privateExist.Sort((a, b) => dateMax(b.CollectionTime, b.CreateDate).Value.CompareTo(dateMax(a.CollectionTime, a.CreateDate)));
                var publicExist = publicCustomerInfos.FindAll(p => p.CustomerId == company.CustomerId);
                if (privateExist.Count > 0)
                {
                    preInspectionCustomer_OUT.IsPrivate = true;
                    //preInspectionCustomer_OUT.CustomerName = company.MatchCompanyName;
                    preInspectionCustomer_OUT.CustomerName = privateExist[0].CustomerName;
                    preInspectionCustomer_OUT.MatchCompanyName = company.MatchCompanyName;
                    preInspectionCustomer_OUT.RelatedNames = company.MatchRelatedCompanyName;
                    preInspectionCustomer_OUT.CountryAndCity = company.CountryAndCity;
                    if (!(string.IsNullOrEmpty(privateExist[0].CustomerNum) || privateExist[0].CustomerNum.Length == 0 || secretCustomerIds.Contains(preInspectionCustomer_OUT.CustomerId)))
                    {
                        preInspectionCustomer_OUT.HSIDSort = privateExist[0].CustomerNum.Substring(0, privateExist[0].CustomerNum.Length - 1);
                    }
                    //preInspectionCustomer_OUT.CustomerNum = privateExist[0].CustomerNum;
                    //preInspectionCustomer_OUT.CollectionTime = privateExist[0].CollectionTime;
                    preInspectionCustomer_OUT.UserId = privateExist[0].UserId;
                    //preInspectionCustomer_OUT.RelatedNames = DbOpe_crm_customer_subcompany.Instance.GetCustomerCompanys(preInspectionCustomer_OUT.CustomerId).FindAll(c => c.CompanyName != company.MatchCompanyName).Select(c => c.CompanyName).ToList();
                    var user = Db.Queryable<Db_v_userwithorg>().Where(u => u.Id == privateExist[0].UserId).First();
                    preInspectionCustomer_OUT.UserName = user == null ? "-" : user.UserWithOrgFullName;
                    if (blackUser.Contains(preInspectionCustomer_OUT.UserId))
                    {
                        preInspectionCustomer_OUT.BlackTips = "(北京保存的客户为黑名单客户，禁止客户经理保存联系)";
                    }
                    preInspectionCustomer_OUT.IsMain = company.IsMain == (int)EnumCustomerCompanyMain.Main;
                    r.Add(preInspectionCustomer_OUT);
                }
                else if (publicExist.Count > 0)
                {
                    preInspectionCustomer_OUT.IsPublic = true;
                    //preInspectionCustomer_OUT.CustomerName = company.MatchCompanyName;
                    preInspectionCustomer_OUT.CustomerName = publicExist[0].CustomerName;
                    preInspectionCustomer_OUT.MatchCompanyName = company.MatchCompanyName;
                    preInspectionCustomer_OUT.RelatedNames = company.MatchRelatedCompanyName;
                    preInspectionCustomer_OUT.CountryAndCity = company.CountryAndCity;
                    if (!(string.IsNullOrEmpty(publicExist[0].CustomerNum) || publicExist[0].CustomerNum.Length == 0 || secretCustomerIds.Contains(preInspectionCustomer_OUT.CustomerId)))
                    {
                        preInspectionCustomer_OUT.HSIDSort = publicExist[0].CustomerNum.Substring(0, publicExist[0].CustomerNum.Length - 1);
                    }
                    preInspectionCustomer_OUT.CollectionTime = null;
                    preInspectionCustomer_OUT.UserId = "";
                    preInspectionCustomer_OUT.UserName = "";
                    preInspectionCustomer_OUT.IsMain = company.IsMain == (int)EnumCustomerCompanyMain.Main;
                    //preInspectionCustomer_OUT.RelatedNames = DbOpe_crm_customer_subcompany.Instance.GetCustomerCompanys(preInspectionCustomer_OUT.CustomerId).FindAll(c => c.CompanyName != company.MatchCompanyName).Select(c => c.CompanyName).ToList();
                    r.Add(preInspectionCustomer_OUT);
                }
                else
                {
                    throw new ApiException("客户信息查询失败");
                }

            }
            result.list = r;
            result.msg = msg;
            result.KeyWords = keyWords;
            return result;
        }

        /// <summary>
        /// 预验客户是是否已经签约
        /// </summary>
        /// <param name="checkCustomer_In"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        public string PreInspectionCustomerHasSigned(PreInspectionCustomerHasSigned_IN checkCustomer_In, string userId)
        {
            if (string.IsNullOrEmpty(checkCustomer_In.Reason))
                throw new ApiException("请输入查看原因");
            //检查用户查询次数是否已达上限，相同客户Id不计算
            var customerIds = Db.Queryable<Db_crm_customer_querysign_record>()
                .Where(e => e.UserId == userId)
                .Where(e => SqlFunc.ToDateShort(e.QueryDate) == DateTime.Now.Date)
                .Where(e => e.Result == EnumCustomerSignStage.Sign)
                .GroupBy(e => e.CustomerId)
                .Select(e => e.CustomerId)
                .ToList();
            if (customerIds.Count == 5 && !customerIds.Contains(checkCustomer_In.CustomerId))
                throw new ApiException("已超出今日查看数量限制");
            //验证是否公有 v_customer
            if (Db.Queryable<Db_v_customer>().Any(e => e.CustomerId == checkCustomer_In.CustomerId && e.publicPool == 1))
            {
                Db_crm_customer_querysign_record record = new Db_crm_customer_querysign_record()
                {
                    UserId = userId,
                    CustomerId = checkCustomer_In.CustomerId,
                    QueryDate = DateTime.Now,
                    Reason = checkCustomer_In.Reason,
                    Result = EnumCustomerSignStage.UnRetain,
                };
                DbOpe_crm_customer_querysign_record.Instance.InsertData(record);
                return EnumCustomerSignStage.UnRetain.GetEnumDescription();
            }
            //查验是否是黑名单客户
            if (Db.Queryable<Db_crm_customer_blacklistuser>().LeftJoin<Db_v_customer>((black, view) => view.UserId == black.UserId).Any((black, view) => view.CustomerId == checkCustomer_In.CustomerId && black.Deleted == false))
                return "北京保存的客户为黑名单客户，禁止客户经理保存联系";
            //查询客户信息
            var customer = DbOpe_crm_customer.Instance.GetDataById(checkCustomer_In.CustomerId);
            //查询客户的所有子公司Id
            var subCompanyIds = DbOpe_crm_customer_subcompany.Instance.GetCustomerCompanys(checkCustomer_In.CustomerId).Select(e => e.Id).ToList();
            //判断客户子公司是否存在私密，如果存在则返回状态为保留，不记录查询次数
            if (Db.Queryable<Db_v_contract_secret>().Where(e => subCompanyIds.Contains(e.FirstParty)).Any())
            {
                Db_crm_customer_querysign_record record = new Db_crm_customer_querysign_record()
                {
                    UserId = userId,
                    CustomerId = checkCustomer_In.CustomerId,
                    QueryDate = DateTime.Now,
                    Reason = checkCustomer_In.Reason,
                    Result = EnumCustomerSignStage.Retain,
                };
                DbOpe_crm_customer_querysign_record.Instance.InsertData(record);
                return EnumCustomerSignStage.Retain.GetEnumDescription();
            }

            //如果是到账成功，认为是签约客户，需要记录查询次数
            if (customer.TrackingStage == EnumTrackingStage.Received.ToInt())
            {
                Db_crm_customer_querysign_record record = new Db_crm_customer_querysign_record()
                {
                    UserId = userId,
                    CustomerId = checkCustomer_In.CustomerId,
                    QueryDate = DateTime.Now,
                    Reason = checkCustomer_In.Reason,
                    Result = EnumCustomerSignStage.Sign,
                };
                DbOpe_crm_customer_querysign_record.Instance.InsertData(record);
                return EnumCustomerSignStage.Sign.GetEnumDescription();
            }
            else
            {
                Db_crm_customer_querysign_record record = new Db_crm_customer_querysign_record()
                {
                    UserId = userId,
                    CustomerId = checkCustomer_In.CustomerId,
                    QueryDate = DateTime.Now,
                    Reason = checkCustomer_In.Reason,
                    Result = EnumCustomerSignStage.Retain,
                };
                DbOpe_crm_customer_querysign_record.Instance.InsertData(record);
                return EnumCustomerSignStage.Retain.GetEnumDescription();
            }

        }
        /// <summary>
        /// 获取归属客户信息
        /// </summary>
        /// <param name="customerName"></param>
        /// <returns></returns>
        public List<AscriptionCustomer_OUT> AscriptionCustomerList(string customerName)
        {
            /*
             该功能可对客户进行统计当前客户的具体归属情况（相当于只看私有池）
             客户归属列表默认为空态，点击查询后才显示搜索的客户信息，若数据库无此客户则显示“无法搜索到当前客户内容”
            */
            List<AscriptionCustomer_OUT> r = new List<AscriptionCustomer_OUT>();
            List<Db_crm_customer_subcompany> companys = DbOpe_crm_customer_subcompany.Instance.AscriptionCustomerCompanys(customerName);
            var customerIds = companys.Select(company => company.CustomerId).ToList();
            List<PrivateCustomerSimpleInfo> privateCustomerInfos = GetPrivateCustomerInfo(customerIds, "", false, true, false);
            foreach (Db_crm_customer_subcompany company in companys)
            {
                AscriptionCustomer_OUT preInspectionCustomer_OUT = company.MappingTo<AscriptionCustomer_OUT>();
                preInspectionCustomer_OUT.CompanyId = company.Id;
                var privateExist = privateCustomerInfos.FindAll(p => p.CustomerId == company.CustomerId);
                privateExist.Sort((a, b) => dateMax(b.CollectionTime, b.CreateDate).Value.CompareTo(dateMax(a.CollectionTime, a.CreateDate)));
                if (privateExist.Count > 0)
                {
                    preInspectionCustomer_OUT.CustomerName = company.CompanyName;
                    preInspectionCustomer_OUT.CustomerNum = privateExist[0].CustomerNum;
                    //preInspectionCustomer_OUT.CollectionTime = privateExist[0].CollectionTime;
                    //preInspectionCustomer_OUT.ProtectionDeadline = privateExist[0].ProtectionDeadline;
                    preInspectionCustomer_OUT.CreateUser = privateExist[0].CreateUser;
                    var cUser = Db.Queryable<Db_v_userwithorg>().Where(u => u.Id == privateExist[0].CreateUser).First();
                    preInspectionCustomer_OUT.CreateUserName = cUser == null ? "-" : cUser.UserWithOrgFullName;
                    preInspectionCustomer_OUT.UserId = privateExist[0].State == (int)EnumCustomerPrivateRelease.Not ? privateExist[0].UserId : "-";
                    var user = Db.Queryable<Db_v_userwithorg>().Where(u => u.Id == privateExist[0].UserId).First();
                    preInspectionCustomer_OUT.UserName = (privateExist[0].State != (int)EnumCustomerPrivateRelease.Not || user == null) ? "-" : user.UserWithOrgFullName;
                    preInspectionCustomer_OUT.IsRetain = privateExist[0].State == (int)EnumCustomerPrivateRelease.Not;
                    r.Add(preInspectionCustomer_OUT);
                }

            }
            return r;
        }
        /// <summary>
        /// 获取归属客户信息
        /// </summary>
        /// <param name="ascriptionCustomer_IN"></param>
        /// <returns></returns>
        public ApiTableOut<AscriptionCustomer_OUT> AscriptionCustomerList(AscriptionCustomer_IN ascriptionCustomer_IN)
        {
            /*
             该功能可对客户进行统计当前客户的具体归属情况（相当于只看私有池）
             客户归属列表默认为空态，点击查询后才显示搜索的客户信息，若数据库无此客户则显示“无法搜索到当前客户内容”

            20240718 改为客户历史保留记录查询 主要查 crm_org_log 表
            */
            ApiTableOut<AscriptionCustomer_OUT> r = new ApiTableOut<AscriptionCustomer_OUT>();
            if (string.IsNullOrEmpty(ascriptionCustomer_IN.CustomerName))
            {
                throw new ApiException("关键词不能为空");
            }
            List<Db_crm_customer_subcompany> companys = DbOpe_crm_customer_subcompany.Instance.AscriptionCustomerCompanys(ascriptionCustomer_IN.CustomerName);
            var customerIds = companys.Select(company => company.CustomerId).ToList();
            int total = 0;
            r.Data = Db.Queryable(Db.Reportable(companys).ToQueryable())
                .LeftJoin<Db_crm_customer_org_log>((company, log) => log.CustomerId == company.CustomerId)
                .LeftJoin<Db_crm_customer_privatepool>((company, log, privatep) => privatep.Id == log.RelatedPrivatePoolId)
                .LeftJoin<Db_crm_customer_subcompany>((company, log, privatep, main) => main.CustomerId == company.CustomerId && main.IsMain == (int)EnumCustomerCompanyMain.Main)
                .LeftJoin<Db_v_userwithorg>((company, log, privatep, main, user) => log.UserId == user.Id && user.Deleted == false)
                .LeftJoin<Db_crm_customer>((company, log, privatep, main, user, customer) => main.CustomerId == customer.Id)
                .Select((company, log, privatep, main, user, customer) => new AscriptionCustomer_OUT()
                {
                    CompanyId = company.Id,
                    CustomerId = company.CustomerId,
                    CompanyName = company.CompanyName,
                    CustomerName = main.CompanyName,
                    CollectionTime = privatep.CollectionTime == null ? privatep.CreateDate : privatep.CollectionTime,
                    IsRetain = log.IsValid == 1,
                    InvalidDate = log.InvalidDate,
                    CreateType = log.CreateType,
                    UserId = privatep.UserId,
                    UserName = user.UserWithOrgFullName,
                    UpdateDate = log.UpdateDate == null ? log.CreateDate : log.UpdateDate,
                    IsSignCustomer = customer.TrackingStage == (int)EnumTrackingStage.Received
                })
                .MergeTable()
                .OrderByDescending(it => it.UpdateDate)
                .Mapper(it =>
                {
                    it.CreateTypeString = ((EnumCustomerLogCreateType)it.CreateType).GetEnumDescription();
                })
                .ToPageList(ascriptionCustomer_IN.PageNumber, ascriptionCustomer_IN.PageSize, ref total);
            r.Total = total;
            return r;
            //List<PrivateCustomerSimpleInfo> privateCustomerInfos = GetPrivateCustomerInfo(customerIds, "", false, true, false);
            //foreach (Db_crm_customer_subcompany company in companys)
            //{
            //    AscriptionCustomer_OUT preInspectionCustomer_OUT = company.MappingTo<AscriptionCustomer_OUT>();
            //    preInspectionCustomer_OUT.CompanyId = company.Id;
            //    var privateExist = privateCustomerInfos.FindAll(p => p.CustomerId == company.CustomerId);
            //    privateExist.Sort((a, b) => dateMax(b.CollectionTime, b.CreateDate).Value.CompareTo(dateMax(a.CollectionTime, a.CreateDate)));
            //    if (privateExist.Count > 0)
            //    {
            //        preInspectionCustomer_OUT.CustomerName = company.CompanyName;
            //        preInspectionCustomer_OUT.CustomerNum = privateExist[0].CustomerNum;
            //        //preInspectionCustomer_OUT.CollectionTime = privateExist[0].CollectionTime;
            //        //preInspectionCustomer_OUT.ProtectionDeadline = privateExist[0].ProtectionDeadline;
            //        preInspectionCustomer_OUT.CreateUser = privateExist[0].CreateUser;
            //        var cUser = Db.Queryable<Db_v_userwithorg>().Where(u => u.Id == privateExist[0].CreateUser).First();
            //        preInspectionCustomer_OUT.CreateUserName = cUser == null ? "-" : cUser.UserWithOrgFullName;
            //        preInspectionCustomer_OUT.UserId = privateExist[0].State == (int)EnumCustomerPrivateRelease.Not ? privateExist[0].UserId : "-";
            //        var user = Db.Queryable<Db_v_userwithorg>().Where(u => u.Id == privateExist[0].UserId).First();
            //        preInspectionCustomer_OUT.UserName = (privateExist[0].State != (int)EnumCustomerPrivateRelease.Not || user == null) ? "-" : user.UserWithOrgFullName;
            //        preInspectionCustomer_OUT.IsRetain = privateExist[0].State == (int)EnumCustomerPrivateRelease.Not;
            //        r.Add(preInspectionCustomer_OUT);
            //    }

            //}
        }
        public DateTime? dateMax(DateTime? date, DateTime? compareDate)
        {
            if (date == null)
            {
                return compareDate;
            }
            if (compareDate == null)
            {
                return date;
            }
            if (date > compareDate)
            {
                return date;
            }
            else
            {
                return compareDate;
            }
        }
        /// <summary>
        /// 获取客户的领取历史记录（只有在公有池的可以看）（11.13 新建客户不算领取）
        /// </summary>
        /// <param name="customerId"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        public List<QueryCustomerGetHistory_OUT> QueryCustomerGetHistory(string customerId, string userId)
        {
            if (!DbOpe_crm_customer_publicpool.Instance.CheckInPool(customerId, true, userId))
            {
                throw new ApiException("操作失败，未在公有池找到客户");
            }
            var getTypes = new List<int>() {
                //(int)EnumCustomerPrivateCreateType.Create
                (int)EnumCustomerPrivateCreateType.GetPublic
                ,(int)EnumCustomerPrivateCreateType.Transfer
                ,(int)EnumCustomerPrivateCreateType.Split
            };
            return Queryable
                   .LeftJoin<Db_crm_customer_subcompany>((privatePool, company) => privatePool.CustomerId == company.CustomerId && company.IsMain == (int)EnumCustomerCompanyMain.Main)
                   .LeftJoin<Db_crm_customer>((privatePool, company, customer) => company.CustomerId == customer.Id)
                   .LeftJoin<Db_sys_user>((privatePool, company, customer, user) => privatePool.UserId == user.Id)
                   .Where((privatePool, company, customer, user) => customerId == privatePool.CustomerId)
                   .Where((privatePool, company, customer, user) => privatePool.Deleted == (int)EnumCustomerDel.NotDel)
                   .Where((privatePool, company, customer) => SqlFunc.ContainsArray(getTypes, privatePool.CreateType))
                   .OrderByDescending((privatePool, company, customer, user) => privatePool.ReleaseTime)
                   .Select((privatePool, company, customer, user) => new QueryCustomerGetHistory_OUT()
                   {
                       UserName = user.Name,
                       CollectionDate = privatePool.CollectionTime == null ? "" : privatePool.CollectionTime.Value.ToString("yyyy-MM-dd HH:mm"),
                       ReleaseDate = privatePool.ReleaseTime == null ? "" : privatePool.ReleaseTime.Value.ToString("yyyy-MM-dd HH:mm"),
                       ReleaseType = (EnumCustomerPrivateReleaseType)privatePool.ReleaseType,
                       ReleaseReason = privatePool.ReleaseReason,
                   }, true)
                   .ToList();
        }
        /// <summary>
        /// 获取领取次数
        /// </summary>
        /// <param name="customerId"></param>
        /// <returns></returns>
        public int QueryCustomerGetNum(string customerId)
        {
            return QueryCustomerGetNum(new List<string>() { customerId })[customerId];
        }
        /// <summary>
        /// 获取领取次数 11.13 新建客户不算领取
        /// </summary>
        /// <param name="customerIds"></param>
        /// <returns></returns>
        public Dictionary<string, int> QueryCustomerGetNum(List<string> customerIds)
        {
            Dictionary<string, int> dic = new Dictionary<string, int>();
            var getTypes = new List<int>() {
                //(int)EnumCustomerPrivateCreateType.Create
                (int)EnumCustomerPrivateCreateType.GetPublic
                ,(int)EnumCustomerPrivateCreateType.Transfer
                ,(int)EnumCustomerPrivateCreateType.Split
            };
            var query = Queryable
                   .LeftJoin<Db_crm_customer_subcompany>((privatePool, company) => privatePool.CustomerId == company.CustomerId && company.IsMain == (int)EnumCustomerCompanyMain.Main)
                   .LeftJoin<Db_crm_customer>((privatePool, company, customer) => company.CustomerId == customer.Id)
                   .Where((privatePool, company, customer) => SqlFunc.ContainsArray(customerIds, privatePool.CustomerId))
                   .Where((privatePool, company, customer) => privatePool.Deleted == (int)EnumCustomerDel.NotDel)
                   .Where((privatePool, company, customer) => SqlFunc.ContainsArray(getTypes, privatePool.CreateType))
                   .GroupBy((privatePool, company, customer) => privatePool.CustomerId)
                   .Select(
                    (privatePool, company, customer) => new
                    {
                        CustomerId = privatePool.CustomerId,
                        Value = SqlFunc.AggregateCount(privatePool.Id)
                    }
                   ).MergeTable()
                   .ToDictionary(e => e.CustomerId, e => e.Value);
            foreach (var customerId in customerIds)
            {
                if (!dic.ContainsKey(customerId))
                {
                    if (query.ContainsKey(customerId))
                    {
                        dic.Add(customerId, query[customerId].ToInt());
                    }
                    else
                    {
                        dic.Add(customerId, 0);
                    }
                }

            }
            return dic;
        }
        /// <summary>
        /// 根据客户Id获取相关记录（私有池、公有池、团队池）
        /// </summary>
        /// <param name="customerRecord_IN"></param>
        /// <param name="userId"></param>
        /// <param name="total"></param>
        /// <returns></returns>
        public List<CustomerRecord_OUT> GetRecordsByCustomerId(CustomerRecord_IN customerRecord_IN, string userId, ref int total)
        {
            var cai = DbOpe_crm_customer.Instance.CheckCustomerAuthInfo(customerRecord_IN.CustomerId, userId);
            string customerUser = userId;
            if (!cai.CanView)
            {
                throw new ApiException("未找到对应的客户信息");
            }
            else
            {
                //这里要是团队池的话，要查现在这个负责人的相关记录，而不是管理者自己的
                if (cai.IsOrgCustomer)
                {
                    customerUser = cai.UserId;
                }
            }
            List<string> imageExtList = new List<string>()
            {
                "bmp", "jpg", "jpeg", "webp", "png"
            };

            var releaseQ = SearchCustomerReleaseRecords(customerRecord_IN.CustomerId);
            var delayQ = DbOpe_crm_customer_privatepool_delay.Instance.SearchCustomerDelayRecords(customerRecord_IN.CustomerId);
            var createQ = DbOpe_crm_customer.Instance.SearchCustomerCreateRecords(customerRecord_IN.CustomerId);
            var trackingRecordQ = DbOpe_crm_trackingrecord.Instance.SearchCustomerTrackingRecords(customerRecord_IN.CustomerId, customerUser);
            var scheduleQ = DbOpe_crm_schedule.Instance.SearchCustomerScheduleRecords(customerRecord_IN.CustomerId, customerUser);
            var list = Db.UnionAll(releaseQ, delayQ, createQ, trackingRecordQ, scheduleQ)
                .Select<CustomerRecord_OUT>()
                .WhereIF(customerRecord_IN.EnumCustomerLogType != EnumCustomerLogType.ALL, o => (int)customerRecord_IN.EnumCustomerLogType == o.EnumCustomerLogType)
                .WhereIF(customerRecord_IN.RecordDateStart != null, o => o.RecordDate != null && o.RecordDate >= customerRecord_IN.RecordDateStart)
                .WhereIF(customerRecord_IN.RecordDateEnd != null, o => o.RecordDate != null && o.RecordDate <= customerRecord_IN.RecordDateEnd)
                .OrderByDescending(o => new { o.RecordDate, o.Id })
                .ToPageList(customerRecord_IN.PageNumber, customerRecord_IN.PageSize, ref total);
            Db.ThenMapper(list, item =>
            {
                item.ReleaseRecord = Db.Queryable<Db_crm_customer_privatepool>()
                .LeftJoin<Db_crm_customer>((pool, customer) => pool.CustomerId == customer.Id && customer.Deleted == (int)EnumCustomerDel.NotDel)
                .InnerJoin<Db_crm_customer_subcompany>((pool, customer, subcompany) => pool.CustomerId == subcompany.CustomerId && subcompany.IsMain == (int)EnumCustomerCompanyMain.Main && subcompany.Deleted == (int)EnumCustomerDel.NotDel)
                .LeftJoin<Db_sys_user>((pool, customer, subcompany, user) => user.Id == pool.UserId && !user.Deleted)
                .Select((pool, customer, subcompany, user) => new PrivateReleaseRecord_Out()
                {
                    Id = pool.Id,
                    CustomerId = pool.CustomerId,
                    CustomerName = subcompany.CompanyName,
                    UserId = pool.UserId,
                    UserName = user.Name,
                    CollectionTime = pool.CollectionTime,
                    ProtectionDeadline = pool.ProtectionDeadline,
                    State = (EnumCustomerPrivateRelease)pool.State,
                    ReleaseTime = pool.ReleaseTime,
                    ReleaseReason = pool.ReleaseReason,
                    DelayTimes = pool.DelayTimes,
                    ReleaseType = (EnumCustomerPrivateReleaseType)pool.ReleaseType,
                    LastUpdateDate = pool.UpdateDate == null ? pool.CreateDate : pool.UpdateDate
                })
                .SetContext(pool => pool.Id, () => item.Id, item).FirstOrDefault();
                item.DelayRecord = Db.Queryable<Db_crm_customer_privatepool_delay>()
                .LeftJoin<Db_crm_customer>((delay, customer) => delay.CustomerId == customer.Id && customer.Deleted == (int)EnumCustomerDel.NotDel)
                .InnerJoin<Db_crm_customer_subcompany>((delay, customer, subcompany) => delay.CustomerId == subcompany.CustomerId && subcompany.IsMain == (int)EnumCustomerCompanyMain.Main && subcompany.Deleted == (int)EnumCustomerDel.NotDel)
                .LeftJoin<Db_sys_user>((delay, customer, subcompany, user) => user.Id == delay.UserId && !user.Deleted)
                .Select((delay, customer, subcompany, user) => new DelayRecord_Out()
                {
                    Id = delay.Id,
                    CustomerId = delay.CustomerId,
                    CustomerPrivatePoolId = delay.CustomerPrivatePoolId,
                    CustomerName = subcompany.CompanyName,
                    UserId = delay.UserId,
                    UserName = user.Name,
                    ExecuteDelayDate = delay.ExecuteDelayDate,
                    TimeExpand = delay.TimeExpand,
                    ProtectionDeadlineCopy = delay.ProtectionDeadlineCopy,
                    LeftDelayTimesCopy = delay.LeftDelayTimesCopy,
                    CreateUser = delay.CreateUser,
                    DelayTimes = delay.DelayTimes,
                    CreateDate = delay.CreateDate
                })
                .SetContext(delay => delay.Id, () => item.Id, item).FirstOrDefault();
                item.TrackingRecord = Db.Queryable<Db_crm_trackingrecord>()
                .LeftJoin<Db_sys_user>((trackingRecord, user) => user.Id == trackingRecord.UserId && !user.Deleted)
                .LeftJoin<Db_crm_contract>((trackingRecord, user, contract) => contract.Id == trackingRecord.ContractId && contract.Deleted == false)
                .LeftJoin<Db_v_userwithorg>((trackingRecord, user, contract, vu) => user.Id.Equals(vu.Id))
                .Where((trackingRecord, user, contract) => trackingRecord.Deleted == false)
                .Select((trackingRecord, user, contract, vu) => new TrackingRecord_Out()
                {
                    Id = trackingRecord.Id,
                    CustomerId = trackingRecord.CustomerId,
                    CustomerDataSource = trackingRecord.CustomerDataSource,
                    CustomerName = trackingRecord.CustomerName,
                    UserId = trackingRecord.UserId,
                    UserName = user.Name,
                    UserOrganizationName = vu.OrgFullName,
                    ContractId = trackingRecord.ContractId,
                    ContractName = contract.ContractName,
                    UserOrganizationId = user.OrganizationId,
                    TrackingType = trackingRecord.TrackingType,
                    TrackingPurpose = trackingRecord.TrackingPurpose,
                    TrackingStage = trackingRecord.TrackingStage,
                    Remark = trackingRecord.Remark,
                    CreateDate = trackingRecord.CreateDate,
                    CanEdit = trackingRecord.CanEdit
                }).Mapper(it =>
                {
                    it.CanEdit = it.CanEdit.IsNull() ? true : it.CanEdit;
                    /*List<SysOrganizationTree> orgTreeList = Db.Queryable<SysOrganizationTree>().ToParentList(ot => ot.ParentId, it.UserOrganizationId);
                    if (orgTreeList is not null and { Count: > 0 })
                    {
                        orgTreeList.Reverse();
                        string orgAllName = string.Empty;
                        foreach (var orgItem in orgTreeList)
                        {
                            if (!string.IsNullOrEmpty(orgItem.OrgName))
                            {
                                orgAllName = orgAllName.Equals(string.Empty) ? orgItem.OrgName : orgAllName + "/" + orgItem.OrgName;
                            }
                        }
                        if (!string.IsNullOrEmpty(orgAllName))
                        {
                            it.UserOrganizationName = orgAllName;
                        }
                    }*/
                    //it.AttachFiles = Db.Queryable<Db_crm_attachfile>()
                    //    .Where(e => e.Type == AttachEnumOption.TrackingRecord.ToIntNullable() && e.SubjectId.Equals(it.Id)).Select<AttachFile_Out>().ToList();
                    var attachFiles = Db.Queryable<Db_crm_attachfile>()
                   .Where(scl => scl.Type == AttachEnumOption.TrackingRecord.ToIntNullable() && scl.Deleted == false)
                   .Where(scl => scl.SubjectId == it.Id)
                   .OrderBy(scl => scl.CreateDate, OrderByType.Desc)
                   .Select(s => new BM_FileInfoAndFileExtension
                   {
                       Id = s.Id,
                       FileName = s.FileName,
                       FilePath = s.FilePath,
                       FileExtension = s.FileExtension
                   })
                   .ToList();
                    List<object> imageAttachFile = new();
                    List<object> documentAttachFile = new();
                    foreach (var file in attachFiles)
                    {
                        if (imageExtList.Contains(file.FileExtension))
                        {
                            imageAttachFile.Add(new BM_FileInfo
                            {
                                Id = file.Id,
                                FileName = file.FileName,
                                FilePath = file.FilePath
                            });
                        }
                        else
                        {
                            documentAttachFile.Add(new BM_FileInfo
                            {
                                Id = file.Id,
                                FileName = file.FileName,
                                FilePath = file.FilePath
                            });
                        }
                    }
                    it.ImageAttachFile = imageAttachFile;
                    it.DocumentAttachFile = documentAttachFile;
                })
               .SetContext(trackingRecord => trackingRecord.Id, () => item.Id, item).FirstOrDefault();
                item.ScheduleRecord = Db.Queryable<Db_crm_schedule>()
                .LeftJoin<Db_sys_user>((schedule, user) => user.Id == schedule.UserId && !user.Deleted)
                .LeftJoin<Db_crm_contract>((schedule, user, contract) => contract.Id == schedule.ContractId && contract.Deleted == false)
                .Where((schedule, user, contract) => schedule.Deleted == false)
                .Select((schedule, user, contract) => new ScheduleRecord_Out()
                {
                    Id = schedule.Id,
                    CustomerId = schedule.CustomerId,
                    CustomerDataSource = schedule.CustomerDataSource,
                    CustomerName = schedule.CustomerName,
                    UserId = schedule.UserId,
                    UserName = user.Name,
                    ContractId = schedule.ContractId,
                    ContractName = contract.ContractName,
                    TrackingType = schedule.TrackingType,
                    TrackingPurpose = schedule.TrackingPurpose,
                    TrackingStage = schedule.TrackingStage,
                    Remark = schedule.Remark,
                    CreateDate = schedule.CreateDate,
                    VisitorTime = schedule.VisitorTimeStart,
                    State = schedule.State,
                    TrackingEvents = schedule.TrackingEvents,
                    CanEdit = schedule.CanEdit
                }).Mapper(it =>
                {
                    it.CanEdit = it.CanEdit.IsNull() ? true : it.CanEdit;
                    var attachFiles = Db.Queryable<Db_crm_schedule_attachfile>()
                                       .Where(scl => scl.Deleted == false && scl.ScheduleId == it.Id)
                                       .OrderBy(scl => scl.CreateDate, OrderByType.Desc)
                                       .Select(s => new BM_FileInfoAndFileExtension
                                       {
                                           Id = s.Id,
                                           FileName = s.FileName,
                                           FilePath = s.FilePath,
                                           FileExtension = s.FileExtension
                                       })
                                       .ToList();
                    List<object> imageAttachFile = new();
                    List<object> documentAttachFile = new();
                    foreach (var file in attachFiles)
                    {
                        if (imageExtList.Contains(file.FileExtension))
                        {
                            imageAttachFile.Add(new BM_FileInfo
                            {
                                Id = file.Id,
                                FileName = file.FileName,
                                FilePath = file.FilePath
                            });
                        }
                        else
                        {
                            documentAttachFile.Add(new BM_FileInfo
                            {
                                Id = file.Id,
                                FileName = file.FileName,
                                FilePath = file.FilePath
                            });
                        }
                    }
                    it.ImageAttachFile = imageAttachFile;
                    it.DocumentAttachFile = documentAttachFile;
                })
                .SetContext(schedule => schedule.Id, () => item.Id, item).FirstOrDefault();
            });
            return list;
        }
        public ISugarQueryable<object> SearchCustomerReleaseRecords(string customerId)
        {
            var q = Queryable
                .LeftJoin<Db_crm_customer>((pool, customer) => pool.CustomerId == customer.Id && customer.Deleted == (int)EnumCustomerDel.NotDel)
                .InnerJoin<Db_crm_customer_subcompany>((pool, customer, subcompany) => pool.CustomerId == subcompany.CustomerId && subcompany.IsMain == (int)EnumCustomerCompanyMain.Main && subcompany.Deleted == (int)EnumCustomerDel.NotDel)
                .LeftJoin<Db_sys_user>((pool, customer, subcompany, user) => user.Id == pool.UserId && !user.Deleted)
                .Where((pool, customer, subcompany, user) => pool.State != (int)EnumCustomerPrivateRelease.Not && pool.State != (int)EnumCustomerPrivateRelease.Merge)
                .Where((pool, customer, subcompany, user) => pool.CustomerId == customerId)
                .Where((pool, customer, subcompany, user) => pool.Deleted == (int)EnumCustomerDel.NotDel)
                 .Select((pool, customer, subcompany, user) => (object)new
                 {
                     Id = pool.Id,
                     DataOpUserId = pool.UserId,
                     RecordDate = SqlFunc.ToDate(pool.ReleaseTime.Value),
                     EnumCustomerLogType = EnumCustomerLogType.Release,
                     UserName = user.Name,
                     CustomerId = pool.CustomerId,
                     AvatarImage = user.AvatarImage
                 });
            return q;
        }
        /// <summary>
        /// 获取私有池客户数量及昨日变化
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public GetUserCustomerAbstract_OUT GetUserCustomerAbstrct(string userId)
        {
            DateTime today = DateTime.Parse(DateTime.Today.ToString("yyyy-MM-dd"));
            DateTime yesterday = DateTime.Parse(DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd"));
            GetUserCustomerAbstract_OUT getUserCustomerAbstract_OUT = new GetUserCustomerAbstract_OUT();
            //私有池总记录（包括已经释放的）
            var totalData = GetDataList(p => p.UserId == userId).ToList();
            //私有池未释放的客户
            var notReleaseData = totalData.FindAll(p =>
                p.State == (int)EnumCustomerPrivateRelease.Not
            ).ToList();
            //昨日新增的客户（领取或者创建）
            var yesterdayAddData = notReleaseData.FindAll(p =>
                p.CollectionTime != null && p.CollectionTime.Value.GetDaysStart() == yesterday.GetDaysStart()
                || p.CreateDate.Value.GetDaysStart() == yesterday.GetDaysStart()
            ).ToList();
            //今日新增的客户（领取或者创建）
            var todayAddData = notReleaseData.FindAll(p =>
                p.CollectionTime != null && p.CollectionTime.Value.GetDaysStart() == today.GetDaysStart()
                || p.CreateDate.Value.GetDaysStart() == today.GetDaysStart()
            ).ToList();
            //昨日释放的客户
            var yesterdayReleaseData = totalData.FindAll(p =>
                p.ReleaseTime != null && p.ReleaseTime.Value.GetDaysStart() == yesterday.GetDaysStart()
            ).ToList();
            //昨日新签约的客户
            //var yesterdaySignData = Queryable
            // .LeftJoin<Db_crm_customer>((privatePool, customer) => privatePool.CustomerId == customer.Id)
            // .Where((privatePool, customer) => privatePool.State == (int)EnumCustomerPrivateRelease.Not)
            // .Where((privatePool, customer) => customer.IsMerge == (int)EnumCustomerMerge.NotMerge)
            // .Where((privatePool, customer) => privatePool.Deleted == (int)EnumCustomerDel.NotDel && customer.Deleted == (int)EnumCustomerDel.NotDel)
            // .Where((privatePool, customer) => privatePool.UserId == userId)
            // .Where((privatePool, customer) =>
            //     SqlFunc.Subqueryable<Db_crm_customer_subcompany>()
            //     .LeftJoin<Db_crm_contract>((company, contract) => company.Id == contract.FirstParty && contract.Deleted == false)
            //     .LeftJoin<Db_crm_contract_audit>((company, contract,audit) => contract.Id == audit.ContractId && audit.Deleted == false)
            //     .Where((company, contract, audit) => company.CustomerId == customer.Id && contract.ContractStatus == (int)EnumContractStatus.Pass)
            //     .Where((company, contract, audit) => audit.ReviewerDate!=null&&SqlFunc.DateIsSame(audit.ReviewerDate.Value,yesterday, DateType.Day))
            //     .Any()
            // )
            // .Select((privatePool, customer) => privatePool)
            // .ToList();
            var yesterdayQuery = DbOpe_crm_contract.Instance.GetSignDataQuery(new SalesAnalyseStatisticsParams()
            {
                UserId = userId,
                DateStart = yesterday.GetDaysStart(),
                DateEnd = yesterday.GetDaysEnd()
            });
            var yesterdaySignDataCount = Db.Queryable(yesterdayQuery).Select(t => SqlFunc.AggregateDistinctCount(t.CustomerId)).Count();
            //今日签约的客户
            var todayQuery = DbOpe_crm_contract.Instance.GetSignDataQuery(new SalesAnalyseStatisticsParams()
            {
                UserId = userId,
                DateStart = today.GetDaysStart(),
                DateEnd = today.GetDaysEnd()
            });
            var todaySignDataCount = Db.Queryable(todayQuery).Select(t => SqlFunc.AggregateDistinctCount(t.CustomerId)).Count();
            //昨日合同到期的客户（释放+未释放）
            var yesterdayOutSignData = Queryable
             .LeftJoin<Db_crm_customer>((privatePool, customer) => privatePool.CustomerId == customer.Id)
             //.Where((privatePool, customer) => privatePool.State == (int)EnumCustomerPrivateRelease.Not)
             .Where((privatePool, customer) => customer.IsMerge == (int)EnumCustomerMerge.NotMerge)
             .Where((privatePool, customer) => privatePool.Deleted == (int)EnumCustomerDel.NotDel && customer.Deleted == (int)EnumCustomerDel.NotDel)
             .Where((privatePool, customer) => privatePool.UserId == userId)
             .Where((privatePool, customer) =>
                 SqlFunc.Subqueryable<Db_crm_customer_subcompany>()
                 .LeftJoin<Db_crm_contract>((company, contract) => company.Id == contract.FirstParty && contract.Deleted == false)
                 .Where((company, contract) => company.CustomerId == customer.Id && contract.ProtectionDeadline != null && SqlFunc.DateIsSame(contract.ProtectionDeadline.Value, yesterday, DateType.Day))
                 .Any()
             )
             .Select((privatePool, customer) => privatePool)
             .ToList();
            //昨日合同到期且尚未释放
            var yesterdayOutSignNotReleaseData = yesterdayOutSignData.FindAll(p => p.State == (int)EnumCustomerPrivateRelease.Not).ToList();

            //今日跟踪客户数
            var trackingTodayCount = Db.Queryable<Db_crm_trackingrecord>()
                .Where(t => SqlFunc.DateIsSame(t.CreateDate.Value, today, DateType.Day))
                .Where(t => t.Deleted == false)
                .Select(t => SqlFunc.AggregateDistinctCount(t.CustomerId))
                .Count();
            //昨日跟踪客户数
            var trackingYesterdayCount = Db.Queryable<Db_crm_trackingrecord>()
                .Where(t => SqlFunc.DateIsSame(t.CreateDate.Value, yesterday, DateType.Day))
                .Where(t => t.Deleted == false)
                .Select(t => SqlFunc.AggregateDistinctCount(t.CustomerId))
                .Count();

            //总数：未释放的所有客户数
            getUserCustomerAbstract_OUT.PrivateCustomerTotal = notReleaseData.Count;
            //总数变化：昨日新增的客户 - 昨日释放的客户
            getUserCustomerAbstract_OUT.PrivateCustomerChange = yesterdayAddData.Count - yesterdayReleaseData.Count;
            ////签约客户数：有合同且在合同保护期内的客户
            //getUserCustomerAbstract_OUT.SignTotal = GetSignPrivateCustomerNum(userId);


            //20250228 签约客户数：必须是到账的才算
            //到账客户数
            var recieveTotal = GetRecievePrivateCustomerNum(userId);
            getUserCustomerAbstract_OUT.SignTotal = recieveTotal;

            //20250228 保留客户数
            getUserCustomerAbstract_OUT.ProtectTotal = GetPrivateSaveCustomerQuery(userId).MergeTable().Select(p => p.CustomerId).Distinct().Count();


            //签约客户数变化：昨日新签约的客户数 - 昨日合同到期的客户
            getUserCustomerAbstract_OUT.SignChange = yesterdaySignDataCount - yesterdayOutSignData.Count;
            //昨日签约客户数
            getUserCustomerAbstract_OUT.SignYesterday = yesterdaySignDataCount;
            //今日签约客户数
            getUserCustomerAbstract_OUT.SignToday = todaySignDataCount;




            ////保留客户数:总客户数-签约客户数  
            //getUserCustomerAbstract_OUT.ProtectTotal = getUserCustomerAbstract_OUT.PrivateCustomerTotal - getUserCustomerAbstract_OUT.SignTotal;

            //保留变化数: 总数变化（昨日新增-昨日释放） + 昨日合同到期且未释放
            getUserCustomerAbstract_OUT.ProtectChange = yesterdayAddData.Count - yesterdayReleaseData.Count + yesterdayOutSignNotReleaseData.Count;
            //今日新增保护数
            getUserCustomerAbstract_OUT.ProtectAddToday = todayAddData.Count;
            //昨日新增保护数
            getUserCustomerAbstract_OUT.ProtectAddYesterday = yesterdayAddData.Count;
            //今日新增跟踪数
            getUserCustomerAbstract_OUT.TrackingToday = todayAddData.Count;
            //昨日新增跟踪数
            getUserCustomerAbstract_OUT.TrackingYesterday = trackingYesterdayCount;
            return getUserCustomerAbstract_OUT;

        }
        /// <summary>
        /// 客户数据-客户统计
        /// </summary>
        /// <param name="salesDataStatistics"></param>
        /// <returns></returns>
        public List<CustomerStatisticsItem_OUT> CustomerStatistics(SalesDataStatisticsParams salesDataStatistics)
        {
            List<CustomerStatisticsItem_OUT> customerStatisticsItem_OUTs = new List<CustomerStatisticsItem_OUT>();
            var queryableLeft = GetStatisticsDateSpanQuery(salesDataStatistics);
            var newCustomer = Db.Queryable<Db_crm_customer_org_log>()
                .LeftJoin<Db_sys_user>((privatePool, user) => privatePool.UserId == user.Id && user.Deleted == false)
                .LeftJoin<Db_crm_customer>((privatePool, user, customer) => privatePool.CustomerId == customer.Id && customer.Deleted == (int)EnumCustomerDel.NotDel)
                .LeftJoin<Db_crm_customer_subcompany>((privatePool, user, customer, company) => customer.Id == company.CustomerId && company.Deleted == (int)EnumCustomerDel.NotDel && company.IsMain == (int)EnumCustomerCompanyMain.Main)
                .LeftJoin<Db_sys_organization>((privatePool, user, customer, company, org) => user.OrganizationId == org.Id && org.Deleted == false)
                .Where((privatePool, user, customer, company, org) => privatePool.Deleted == (int)EnumCustomerDel.NotDel)
                .Where((privatePool, user, customer, company, org) => !(string.IsNullOrEmpty(privatePool.OrgBrigadeId) && string.IsNullOrEmpty(privatePool.OrgDivisionId) && string.IsNullOrEmpty(privatePool.OrgRegimentId)))
                //.Where((privatePool, user, customer, company, org) => !SqlFunc.IsNullOrEmpty(customer.CustomerLevel))
                .WhereIF(!ArrayUtil.IsNullOrEmpty(salesDataStatistics.CityIds), (privatePool, user, customer, company, org) => SqlFunc.ContainsArray(salesDataStatistics.CityIds, company.City))
                .WhereIF(!ArrayUtil.IsNullOrEmpty(salesDataStatistics.CustomerDataSources), (privatePool, user, customer, company, org) => SqlFunc.ContainsArray(salesDataStatistics.CustomerDataSources, customer.CustomerSource))
                .WhereIF(!StringUtil.IsNullOrEmpty(salesDataStatistics.UserId), (privatePool, user, customer, company, org) => salesDataStatistics.UserId == privatePool.UserId)
                //.WhereIF(!ArrayUtil.IsNullOrEmpty(customerStatisticsParams.OrgIds), (privatePool, user, customer, company,org) => SqlFunc.ContainsArray(customerStatisticsParams.OrgIds, org.Id))
                .WhereIF(!ArrayUtil.IsNullOrEmpty(salesDataStatistics.OrgDivisionId), (privatePool, user, customer, company, org) => SqlFunc.ContainsArray(salesDataStatistics.OrgDivisionId, privatePool.OrgDivisionId))
                .WhereIF(!ArrayUtil.IsNullOrEmpty(salesDataStatistics.OrgBrigadeId), (privatePool, user, customer, company, org) => SqlFunc.ContainsArray(salesDataStatistics.OrgBrigadeId, privatePool.OrgBrigadeId))
                .WhereIF(!ArrayUtil.IsNullOrEmpty(salesDataStatistics.OrgRegimentId), (privatePool, user, customer, company, org) => SqlFunc.ContainsArray(salesDataStatistics.OrgRegimentId, privatePool.OrgRegimentId))
                .WhereIF(salesDataStatistics.DateStart != null, (privatePool, user, customer, company, org) => privatePool.CreateDate >= salesDataStatistics.DateStart)
                .WhereIF(salesDataStatistics.DateEnd != null, (privatePool, user, customer, company, org) => privatePool.CreateDate <= salesDataStatistics.DateEnd)
                .Select((privatePool, user, customer, company, org) => new
                {
                    Date = privatePool.CreateDate,
                    Level = customer.CustomerLevel,
                    CustomerId = privatePool.CustomerId
                })
                .MergeTable()
                .RightJoin(queryableLeft, (c, q) => c.Date >= q.StartDate && c.Date <= q.EndDate)
                .GroupBy((c, q) => new { q.Index, q.Key, q.StartDate, c.Level })
                .OrderBy((c, q) => new { q.Index, q.Key, q.StartDate, c.Level })
                .Select((c, q) => new StatisticsTypeItem<string>
                {
                    Index = q.Index,
                    Key = q.Key,
                    Name = q.Key,
                    TypeKey = SqlFunc.ToString(c.Level),
                    Value = SqlFunc.AggregateDistinctCount(c.CustomerId)
                });
            var li = Db.Queryable(newCustomer).ToList();
            customerStatisticsItem_OUTs = Db.Queryable(newCustomer)
                .GroupBy(c => new { c.Index, c.Key, c.Name })
                .Select(c => new CustomerStatisticsItem_OUT
                {
                    Index = c.Index,
                    Name = c.Name,
                    Key = c.Key,
                    Count = SqlFunc.ToInt32(SqlFunc.AggregateSum(c.Value))
                })
                .Mapper(c =>
                {
                    c.Type = new List<CustomerStatisticsTypeItem_OUT>();
                    li.FindAll(d => d.Index == c.Index).ForEach(g =>
                    {
                        c.Type.Add(new CustomerStatisticsTypeItem_OUT
                        {
                            Key = g.TypeKey,
                            Name = StringUtil.IsNotNullOrEmpty(g.TypeKey) ? ((EnumCustomerLevel)(Int32.Parse(g.TypeKey))).GetEnumDescription() : "",
                            Count = g.Value == null ? 0 : g.Value.Value.ToInt()
                        });
                    });
                })
                .ToList();
            return customerStatisticsItem_OUTs;
        }
        #region 作废
        ///// <summary>
        ///// 公司信息排重 (所有私有池 有效 未释放 未删除 验证名称和代码)
        ///// </summary>
        ///// <param name="crmCustomerCompanys"></param>
        ///// <param name="selfExceptCustomerIds">排重时要排除的客户ID数组</param>
        //public void CheckCompanyExist(List<Db_crm_customer_subcompany> crmCustomerCompanys, List<string>? selfExceptCustomerIds = null)
        //{
        //    var nameQuery = Queryable
        //         .RightJoin<Db_crm_customer_subcompany>((privatePool, company) => privatePool.CustomerId == company.CustomerId)
        //         .LeftJoin<Db_crm_customer>((privatePool, company, customer) => company.CustomerId == customer.Id)
        //         .Where((privatePool, company, customer) => SqlFunc.ContainsArray<string>(crmCustomerCompanys.Select(x => x.CompanyName).ToArray(), company.CompanyName))
        //         .WhereIF(!ArrayUtil.IsNullOrEmpty(selfExceptCustomerIds), (privatePool, company, customer) => !SqlFunc.ContainsArray<string>(selfExceptCustomerIds, company.Id))
        //         .Where((privatePool, company, customer) => privatePool.State == (int)EnumCustomerPrivateRelease.Not)
        //         .Where((privatePool, company, customer) => privatePool.Deleted == (int)EnumCustomerDel.Del && company.Deleted == (int)EnumCustomerDel.Del)
        //         .Where((privatePool, company, customer) => customer.IsValid == (int)EnumCustomerValid.VALID)
        //         .Select((privatePool, company) => company.CompanyName)
        //         .Distinct()
        //         .ToList();
        //    if (nameQuery.Count > 0)
        //    {
        //        throw new ApiException("公司名称(" + string.Join(",", nameQuery) + ")已存在");
        //    }
        //    var codeQuery = Queryable
        //         .RightJoin<Db_crm_customer_subcompany>((privatePool, company) => privatePool.CustomerId == company.CustomerId)
        //         .LeftJoin<Db_crm_customer>((privatePool, company, customer) => company.CustomerId == customer.Id)
        //         .Where((privatePool, company, customer) => SqlFunc.ContainsArray<string>(crmCustomerCompanys.Select(x => x.CreditCode).ToArray(), company.CreditCode))
        //         .WhereIF(!ArrayUtil.IsNullOrEmpty(selfExceptCustomerIds), (privatePool, company, customer) => !SqlFunc.ContainsArray<string>(selfExceptCustomerIds, company.Id))
        //         .Where((privatePool, company, customer) => privatePool.State == (int)EnumCustomerPrivateRelease.Not)
        //         .Where((privatePool, company, customer) => privatePool.Deleted == (int)EnumCustomerDel.Del && company.Deleted == (int)EnumCustomerDel.Del)
        //         .Where((privatePool, company, customer) => customer.IsValid == (int)EnumCustomerValid.VALID)
        //         .Select((privatePool, company) => company.CreditCode)
        //         .Distinct()
        //         .ToList();
        //    if (codeQuery.Count > 0)
        //    {
        //        throw new ApiException("信用代码(" + string.Join(",", codeQuery) + ")已存在");
        //    }

        //}
        #endregion
        /// <summary>
        /// 客户名称隐私处理
        /// </summary>
        /// <param name="customerName"></param>
        /// <returns></returns>
        public string CustomerNameHidden(string customerName)
        {
            string r = customerName.Clone().ToString();
            if (StringUtil.IsNullOrEmpty(customerName))
            {
                return "";
            }
            else if (Regex.Matches(customerName, "[a-zA-Z]").Count > 0)
            {
                return Regex.Replace(r, @"\S", "*");
            }
            else
            {
                List<string> regexStrs = new List<string>() { "公司", "有限公司" };
                regexStrs = regexStrs.Concat(LocalCache.LC_Address.CountryAndAreaCache.Select(c => c.Name).ToList()).ToList();
                regexStrs = regexStrs.Concat(LocalCache.LC_Address.ProvinceCache.Select(c => c.Name).ToList()).ToList();
                regexStrs = regexStrs.Concat(LocalCache.LC_Address.CityCache.Select(c => c.Name).ToList()).ToList();
                List<int> saveIndexs = new List<int>();
                foreach (var regexStr in regexStrs)
                {
                    var index = customerName.IndexOf(regexStr);
                    if (index >= 0)
                    {
                        for (var i = 0; i < regexStr.Length; i++)
                        {
                            var newIndex = index + i;
                            if (!saveIndexs.Contains(newIndex))
                            {
                                saveIndexs.Add(newIndex);
                            }
                        }
                    }
                }
                for (var j = 0; j < customerName.Length; j++)
                {
                    if (!saveIndexs.Contains(j))
                    {
                        r = r.ReplaceByIndex(j, 1, "*");
                    }
                }
            }
            return r;
        }
        /// <summary>
        /// 获取客户/公司下所有gtis服务最新的gtis appl id
        /// </summary>
        /// <returns></returns>
        public List<string> GetCustomerGtisNewlyAppls(string customerId, string companyId, string userId)
        {
            var gtisNewlyAppls = Queryable
            .LeftJoin<Db_crm_customer_subcompany>((p, s) => p.CustomerId == s.CustomerId && s.Deleted == (int)EnumCustomerDel.NotDel)
            .LeftJoin<Db_crm_contract>((p, s, c) => c.FirstParty == s.Id && c.Deleted == false)
            .LeftJoin<Db_crm_contract_serviceinfo_gtis>((p, s, c, g) => g.ContractId == c.Id && g.Deleted == false)
            .LeftJoin<Db_crm_contract_productserviceinfo_gtis_appl>((p, s, c, g, a) => a.Id == g.ProductServiceInfoGtisApplId && a.Deleted == false)
            .Where((p, s, c, g, a) => p.Deleted == (int)EnumCustomerDel.NotDel)
            .Where((p, s, c, g, a) => g.IsApplHistory == false)
            .Where((p, s, c, g, a) => SqlFunc.ContainsArray(new int[] { (int)EnumContractServiceState.VALID, (int)EnumContractServiceState.OUT }, g.State))
            .Where((p, s, c, g, a) => g.IsChanged == (int)EnumContractServiceInfoIsChange.Not)
            .Where((p, s, c, g, a) => s.IsValid == (int)EnumCompanyValid.VALID)
            .Where((p, s, c, g, a) => p.State == (int)EnumCustomerPrivateRelease.Not)
            .Where((p, s, c, g, a) => p.CustomerId == customerId)
            .WhereIF(!string.IsNullOrEmpty(companyId), (p, s, c, g, a) => s.Id == companyId)
            .Where((p, s, c, g, a) => p.UserId == userId)
            .Select((p, s, c, g, a) => SqlFunc.ToString(a.Id))
            .ToList();
            return gtisNewlyAppls;
        }


        /// <summary>
        /// 根据查询条件获取用户私有池客户信息列表
        /// </summary>
        /// <param name="query_In"></param>
        public List<QueryCustomer_OUT> SearchArbitrateCompany(SearchArbitrateCompany_In query_In)
        {
            int pD = DbOpe_crm_customer_privatepool.Instance.GetProtectWarningDays();
            int sD = DbOpe_crm_customer_privatepool.Instance.GetServiceWarningDays();
            var maxDelayTimes = GetMaxDelayTimes();
            var dt = DateTime.Now;

            var dtDay = DateTime.Parse(dt.ToString("yyyy-MM-dd"));
            var dtDayEnd = DateTime.Parse(dt.ToString("yyyy-MM-dd 23:59:59"));
            var weekEnd = dt.AddDays(1 - Convert.ToInt32(dt.DayOfWeek.ToString("d"))).AddDays(6).ToString("yyyy-MM-dd");
            var weekStart = dt.AddDays(1 - Convert.ToInt32(dt.DayOfWeek.ToString("d"))).ToString("yyyy-MM-dd");

            bool noProductsStateQuery = true;

            List<QueryCustomer_OUT> r = new List<QueryCustomer_OUT>();
            r = Queryable
                .LeftJoin<Db_crm_customer_subcompany>((privatePool, company) => privatePool.CustomerId == company.CustomerId && company.IsMain == (int)EnumCustomerCompanyMain.Main)
                .LeftJoin<Db_crm_customer>((privatePool, company, customer) => company.CustomerId == customer.Id)
                .LeftJoinIF<Db_v_customerproductserviceinfostatus>(!noProductsStateQuery, (privatePool, company, customer, productstate) => company.CustomerId == productstate.CustomerId)
                .LeftJoin<Db_v_customer_service_nearlyend>((privatePool, company, customer, productstate, serviceEnd) => company.CustomerId == serviceEnd.CustomerId)
                .WhereIF(!string.IsNullOrEmpty(query_In.CustomerName), (privatePool, company, customer, productstate, serviceEnd) => company.CompanyName.Contains(query_In.CustomerName))
                .Where((privatePool, company, customer, productstate, serviceEnd) => privatePool.State == (int)EnumCustomerPrivateRelease.Not)
                .Where((privatePool, company, customer, productstate, serviceEnd) =>
                    privatePool.Deleted == (int)EnumCustomerDel.NotDel
                    && company.Deleted == (int)EnumCustomerDel.NotDel
                    && customer.Deleted == (int)EnumCustomerDel.NotDel
                )
                .Where((privatePool, company, customer, productstate, serviceEnd) => customer.IsValid == (int)EnumCustomerValid.VALID)
                .Where((privatePool, company, customer, productstate, serviceEnd) => customer.IsMerge == (int)EnumCustomerMerge.NotMerge)
                .Where((privatePool, company, customer, productstate, serviceEnd) => query_In.UserIds.Contains(privatePool.UserId))
                .Select((privatePool, company, customer, productstate, serviceEnd) => new QueryCustomer()
                {
                    Id = customer.Id,
                    CreditCode = company.CreditCode,
                    CustomerName = company.CompanyName,
                    Contacts = company.Contacts,
                    ContactWay = company.ContactWay,
                    CustomerNum = customer.CustomerNum,
                    //ServiceState = productstate?.ProductServiceInfoStatus,
                    ProtectionDeadline = privatePool.ProtectionDeadline == null ? "" : privatePool.ProtectionDeadline.Value.ToString("yyyy-MM-dd 00:00"),
                    //CollectionTime = privatePool.CollectionTime == null ? "" : privatePool.CollectionTime.Value.ToString("yyyy-MM-dd HH:mm"),
                    CollectionTime = privatePool.CollectionTime == null ? customer.CreateDate.Value.ToString("yyyy-MM-dd HH:mm") : privatePool.CollectionTime.Value.ToString("yyyy-MM-dd HH:mm"),
                    CreateDate = customer.CreateDate == null ? "" : customer.CreateDate.Value.ToString("yyyy-MM-dd HH:mm"),
                    ProductName = "产品信息",
                    UserId = privatePool.UserId,
                    IsSupplementary = (EnumNeedSupplement)customer.IsSupplementary,
                    ProtectionDeadlineDt = privatePool.ProtectionDeadline == null ? DateTime.MaxValue : privatePool.ProtectionDeadline.Value,
                    ServiceEndDt = serviceEnd.ServiceCycleEnd == null ? DateTime.MaxValue : serviceEnd.ServiceCycleEnd.Value,
                    ServiceEnd = serviceEnd.ServiceCycleEnd == null ? "" : serviceEnd.ServiceCycleEnd.Value.ToString("yyyy-MM-dd 00:00"),
                    MainCompanyId = company.Id,
                    CreditType = company.CreditType,
                    Country = company.Country
                },
                true)
                .MergeTable()
                .Select(mit => new QueryCustomer_OUT
                {
                    Id = mit.Id.SelectAll(),
                    NearlyRealese = SqlFunc.DateDiff(DateType.Day, dtDay, mit.ProtectionDeadlineDt) <= pD,
                    NearlyServiceEnd = SqlFunc.DateDiff(DateType.Day, dtDay, mit.ServiceEndDt) <= sD,
                    //ProtectLeftDays = SqlFunc.DateDiff(DateType.Day, dtDay, mit.ProtectionDeadlineDt),
                    //ServiceLeftDays = mit.ServiceEndDt == DateTime.MaxValue ? null : SqlFunc.DateDiff(DateType.Day, dtDay, mit.ServiceEndDt),
                    DelayTimesLeft = SqlFunc.IIF(maxDelayTimes - mit.DelayTimes > 0, maxDelayTimes - mit.DelayTimes, 0),
                    ProtectLeftDays = SqlFunc.IIF(SqlFunc.DateDiff(DateType.Day, dtDay, mit.ProtectionDeadlineDt) > 0, SqlFunc.DateDiff(DateType.Day, dtDay, mit.ProtectionDeadlineDt), 0),
                    ServiceLeftDays = SqlFunc.IIF(
                    mit.ServiceEndDt != DateTime.MaxValue && (SqlFunc.DateDiff(DateType.Day, dtDay, mit.ServiceEndDt) > 0),
                    SqlFunc.DateDiff(DateType.Day, dtDay, mit.ServiceEndDt),
                    0)
                })
                .MergeTable()
                //相同时间mysql排序会有问题，需要加一列id
                .OrderBy(it => it.Id)
                .Mapper(it =>
                {
                    var userInfo = DbOpe_sys_user.Instance.GetUserById(it.UserId);
                    it.UserName = userInfo.Name;
                    it.OrgName = userInfo.OrganizationName;
                    it.TrackingStageName = it.TrackingStage.GetEnumDescription();
                    it.CustomerSourceName = it.CustomerSource.GetEnumDescription();
                    it.PeerDataName = it.PeerData.GetEnumDescription();
                    it.CustomerLevelName = it.CustomerLevel.GetEnumDescription();
                    it.CustomerNatureName = it.CustomerNature.GetEnumDescription();
                    it.CustomerSizeName = it.CustomerSize.GetEnumDescription();
                    it.ServiceStateName = ((EnumCustomerProductServiceState)(it.ServiceState == null ? 1 : it.ServiceState)).GetEnumDescription();
                    it.AvatarImage = userInfo.AvatarImage;
                    it.CountryName = LocalCache.LC_Address.CountryCache.FirstOrDefault(c => c.Id == it.Country)?.Name ?? "";
                    //if (StringUtil.IsNotNullOrEmpty(it.ProtectionDeadline))
                    //{
                    //    var ProtectionDeadlineDate = DateTime.Parse(it.ProtectionDeadline);
                    //    it.NearlyRealese = ProtectionDeadlineDate.Subtract(dtDay).Days + 1 < 15;
                    //}
                    //else
                    //{
                    //    it.NearlyRealese = false;
                    //}

                })
                .ToList();
            //.ToPageList(query_In.PageNumber, query_In.PageSize, ref total);

            Db.ThenMapper(r, item =>
            {
                item.SubCompanys = Db.Queryable<Db_crm_customer_subcompany>()
                .Where(it => it.Deleted == (int)EnumCustomerDel.NotDel && it.IsMain == (int)EnumCustomerCompanyMain.Sub)
                .Select(it => new CompanySimple_OUT() { Id = it.Id, CompanyName = it.CompanyName, CustomerName = it.CompanyName, CreditCode = it.CreditCode, CustomerId = it.CustomerId, Contacts = it.Contacts })
                .SetContext(x => x.CustomerId, () => item.Id, item).ToList();
                item.CustomerIndustry = Db.Queryable<Db_crm_customer_subcompany_mainbusiness>()
                .LeftJoin<Db_sys_customerindustry>((d, dic) => d.CustomerIndustry == dic.Id)
                .Select((d, dic) => new { SubCompanyId = d.SubCompanyId, CustomerIndustryName = dic.Name })
                .SetContext(d => d.SubCompanyId, () => item.MainCompanyId, item)
                .ToList()
                .Select(it => it.CustomerIndustryName)
                .Distinct()
                .JoinToString(",");
                item.RelatedCompanies = Db.Queryable<Db_crm_customer_subcompany_related>()
                .Where(it => it.Deleted == false && it.IsValid == (int)EnumCompanyValid.VALID)
                .Select(it => new SubCompanyRelated_OUT() { Id = it.Id, CompanyName = it.CompanyName, CreditType = it.CreditType, CreditCode = it.CreditCode, CompanyId = it.CompanyId, InValidTime = it.InValidTime, IsValid = it.IsValid, CreateDate = it.CreateDate })
                .SetContext(x => x.CompanyId, () => item.MainCompanyId, item).ToList();
                item.HasContractProduct = Db.Queryable<Db_crm_customer_subcompany>()
                .InnerJoin<Db_crm_contract>((s, c) => c.FirstParty == s.Id)
                .Where((s, c) => s.Deleted == (int)EnumCustomerDel.NotDel && s.IsValid == (int)EnumCompanyValid.VALID && c.Deleted == false)
                .Select((s, c) => new { Id = c.Id, CustomerId = s.CustomerId })
                .SetContext(s => s.CustomerId, () => item.Id, item)
                .Any();
            });
            List<string> subCompanyIds = r.Select(c => c.MainCompanyId).ToList();
            foreach (var item in r)
            {
                subCompanyIds = subCompanyIds.Concat(item.SubCompanys.Select(it => it.Id)).Distinct().ToList();
            }
            var tds = Db.Queryable<Db_crm_trackingrecord>()
                    .LeftJoin<Db_crm_customer_subcompany>((t, s) => t.CustomerSubCompanyId == s.Id)
                    .Where((t, s) => SqlFunc.ContainsArray(subCompanyIds, t.CustomerSubCompanyId))
                    .Where((t, s) => t.Deleted == false && s.Deleted == (int)EnumCustomerDel.NotDel)
                    .Where((t, s) =>
                    t.CustomerDataSource == EnumCustomerDataSource.Private
                    || (t.CustomerDataSource == EnumCustomerDataSource.Temporary && t.IsVisible == 1)
                    || (t.CustomerDataSource == EnumCustomerDataSource.Temporary && query_In.UserIds.Contains(t.UserId))
                    )
                    .Select((t, s) => new TrackingRecordSimple_OUT
                    {
                        UpdateDate = t.UpdateDate == null ? t.CreateDate : t.UpdateDate,
                        CustomerId = s.CustomerId,
                        Remark = t.Remark
                    }).MergeTable()
                    .Select(it => new TrackingRecordSimple_OUT
                    {
                        index = SqlFunc.RowNumber($"{it.UpdateDate} desc", it.CustomerId),
                        CustomerId = it.CustomerId,
                        Remark = it.Remark
                    }).MergeTable().Where(it => it.index == 1).ToList();
            foreach (var item in r)
            {
                var t = tds.Find(t => t.CustomerId == item.Id);
                if (t != null)
                {
                    item.TrackingDetail = t.Remark;
                }
            }
            return r;
        }
    }
}
