using CRM2_API.BLL.GtisOpe;
using CRM2_API.Common;
using CRM2_API.Common.Utils;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BLLModel.ServiceOpening;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.BusinessModel.BM_GtisOpe;
using CRM2_API.Model.Enum;
using CRM2_API.Model.System;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CRM2_API.BLL.ServiceOpening
{
    /// <summary>
    /// 服务开通 - 辅助方法
    /// </summary>
    public partial class BLL_ServiceOpening
    {
        #region GTIS相关辅助方法
        
        /// <summary>
        /// 从开通参数中获取有GTIS权限的Wits用户列表（保留完整权限信息用于权限对比）
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <returns>Wits用户列表</returns>
        private List<Db_crm_contract_serviceinfo_wits_user> GetWitsUsersWithGtisPermission(ServiceOpeningParams openingParams)
        {
            // 直接返回有GTIS权限的wits_user，保留完整的权限信息
            var usersWithGtisPermission = openingParams.Users
                .Where(x => x.GtisPermission == true)
                .ToList();

            LogUtil.AddLog($"获取有GTIS权限的Wits用户，数量: {usersWithGtisPermission.Count}");

            return usersWithGtisPermission;
        }


        
        /// <summary>
        /// 获取主GTIS账号
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <returns>主账号邮箱</returns>
        private string GetMainGtisAccount(ServiceOpeningParams openingParams)
        {
            var mainUser = openingParams.Users
                .FirstOrDefault(x => x.GtisPermission == true && 
                               x.AccountType == (int)EnumGtisAccountType.Main);
            
            return mainUser?.AccountNumber ?? "";
        }
        
        /// <summary>
        /// 分类用户用于续约或变更
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <param name="currentWitsUsers">当前Wits用户列表（包含完整权限信息）</param>
        /// <returns>用户分类结果</returns>
        private UserClassificationResult ClassifyUsersForRenewalOrChange(ServiceOpeningParams openingParams,
            List<Db_crm_contract_serviceinfo_wits_user> currentWitsUsers)
        {
            var result = new UserClassificationResult
            {
                AddUsers = new List<Db_crm_contract_serviceinfo_wits_user>(),
                UpdateUsers = new List<Db_crm_contract_serviceinfo_gtis_user>(),
                DelUsers = new List<Db_crm_contract_serviceinfo_gtis_user>()
            };

            try
            {
                // 获取历史用户数据 - 通过GTIS系统接口查询真实状态
                var historyUsers = GetHistoryGtisUsersFromSystem(openingParams);

                // 直接使用Wits用户（wits_user是gtis_user的扩展，无需转换）
                var currentGtisUsers = currentWitsUsers.MappingTo<List<Db_crm_contract_serviceinfo_wits_user>>();

                LogUtil.AddLog($"GTIS用户分类开始，当前用户数量: {currentGtisUsers.Count}，历史用户数量: {historyUsers.Count}");


                // 新增：权限对比逻辑 - 基于Wits用户进行权限对比
                result.UserPermissionChanges = CompareUserPermissions(currentWitsUsers);

                LogUtil.AddLog($"权限变更检测完成，发现{result.UserPermissionChanges.Count}个权限变更");

                // 参考现有ExcuteGtisProcess逻辑进行用户分类（第3084-3110行和第3201-3228行）
                foreach (var historyUser in historyUsers)
                {
                    var findIndex = currentGtisUsers.FindIndex(u => u.SysUserId == historyUser.UserId && !string.IsNullOrEmpty(u.SysUserId));
                    if (findIndex >= 0)
                    {
                        // 沿用的账号 - 复制历史信息，更新业务参数（完全按照ExcuteGtisProcess逻辑）
                        var copyUser = historyUser.MappingTo<Db_crm_contract_serviceinfo_gtis_user>();
                        copyUser.Id = currentGtisUsers[findIndex].Id;
                        copyUser.SharePeopleNum = currentGtisUsers[findIndex].SharePeopleNum;
                        // 注意：wits_user没有ContractServiceInfoGtisId和AllCountrySubUser字段，这些在接口调用时设置

                        if (copyUser.AccountType == (int)EnumGtisAccountType.Main)
                        {
                            copyUser.AuthorizationNum = currentGtisUsers[findIndex].AuthorizationNum;
                        }

                        result.UpdateUsers.Add(copyUser);
                        currentGtisUsers.RemoveAt(findIndex);
                    }
                    else
                    {
                        // 停用的账号
                        result.DelUsers.Add(historyUser);
                    }
                }

                // 剩余的是新增账号
                result.AddUsers = currentGtisUsers;

                LogUtil.AddLog($"GTIS用户分类完成，新增: {result.AddUsers.Count}，更新: {result.UpdateUsers.Count}，删除: {result.DelUsers.Count}");




            }
            catch (Exception ex)
            {
                var errorMsg = $"GTIS用户分类异常，当前用户数量: {currentWitsUsers?.Count ?? 0}, 错误: {ex.Message}";
                LogUtil.AddErrorLog($"{errorMsg}，详细异常: {ex}");
                throw new CRM2_API.Model.System.ApiException(errorMsg);
            }

            return result;
        }



        /// <summary>
        /// 通过GTIS系统接口获取历史用户数据
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <returns>历史用户列表</returns>
        private List<Db_crm_contract_serviceinfo_gtis_user> GetHistoryGtisUsersFromSystem(ServiceOpeningParams openingParams)
        {
            var historyUsers = new List<Db_crm_contract_serviceinfo_gtis_user>();

            try
            {
                // 根据开通类型确定查询的客户编码
                string queryCustomerCode = GetQueryCustomerCode(openingParams);

                if (string.IsNullOrEmpty(queryCustomerCode))
                {
                    LogUtil.AddLog("未找到有效的客户编码，无法查询历史GTIS用户");
                    return historyUsers;
                }

                LogUtil.AddLog($"开始查询GTIS系统历史用户，客户编码: {queryCustomerCode}");

                // 调用GTIS系统接口查询该客户编码下的现有账号状态
                var gtisOpe = new BLL_GtisOpe();
                var gtisUserInfoList = gtisOpe.GetUserInfo(queryCustomerCode).Result;

                if (gtisUserInfoList != null && gtisUserInfoList.Count > 0)
                {
                    LogUtil.AddLog($"从GTIS系统查询到 {gtisUserInfoList.Count} 个历史用户");

                    // 将GTIS系统返回的用户信息转换为CRM用户格式
                    foreach (var gtisUserInfo in gtisUserInfoList)
                    {
                        var historyUser = ConvertGtisUserInfoToCrmUser(gtisUserInfo);
                        if (historyUser != null)
                        {
                            historyUsers.Add(historyUser);
                        }
                    }
                }
                else
                {
                    LogUtil.AddLog($"GTIS系统中未找到客户编码 {queryCustomerCode} 的历史用户数据");
                }

            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"查询GTIS系统历史用户异常: {ex.Message}");
                // 异常情况下返回空列表，让调用方按新增用户处理
            }

            return historyUsers;
        }

        /// <summary>
        /// 根据开通类型获取查询客户编码
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <returns>客户编码</returns>
        private string GetQueryCustomerCode(ServiceOpeningParams openingParams)
        {
            var openingType = DetermineOpeningType(openingParams);

            if (openingType == ServiceOpeningType.Change)
            {
                // 变更场景：使用当前合同的客户编码
                return openingParams.Contract?.ContractNum ?? "";
            }
            else if (openingType == ServiceOpeningType.Renewal)
            {
                // 续约场景：使用被续约原合同的客户编码
                var renewContractNum = GetOldContractNum(openingParams);
                return renewContractNum;
            }

            return "";
        }



        /// <summary>
        /// 将GTIS系统用户信息转换为CRM用户格式
        /// </summary>
        /// <param name="gtisUserInfo">GTIS用户信息</param>
        /// <returns>CRM用户格式</returns>
        private Db_crm_contract_serviceinfo_gtis_user ConvertGtisUserInfoToCrmUser(BM_GtisOpeUserInfo gtisUserInfo)
        {
            if (gtisUserInfo == null || string.IsNullOrEmpty(gtisUserInfo.SysUserID))
            {
                return null;
            }

            return new Db_crm_contract_serviceinfo_gtis_user
            {
                Id = Guid.NewGuid().ToString(),
                UserId = gtisUserInfo.SysUserID,
                AccountNumber = gtisUserInfo.AccountNumber, // 修正属性名
                PassWord = gtisUserInfo.PassWord, // 修正属性名
                AccountType = (int)EnumGtisAccountType.Sub, // 从GTIS查询接口无法准确判断账号类型，使用默认值
                SharePeopleNum = 0, // 从GTIS系统无法获取，使用默认值
                AllCountrySubUser = false, // 从GTIS系统无法获取，使用默认值
                AuthorizationNum = 0, // 从GTIS系统无法获取，使用默认值
                OpeningStatus = (int)EnumGtisUserOpeningStatus.Ok, // 假设查询到的都是正常状态
                StartDate = gtisUserInfo.StartServerDate, // 使用GTIS系统的服务开始时间
                EndDate = gtisUserInfo.EndServerDate, // 使用GTIS系统的服务结束时间
                Deleted = false
            };
        }



        /// <summary>
        /// 获取旧合同号
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <returns>旧合同号</returns>
        private string GetOldContractNum(ServiceOpeningParams openingParams)
        {
            // 直接从wits表的OldContractNum字段获取被续约的客户编码
            return openingParams.MainService?.OldContractNum ?? "";
        }
        
        /// <summary>
        /// 构建GTIS用户开通结果（不回写数据库）
        /// </summary>
        /// <param name="witsUsers">Wits用户列表（wits_user是gtis_user的扩展，可以直接使用）</param>
        /// <param name="retModels">返回的用户模型</param>
        /// <returns>用户开通结果列表</returns>
        private List<GtisUserResult> BuildGtisUserResults(List<Db_crm_contract_serviceinfo_wits_user> witsUsers,
            List<CRM2_API.Model.BusinessModel.BM_GtisOpe.BM_AddGtisUserRetModel> retModels)
        {
            var userResults = new List<GtisUserResult>();
            
            foreach (var retUser in retModels)
            {
                var user = witsUsers.Find(u => u.Id == retUser.CrmId);
                if (user != null)
                {
                    var userResult = new GtisUserResult
                    {
                        CrmId = user.Id,
                        OriginalUserId = user.SysUserId,
                        AccountNumber = retUser.Uid,
                        GtisUserId = retUser.SysUserID,
                        Password = retUser.Pwd,
                        AccountType = user.AccountType,
                        SharePeopleNum = user.SharePeopleNum,
                        Success = true,
                        Message = "开通成功"
                    };
                    
                    userResults.Add(userResult);
                }
            }
            
            return userResults;
        }
        
        /// <summary>
        /// 构建GTIS续约/变更用户结果（不回写数据库）
        /// </summary>
        /// <param name="userClassification">用户分类结果</param>
        /// <param name="retModels">返回的用户模型</param>
        /// <param name="gtisResult">GTIS结果对象，用于填充UserResults</param>
        private void BuildGtisRenewalResults(UserClassificationResult userClassification,
            List<CRM2_API.Model.BusinessModel.BM_GtisOpe.BM_AddGtisUserRetModel> retModels,
            GtisResult gtisResult)
        {
            // 确保 UserResults 已初始化
            if (gtisResult.UserResults == null)
            {
                gtisResult.UserResults = new List<GtisUserResult>();
            }

            // 只处理新增用户结果（需要后续写入密码）
            foreach (var user in userClassification.AddUsers)
            {
                var addUserRet = retModels.Find(u => u.CrmId == user.Id);
                if (addUserRet != null)
                {
                    var userResult = new GtisUserResult
                    {
                        CrmId = user.Id,
                        OriginalUserId = user.SysUserId,
                        AccountNumber = addUserRet.Uid,
                        GtisUserId = addUserRet.SysUserID,
                        Password = addUserRet.Pwd,
                        AccountType = user.AccountType,
                        SharePeopleNum = user.SharePeopleNum,
                        Success = true,
                        Message = "新增用户成功"
                    };

                    gtisResult.UserResults.Add(userResult);
                }
            }

            // 注意：沿用用户和停用用户不需要写入密码，所以不添加到 UserResults 中
        }
        
        #endregion
        
        #region 环球搜相关辅助方法
        
        /// <summary>
        /// 获取环球搜子账号数量
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <returns>子账号数量</returns>
        private int GetGlobalSearchSubAccountCount(ServiceOpeningParams openingParams)
        {
            return (openingParams.GlobalSearchBasicConfig?.MaxAccountsNum - 1) ?? 0;
        }
        

        
        #endregion
        
        #region SaleWits相关辅助方法
        
        /// <summary>
        /// 从GTIS获取租户ID
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <returns>租户ID</returns>
        /// <exception cref="CRM2_API.Model.System.ApiException">获取租户ID失败时抛出</exception>
        private async Task<string> GetTenantIdFromGtis(ServiceOpeningParams openingParams)
        {
            try
            {
                // 验证必要参数
                if (string.IsNullOrEmpty(openingParams.Contract?.ContractNum))
                {
                    var errorMsg = "合同编号为空，无法获取GTIS租户ID";
                    LogUtil.AddErrorLog(errorMsg);
                    throw new CRM2_API.Model.System.ApiException(errorMsg);
                }

                // // 构建GTIS租户查询参数，只传客户编码
                // var queryParam = new BM_GetAccountTenantId
                // {
                //     SvCode = openingParams.Contract.ContractNum, // 合同编号作为查询参数，不使用合同ID，因为合同ID可能是续约合同的ID，而不是开通合同的ID
                // };
                var queryParam = openingParams.Contract.ContractNum;

                LogUtil.AddLog($"调用GTIS接口获取租户ID，客户编码: {queryParam}");

                // 调用GTIS接口获取租户ID
                var result = await _gtisBll.GetAccountTenantId(queryParam);

                if (result != null && !string.IsNullOrEmpty(result.UnifiedTenantId))
                {
                    LogUtil.AddLog($"成功获取GTIS租户ID: {result.UnifiedTenantId}");
                    return result.UnifiedTenantId;
                }
                else
                {
                    var errorMsg = $"GTIS接口返回空的租户ID，客户编码: {queryParam}，返回结果: {result?.SerializeNewtonJson()}";
                    LogUtil.AddErrorLog(errorMsg);
                    throw new CRM2_API.Model.System.ApiException(errorMsg);
                }
            }
            catch (Exception ex)
            {
                var errorMsg = $"调用GTIS接口获取租户ID异常，客户编码: {openingParams.Contract?.ContractNum}，异常信息: {ex.Message}";
                LogUtil.AddErrorLog($"{errorMsg}，详细异常: {ex}");
                throw new CRM2_API.Model.System.ApiException(errorMsg);
            }
        }

        /// <summary>
        /// 计算年度下发的SaleWits资源参数（专门用于定时下发）
        /// </summary>
        /// <param name="service">SaleWits服务信息</param>
        /// <returns>年度下发资源参数</returns>
        private SaleWitsParams GetAnnualDistributionSaleWitsParams(Db_crm_contract_serviceinfo_saleswits service)
        {
            try
            {
                LogUtil.AddLog($"开始计算年度下发资源，服务ID: {service.Id}");

                // 获取账号数量
                var salesWitsAccountCount = service.AccountsNum ?? 0;

                if (salesWitsAccountCount <= 0)
                {
                    var errorMsg = $"SaleWits账号数量配置无效，账号数量: {salesWitsAccountCount}，服务ID: {service.Id}";
                    LogUtil.AddErrorLog(errorMsg);
                    throw new CRM2_API.Model.System.ApiException(errorMsg);
                }

                // 年度下发固定为12个月
                var serviceMonths = 12;

                var defaultEmailCount = DbOpe_sys_systemparameter.Instance.GetValueByKey("EmailCount");
                var defaultTokenCount = DbOpe_sys_systemparameter.Instance.GetValueByKey("TokenCount");
                if(string.IsNullOrEmpty(defaultEmailCount) || string.IsNullOrEmpty(defaultTokenCount)
                || !int.TryParse(defaultEmailCount, out var defaultEmailCountInt)
                || !int.TryParse(defaultTokenCount, out var defaultTokenCountInt))
                {
                    throw new CRM2_API.Model.System.ApiException("系统参数配置异常");
                }
                
                // 按业务规则计算年度资源：每账号每月300封邮件+10万token
                var totalEmailCount = salesWitsAccountCount * defaultEmailCountInt * serviceMonths;
                var totalTokenCount = salesWitsAccountCount * defaultTokenCountInt * serviceMonths;

                LogUtil.AddLog($"年度下发资源计算成功: 账号数={salesWitsAccountCount}, 服务月数={serviceMonths}, 邮件数={totalEmailCount}, Token数={totalTokenCount}万个");

                return new SaleWitsParams
                {
                    Amount = 0, // 赠送资源，金额为0
                    EmailCount = totalEmailCount,
                    TokenCount = totalTokenCount / 10000 // 转换为万个单位
                };
            }
            catch (CRM2_API.Model.System.ApiException)
            {
                // 重新抛出业务异常，保持原有的错误信息和上下文
                throw;
            }
            catch (Exception ex)
            {
                var errorMsg = $"计算年度下发SaleWits参数异常，服务ID: {service.Id}，异常信息: {ex.Message}";
                LogUtil.AddErrorLog($"{errorMsg}，详细异常: {ex}");
                throw new CRM2_API.Model.System.ApiException(errorMsg);
            }
        }

        /// <summary>
        /// 计算服务周期月数（按自然月计费）
        /// 采用行业标准的自然月计费方式，确保计费的公平性和用户体验
        /// </summary>
        /// <param name="startDate">服务开始日期</param>
        /// <param name="endDate">服务结束日期</param>
        /// <returns>服务月数</returns>
        /// <exception cref="CRM2_API.Model.System.ApiException">当日期参数无效或计算结果异常时抛出</exception>
        /// <remarks>
        /// 计费规则说明：
        /// 1. 采用自然月计费，每个完整的日历月计为1个月
        /// 2. 结束日期在某个计费周期内时，不额外增加月份
        /// 3. 示例：2024-01-15 到 2024-03-14 = 2个月（结束日期在第2个计费周期内）
        /// 4. 示例：2024-01-31 到 2024-02-28 = 1个月（结束日期在第1个计费周期内）
        /// 5. 示例：2025-07-28 到 2026-06-08 = 11个月（结束日期在第11个计费周期内）
        /// 6. 不足1个月的服务期按1个月计算
        /// 7. 如果结束时间早于或等于开始时间，将抛出异常
        /// </remarks>
        public int CalculateServiceMonths(DateTime startDate, DateTime endDate)
        {
            try
            {
                LogUtil.AddLog($"开始计算服务月数，开始时间: {startDate:yyyy-MM-dd}，结束时间: {endDate:yyyy-MM-dd}");

                // 验证日期参数的合理性
                if (endDate <= startDate)
                {
                    var errorMsg = $"服务结束时间({endDate:yyyy-MM-dd})必须晚于开始时间({startDate:yyyy-MM-dd})";
                    LogUtil.AddErrorLog(errorMsg);
                    throw new CRM2_API.Model.System.ApiException(errorMsg);
                }

                // 计算年份和月份的差异
                var yearDiff = endDate.Year - startDate.Year;
                var monthDiff = endDate.Month - startDate.Month;

                // 基础月数 = 年份差异 * 12 + 月份差异
                var totalMonths = yearDiff * 12 + monthDiff;

                // 处理跨月的天数逻辑（自然月计费的核心逻辑）
                if (endDate.Day >= startDate.Day)
                {
                    // 结束日期的天数 >= 开始日期的天数，说明跨越了完整的月份
                    // 例如：1月15日 到 2月15日 = 1个完整月
                    // 例如：1月15日 到 2月20日 = 1个完整月 + 部分天数
                    totalMonths += 1;
                }
                // 如果 endDate.Day < startDate.Day，说明结束日期在当前计费周期内
                // 例如：7月28日开始，次年6月8日结束，6月8日在第11个计费周期内
                // totalMonths 保持不变，不需要额外处理

                // 验证计算结果的合理性
                if (totalMonths <= 0)
                {
                    var errorMsg = $"计算的服务月数异常，月数: {totalMonths}，开始时间: {startDate:yyyy-MM-dd}，结束时间: {endDate:yyyy-MM-dd}";
                    LogUtil.AddErrorLog(errorMsg);
                    throw new CRM2_API.Model.System.ApiException(errorMsg);
                }

                LogUtil.AddLog($"服务月数计算完成: {totalMonths}个月，计算逻辑: 年差={yearDiff}, 月差={monthDiff}, 开始日={startDate.Day}, 结束日={endDate.Day}");

                return totalMonths;
            }
            catch (CRM2_API.Model.System.ApiException)
            {
                // 重新抛出业务异常，保持原有的错误信息和上下文
                throw;
            }
            catch (Exception ex)
            {
                var errorMsg = $"计算服务月数异常，开始时间: {startDate:yyyy-MM-dd}，结束时间: {endDate:yyyy-MM-dd}，异常信息: {ex.Message}";
                LogUtil.AddErrorLog($"{errorMsg}，详细异常: {ex}");
                throw new CRM2_API.Model.System.ApiException(errorMsg);
            }
        }

        /// <summary>
        /// 获取慧思学院配置信息
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <returns>慧思学院配置信息</returns>
        /// <exception cref="CRM2_API.Model.System.ApiException">获取配置失败时抛出</exception>
        private CollegeBasicConfig GetCollegeConfiguration(ServiceOpeningParams openingParams)
        {
            try
            {
                LogUtil.AddLog("开始获取慧思学院配置信息");

                // 验证基础配置
                if (openingParams.CollegeBasicConfig == null)
                {
                    var errorMsg = "慧思学院基础配置为空";
                    LogUtil.AddErrorLog(errorMsg);
                    throw new CRM2_API.Model.System.ApiException(errorMsg);
                }

                var config = openingParams.CollegeBasicConfig;

                // 验证关键配置项
                if (!config.HasAppPermission)
                {
                    var errorMsg = "慧思学院应用权限未启用";
                    LogUtil.AddErrorLog(errorMsg);
                    throw new CRM2_API.Model.System.ApiException(errorMsg);
                }

                if (config.MaxAccountsNum <= 0)
                {
                    var errorMsg = $"慧思学院账号数量配置无效: {config.MaxAccountsNum}";
                    LogUtil.AddErrorLog(errorMsg);
                    throw new CRM2_API.Model.System.ApiException(errorMsg);
                }

                if (config.ServiceCycleStart == null || config.ServiceCycleEnd == null)
                {
                    var errorMsg = "慧思学院服务周期配置不完整";
                    LogUtil.AddErrorLog(errorMsg);
                    throw new CRM2_API.Model.System.ApiException(errorMsg);
                }

                if (config.ServiceCycleEnd <= config.ServiceCycleStart)
                {
                    var errorMsg = $"慧思学院服务周期配置错误，结束时间({config.ServiceCycleEnd:yyyy-MM-dd})必须晚于开始时间({config.ServiceCycleStart:yyyy-MM-dd})";
                    LogUtil.AddErrorLog(errorMsg);
                    throw new CRM2_API.Model.System.ApiException(errorMsg);
                }

                LogUtil.AddLog($"慧思学院配置验证成功: 账号数量={config.MaxAccountsNum}, 服务周期={config.ServiceCycleStart:yyyy-MM-dd}至{config.ServiceCycleEnd:yyyy-MM-dd}");

                return config;
            }
            catch (CRM2_API.Model.System.ApiException)
            {
                // 重新抛出业务异常，保持原有的错误信息和上下文
                throw;
            }
            catch (Exception ex)
            {
                var errorMsg = $"获取慧思学院配置异常，合同ID: {openingParams.Contract?.Id}, 异常信息: {ex.Message}";
                LogUtil.AddErrorLog($"{errorMsg}，详细异常: {ex}");
                throw new CRM2_API.Model.System.ApiException(errorMsg);
            }
        }

        /// <summary>
        /// 更新用户慧思学院权限
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <param name="collegeConfig">慧思学院配置</param>
        /// <returns>权限更新结果</returns>
        private CollegePermissionUpdateResult UpdateUserCollegePermissions(ServiceOpeningParams openingParams, CollegeBasicConfig collegeConfig)
        {
            var result = new CollegePermissionUpdateResult();

            try
            {
                LogUtil.AddLog("开始更新用户慧思学院权限");

                // 获取需要设置慧思学院权限的用户
                var usersToUpdate = openingParams.Users
                    .Where(u => u.CollegePermission == true)
                    .ToList();

                if (usersToUpdate.Count == 0)
                {
                    result.Success = true;
                    result.Message = "没有用户需要设置慧思学院权限";
                    result.UpdatedUsersCount = 0;
                    LogUtil.AddLog("没有用户需要设置慧思学院权限，跳过权限更新");
                    return result;
                }

                // 验证用户数量是否超过配置限制
                if (usersToUpdate.Count > collegeConfig.MaxAccountsNum)
                {
                    var errorMsg = $"需要设置慧思学院权限的用户数量({usersToUpdate.Count})超过了配置的最大账号数量({collegeConfig.MaxAccountsNum})";
                    LogUtil.AddErrorLog(errorMsg);
                    result.Success = false;
                    result.ErrorMessage = errorMsg;
                    return result;
                }

                // TODO: 这里应该调用实际的权限更新逻辑
                // 由于慧思学院的权限更新可能涉及到具体的业务系统调用，
                // 目前先记录日志，实际的权限更新逻辑需要根据具体的业务需求来实现

                LogUtil.AddLog($"模拟更新{usersToUpdate.Count}个用户的慧思学院权限");

                foreach (var user in usersToUpdate)
                {
                    LogUtil.AddLog($"为用户设置慧思学院权限: 用户ID={user.SysUserId}, 账号={user.AccountNumber}");
                    // 这里应该是实际的权限设置逻辑
                }

                result.Success = true;
                result.Message = $"成功为{usersToUpdate.Count}个用户设置慧思学院权限";
                result.UpdatedUsersCount = usersToUpdate.Count;

                LogUtil.AddLog($"慧思学院权限更新完成: {result.Message}");

                return result;
            }
            catch (Exception ex)
            {
                var errorMsg = $"更新用户慧思学院权限异常: {ex.Message}";
                LogUtil.AddErrorLog($"{errorMsg}，详细异常: {ex}");

                result.Success = false;
                result.ErrorMessage = errorMsg;
                return result;
            }
        }

        #endregion

        #region SaleWits资源下发数据准备

        /// <summary>
        /// 准备SaleWits资源下发数据
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <param name="salesWitsService">SaleWits服务配置</param>
        /// <returns>资源数据准备结果</returns>
        private SaleWitsResourceDataResult PrepareSaleWitsResourceData(ServiceOpeningParams openingParams, Db_crm_contract_serviceinfo_saleswits salesWitsService)
        {
            var result = new SaleWitsResourceDataResult();

            try
            {
                LogUtil.AddLog("开始准备SaleWits资源下发数据");

                // 1. 判断SaleWits资源下发类型
                var distributionType = DetermineSaleWitsDistributionType(openingParams);
                LogUtil.AddLog($"SaleWits下发类型判断结果: {distributionType}");

                // 2. 验证预设资源配置
                var resourceValidation = ValidatePresetSaleWitsResources(salesWitsService);
                if (!resourceValidation.IsValid)
                {
                    result.SetError($"SaleWits预设资源验证失败: {resourceValidation.ErrorMessage}");
                    return result;
                }

                // 3. 判断是否需要下发资源或充值
                var tokenCount = resourceValidation.Resources.TokenCount;
                var emailCount = resourceValidation.Resources.EmailCount;
                var rechargeAmount = salesWitsService.RechargeAmount ?? 0;

                if (tokenCount == 0 && emailCount == 0 && rechargeAmount == 0)
                {
                    // Token、Email和充值金额都为0，不需要下发
                    result.SetError("Token、邮件数量和充值金额都为0，无需下发资源或充值");
                    LogUtil.AddLog("SaleWits资源下发判断：Token、邮件数量和充值金额都为0，跳过资源下发和充值");
                    return result;
                }

                // 4. 构建资源数据准备结果
                result.SetSuccess("SaleWits资源数据准备完成，需要下发资源或充值");
                result.DistributionType = distributionType;
                result.PresetResources = resourceValidation.Resources;

                LogUtil.AddLog($"SaleWits资源数据准备完成: 类型={distributionType}, 月数={result.PresetResources.MonthsCount}, 邮件数={result.PresetResources.EmailCount}, Token数={result.PresetResources.TokenCount}万个, 充值金额={rechargeAmount}元");

            }
            catch (Exception ex)
            {
                var errorMsg = $"准备SaleWits资源下发数据异常: {ex.Message}";
                LogUtil.AddErrorLog($"{errorMsg}，详细异常: {ex}");
                result.SetError(errorMsg);
            }

            return result;
        }

        /// <summary>
        /// 判断SaleWits资源下发类型
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <returns>下发类型</returns>
        private CRM2_API.Model.BLLModel.Enum.EnumSaleWitsDistributionType DetermineSaleWitsDistributionType(ServiceOpeningParams openingParams)
        {
            try
            {
                LogUtil.AddLog("开始判断SaleWits资源下发类型");

                // 1. 首先判断开通类型
                var openingType = DetermineOpeningType(openingParams);
                var contractType = openingParams.Contract.ContractType;

                LogUtil.AddLog($"开通类型: {openingType}, 合同类型: {contractType}");

                // 2. 根据开通类型决定后续逻辑
                switch (openingType)
                {
                    case ServiceOpeningType.NewAccount:
                        // 新开账号：直接返回首次下发，无需查询历史记录
                        LogUtil.AddLog("新开账号，直接判断为首次下发");
                        return CRM2_API.Model.BLLModel.Enum.EnumSaleWitsDistributionType.FirstDistribution;

                    case ServiceOpeningType.Change:
                        // 变更服务：查询当前合同的历史服务记录
                        return HandleChangeServiceDistributionType(openingParams);

                    case ServiceOpeningType.Renewal:
                        // 续约服务：查询被续约合同的历史服务记录
                        return HandleRenewalServiceDistributionType(openingParams);

                    default:
                        LogUtil.AddLog($"未知的开通类型: {openingType}，默认按首次下发处理");
                        return CRM2_API.Model.BLLModel.Enum.EnumSaleWitsDistributionType.FirstDistribution;
                }
            }
            catch (Exception ex)
            {
                var errorMsg = $"判断SaleWits资源下发类型异常: {ex.Message}";
                LogUtil.AddErrorLog($"{errorMsg}，详细异常: {ex}");
                throw new CRM2_API.Model.System.ApiException(errorMsg);
            }
        }

        /// <summary>
        /// 处理变更服务的资源下发类型判断
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <returns>下发类型</returns>
        private CRM2_API.Model.BLLModel.Enum.EnumSaleWitsDistributionType HandleChangeServiceDistributionType(ServiceOpeningParams openingParams)
        {
            LogUtil.AddLog("开始处理变更服务的资源下发类型判断");

            // 查询当前合同是否已有SaleWits服务记录
            var existingService = _dbOpe_crm_contract_serviceinfo_saleswits
                .GetData(x => x.ContractId == openingParams.Contract.Id && x.IsProcessed == (int)EnumGtisServiceIsProcess.Processed && x.Deleted == false);

            if (existingService == null)
            {
                LogUtil.AddLog($"变更服务但合同{openingParams.Contract.Id}未找到历史SaleWits服务记录，按首次开通处理");
                return CRM2_API.Model.BLLModel.Enum.EnumSaleWitsDistributionType.FirstDistribution;
            }

            // 获取当前申请的配置信息
            var currentAccountCount = openingParams.SalesWitsBasicConfig?.MaxAccountsNum ?? 0;
            var existingAccountCount = existingService.AccountsNum ?? 0;

            // 获取当前申请的资源配置（从当前申请记录中获取）
            var currentApplService = _dbOpe_crm_contract_serviceinfo_saleswits
                .GetData(x => x.WitsApplId == openingParams.MainService.Id && x.IsProcessed == (int)EnumGtisServiceIsProcess.Processed && x.Deleted == false);

            var currentTokenCount = currentApplService?.CurrentGiftTokens ?? 0;
            var currentEmailCount = currentApplService?.CurrentGiftEmails ?? 0;
            var currentRechargeAmount = currentApplService?.RechargeAmount ?? 0;

            LogUtil.AddLog($"变更服务配置比较：账号数量 当前{currentAccountCount}个/历史{existingAccountCount}个, Token{currentTokenCount}万个, 邮件{currentEmailCount}封, 充值{currentRechargeAmount}元");

            // 判断是否为单纯充值（只有充值金额，没有Token和Email下发，账号数量也没有增加）
            if (currentRechargeAmount > 0 && currentTokenCount == 0 && currentEmailCount == 0 && currentAccountCount <= existingAccountCount)
            {
                LogUtil.AddLog("检测到单纯充值操作（只有充值金额，无Token/Email下发，账号数量未增加），判断为充值操作");
                return CRM2_API.Model.BLLModel.Enum.EnumSaleWitsDistributionType.RechargeOperation;
            }

            // 判断是否为增加账号补发
            if (currentAccountCount > existingAccountCount)
            {
                LogUtil.AddLog("检测到账号数量增加，判断为增加账号补发");
                return CRM2_API.Model.BLLModel.Enum.EnumSaleWitsDistributionType.AddAccountDistribution;
            }
            else
            {
                LogUtil.AddLog("账号数量未增加，判断为年度定时下发");
                return CRM2_API.Model.BLLModel.Enum.EnumSaleWitsDistributionType.AnnualDistribution;
            }
        }

        /// <summary>
        /// 处理续约服务的资源下发类型判断
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <returns>下发类型</returns>
        private CRM2_API.Model.BLLModel.Enum.EnumSaleWitsDistributionType HandleRenewalServiceDistributionType(ServiceOpeningParams openingParams)
        {
            LogUtil.AddLog("开始处理续约服务的资源下发类型判断");

            // 首先查询当前合同的服务记录，看是否是重新开通
            var currentService = _dbOpe_crm_contract_serviceinfo_saleswits
                .GetData(x => x.ContractId == openingParams.Contract.Id && x.IsProcessed == (int)EnumGtisServiceIsProcess.Processed && x.Deleted == false);

            if (currentService == null)
            {
                LogUtil.AddLog($"续约服务但合同{openingParams.Contract.Id}未找到当前服务记录，按首次开通处理");
                return CRM2_API.Model.BLLModel.Enum.EnumSaleWitsDistributionType.FirstDistribution;
            }

            // 检查是否延续了原账号（通过HistoryId判断）
            if (string.IsNullOrEmpty(currentService.HistoryId))
            {
                LogUtil.AddLog("续约服务重新申请账号（无HistoryId），按首次开通处理");
                return CRM2_API.Model.BLLModel.Enum.EnumSaleWitsDistributionType.FirstDistribution;
            }

            // 延续原账号的情况，查询被续约的历史服务记录
            var historyService = _dbOpe_crm_contract_serviceinfo_saleswits
                .GetData(x => x.Id == currentService.HistoryId && x.Deleted == false);

            if (historyService == null)
            {
                LogUtil.AddLog($"续约服务找不到历史服务记录（HistoryId: {currentService.HistoryId}），按首次开通处理");
                return CRM2_API.Model.BLLModel.Enum.EnumSaleWitsDistributionType.FirstDistribution;
            }

            // 比较账号数量判断是否增加了账号
            var currentAccountCount = openingParams.SalesWitsBasicConfig?.MaxAccountsNum ?? 0;
            var historyAccountCount = historyService.AccountsNum ?? 0;

            LogUtil.AddLog($"续约服务账号数量比较：当前{currentAccountCount}个，历史{historyAccountCount}个");

            if (currentAccountCount > historyAccountCount)
            {
                LogUtil.AddLog("续约服务检测到账号数量增加，判断为增加账号补发");
                return CRM2_API.Model.BLLModel.Enum.EnumSaleWitsDistributionType.AddAccountDistribution;
            }
            else
            {
                LogUtil.AddLog("续约服务延续原账号且账号数量未增加，跳过立即下发，等待定时计划执行");
                return CRM2_API.Model.BLLModel.Enum.EnumSaleWitsDistributionType.AnnualDistribution;
            }
        }

        /// <summary>
        /// 验证预设的SaleWits资源配置
        /// </summary>
        /// <param name="salesWitsService">SaleWits服务配置</param>
        /// <returns>验证结果</returns>
        private SaleWitsResourceValidationResult ValidatePresetSaleWitsResources(Db_crm_contract_serviceinfo_saleswits salesWitsService)
        {
            var result = new SaleWitsResourceValidationResult();

            try
            {
                LogUtil.AddLog("开始验证SaleWits预设资源配置");

                // 检查预设资源是否已配置
                if (!salesWitsService.CurrentGiftMonths.HasValue ||
                    !salesWitsService.CurrentGiftTokens.HasValue ||
                    !salesWitsService.CurrentGiftEmails.HasValue)
                {
                    result.SetInvalid("SaleWits服务记录中的资源配置不完整，请先配置本次赠送的月份、Token和邮件数量");
                    return result;
                }

                // 获取资源数量（允许为0）
                var monthsCount = salesWitsService.CurrentGiftMonths.Value;
                var tokenCount = salesWitsService.CurrentGiftTokens.Value;
                var emailCount = salesWitsService.CurrentGiftEmails.Value;

                // 检查月份数量
                if (monthsCount < 0)
                {
                    result.SetInvalid("本次赠送月份不能为负数");
                    return result;
                }

                // 检查Token和Email数量
                if (tokenCount < 0 || emailCount < 0)
                {
                    result.SetInvalid("Token和邮件数量不能为负数");
                    return result;
                }

                // Token和Email可以都为0，这种情况下在数据准备阶段会判断为不需要下发

                // 获取充值金额
                var rechargeAmount = salesWitsService.RechargeAmount ?? 0;

                // 构建预设资源信息
                var presetResources = new SaleWitsPresetResources
                {
                    MonthsCount = monthsCount,
                    TokenCount = tokenCount,
                    EmailCount = emailCount,
                    RechargeAmount = rechargeAmount
                };

                result.SetValid(presetResources);
                LogUtil.AddLog($"SaleWits预设资源验证通过: 月数={presetResources.MonthsCount}, Token={presetResources.TokenCount}万个, 邮件={presetResources.EmailCount}封, 充值金额={presetResources.RechargeAmount}元");

            }
            catch (Exception ex)
            {
                var errorMsg = $"验证SaleWits预设资源配置异常: {ex.Message}";
                LogUtil.AddErrorLog($"{errorMsg}，详细异常: {ex}");
                result.SetInvalid(errorMsg);
            }

            return result;
        }

        #endregion
















    

    #region 辅助数据结构

    /// <summary>
    /// SaleWits参数
    /// </summary>
    public class SaleWitsParams
    {
        public int Amount { get; set; }
        public int EmailCount { get; set; }
        public int TokenCount { get; set; }
    }

        /// <summary>
        /// 获取所有账号的权限信息（20250803 这里不传变更的值了，需要传所有账号的权限信息）
        /// </summary>
        /// <param name="currentWitsUsers">当前Wits用户列表</param>
        /// <returns>所有账号的权限信息列表</returns>
        private List<BM_GtisOpe_RenewalContact.BM_GtisOpe_ChangeUserPermission> CompareUserPermissions(
            List<Db_crm_contract_serviceinfo_wits_user> currentWitsUsers)
        {
            var allUserPermissions = new List<BM_GtisOpe_RenewalContact.BM_GtisOpe_ChangeUserPermission>();

            try
            {
                LogUtil.AddLog("开始获取所有账号的权限信息");

                // 直接遍历当前Wits用户配置，获取所有有SalesWits权限的账号信息
                foreach (var currentUser in currentWitsUsers)
                {
                        if (string.IsNullOrEmpty(currentUser.SalesWitsBindPhoneId))
                        {
                            // throw new ApiException($"账号{currentUser.SysUserId}未配置SalesWits使用者");
                        }
                        if(string.IsNullOrEmpty(currentUser.SysUserId))
                        {
                            continue;
                        }

                        // 获取当前用户的apps权限
                        var currentAccountApps = GetCurrentUserApps(currentUser);

                        var userPermission = new BM_GtisOpe_RenewalContact.BM_GtisOpe_ChangeUserPermission
                        {
                            SysUserID = currentUser.SysUserId,
                            // SysUserPhoneID = currentUser.SalesWitsBindPhoneId,
                            SysUserPhoneID = "",
                            IsOpenCrm = true, // SalesWits权限默认开通CRM
                            apps = currentAccountApps
                        };

                        allUserPermissions.Add(userPermission);
                        LogUtil.AddLog($"添加账号权限信息: 账号{currentUser.SysUserId}，使用者{currentUser.SalesWitsBindPhoneId}");
                }

                LogUtil.AddLog($"权限信息获取完成，共获取{allUserPermissions.Count}个账号的权限信息");
            }
            catch (Exception ex)
            {
                throw new ApiException($"获取用户权限信息异常: {ex.Message}");
            }

            return allUserPermissions;
        }

        /// <summary>
        /// 对比用户权限变更（原版本 - 保留源代码注释）
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <param name="currentWitsUsers">当前Wits用户列表</param>
        /// <returns>权限变更列表</returns>
        private List<BM_GtisOpe_RenewalContact.BM_GtisOpe_ChangeUserPermission> CompareUserPermissions_Original(
            ServiceOpeningParams openingParams,
            List<Db_crm_contract_serviceinfo_wits_user> currentWitsUsers)
        {
            var permissionChanges = new List<BM_GtisOpe_RenewalContact.BM_GtisOpe_ChangeUserPermission>();

            try
            {
                LogUtil.AddLog("开始对比用户权限变更");

                // 获取历史用户权限信息（包含apps和使用者信息）
                var historyUserPermissions = GetHistoryUserPermissions(openingParams);

                foreach (var historyUserInfo in historyUserPermissions)
                {
                    // 查找对应的当前用户配置
                    var currentUser = currentWitsUsers.FirstOrDefault(u => u.SysUserId == historyUserInfo.SysUserID);
                    if (currentUser == null)
                    {
                        LogUtil.AddLog($"历史用户{historyUserInfo.SysUserID}在当前配置中未找到，跳过权限对比");
                        continue;
                    }

                    // 1. 首先对比账户级的apps是否变更
                    var historyAccountApps = historyUserInfo.apps ?? new string[0];
                    var currentAccountApps = GetCurrentUserApps(currentUser);

                    bool accountAppsChanged = IsAppsChanged(historyAccountApps, currentAccountApps);

                    if (accountAppsChanged)
                    {
                        LogUtil.AddLog($"检测到账户级apps权限变更: 账号{historyUserInfo.SysUserID}");
                        LogUtil.AddLog($"  历史apps: [{string.Join(", ", historyAccountApps)}]");
                        LogUtil.AddLog($"  当前apps: [{string.Join(", ", currentAccountApps)}]");

                        // 账户级权限变更，需要检查当前是否有SalesWits使用者
                        if(currentUser.SalesWitsPermission == true && string.IsNullOrEmpty(currentUser.SalesWitsBindPhoneId))
                        {
                            // throw new Exception($"账号{historyUserInfo.SysUserID}未配置SalesWits使用者");
                        }
                            var permissionChange = new BM_GtisOpe_RenewalContact.BM_GtisOpe_ChangeUserPermission
                            {
                                SysUserID = historyUserInfo.SysUserID,
                                SysUserPhoneID = currentUser.SalesWitsBindPhoneId,
                                IsOpenCrm = true, // SalesWits权限默认开通CRM
                                apps = currentAccountApps
                            };

                            permissionChanges.Add(permissionChange);
                            LogUtil.AddLog($"  生成权限变更记录: 使用者{currentUser.SalesWitsBindPhoneId}");
                        
                    }
                    else if(currentUser.SalesWitsPermission == true)
                    {
                        // 2. 账号级没变，再对比开通了SalesWits的使用者是否变化
                        LogUtil.AddLog($"账户级apps权限未变更: 账号{historyUserInfo.SysUserID}，检查SalesWits使用者变更");
                        if(string.IsNullOrEmpty(currentUser.SalesWitsBindPhoneId))
                        {
                            // throw new ApiException($"账号{historyUserInfo.SysUserID}未配置SalesWits使用者");
                        }
                        // 查找历史中有SalesWits权限的使用者
                        string? historySalesWitsPhoneId = null;
                        if (historyUserInfo.AllPhoneUserInfo != null && historyUserInfo.AllPhoneUserInfo.Count > 0)
                        {
                            var historySalesWitsUser = historyUserInfo.AllPhoneUserInfo
                                .FirstOrDefault(p => HasSalesWitsPermission(p.apps));
                            historySalesWitsPhoneId = historySalesWitsUser?.SysUserPhoneID;
                        }

                        // 获取当前的SalesWits使用者
                        var currentSalesWitsPhoneId = currentUser.SalesWitsPermission == true ? currentUser.SalesWitsBindPhoneId : null;

                        // 对比SalesWits使用者是否发生变更
                        if (historySalesWitsPhoneId != currentSalesWitsPhoneId)
                        {
                            LogUtil.AddLog($"检测到SalesWits使用者变更: 账号{historyUserInfo.SysUserID}");
                            LogUtil.AddLog($"  历史SalesWits使用者: {historySalesWitsPhoneId ?? "无"}");
                            LogUtil.AddLog($"  当前SalesWits使用者: {currentSalesWitsPhoneId ?? "无"}");

                            // 如果当前有SalesWits使用者，创建权限变更记录
                            if (!string.IsNullOrEmpty(currentSalesWitsPhoneId))
                            {
                                var permissionChange = new BM_GtisOpe_RenewalContact.BM_GtisOpe_ChangeUserPermission
                                {
                                    SysUserID = historyUserInfo.SysUserID,
                                    SysUserPhoneID = currentSalesWitsPhoneId,
                                    IsOpenCrm = true, // SalesWits权限默认开通CRM
                                    apps = currentAccountApps
                                };

                                permissionChanges.Add(permissionChange);
                                LogUtil.AddLog($"  生成权限变更记录: 使用者{currentSalesWitsPhoneId}");
                            }
                        }
                        else
                        {
                            LogUtil.AddLog($"SalesWits使用者未变更: 账号{historyUserInfo.SysUserID}，使用者{currentSalesWitsPhoneId ?? "无"}");
                        }
                    }
                }

                LogUtil.AddLog($"权限对比完成，共发现{permissionChanges.Count}个权限变更");
            }
            catch (Exception ex)
            {
                throw new ApiException($"对比用户权限变更异常: {ex.Message}");
            }

            return permissionChanges;
        }

        /// <summary>
        /// 获取历史用户权限信息
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <returns>历史用户权限信息</returns>
        private List<BM_GtisOpeUserInfo> GetHistoryUserPermissions(ServiceOpeningParams openingParams)
        {
            try
            {
                // 根据开通类型确定查询的客户编码
                string queryCustomerCode = GetQueryCustomerCode(openingParams);

                if (string.IsNullOrEmpty(queryCustomerCode))
                {
                    LogUtil.AddLog("未找到有效的客户编码，无法查询历史用户权限");
                    return new List<BM_GtisOpeUserInfo>();
                }

                LogUtil.AddLog($"开始查询GTIS系统历史用户权限，客户编码: {queryCustomerCode}");

                // 调用GTIS系统接口查询该客户编码下的现有账号状态（包含apps和使用者信息）
                var gtisOpe = new BLL_GtisOpe();
                var gtisUserInfoList = gtisOpe.GetUserInfo(queryCustomerCode).Result;

                LogUtil.AddLog($"从GTIS系统查询到 {gtisUserInfoList?.Count ?? 0} 个历史用户权限信息");

                return gtisUserInfoList ?? new List<BM_GtisOpeUserInfo>();
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"查询GTIS系统历史用户权限异常: {ex.Message}");
                return new List<BM_GtisOpeUserInfo>();
            }
        }

        /// <summary>
        /// 判断apps权限是否发生变更
        /// </summary>
        /// <param name="historyApps">历史apps权限</param>
        /// <param name="currentApps">当前apps权限</param>
        /// <returns>是否发生变更</returns>
        private bool IsAppsChanged(string[] historyApps, string[] currentApps)
        {
            if (historyApps == null && currentApps == null) return false;
            if (historyApps == null || currentApps == null) return true;
            if (historyApps.Length != currentApps.Length) return true;

            var historySet = new HashSet<string>(historyApps, StringComparer.OrdinalIgnoreCase);
            var currentSet = new HashSet<string>(currentApps, StringComparer.OrdinalIgnoreCase);

            return !historySet.SetEquals(currentSet);
        }

        /// <summary>
        /// 判断apps权限中是否包含SalesWits权限
        /// </summary>
        /// <param name="apps">应用权限数组</param>
        /// <returns>是否有SalesWits权限</returns>
        private bool HasSalesWitsPermission(string[] apps)
        {
            if (apps == null || apps.Length == 0)
                return false;

            return apps.Any(app => app.Equals("salewits", StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 获取当前用户的apps配置 //["CRM", "Gtis6", "College", "HQS"]
        /// </summary>
        /// <param name="witsUser">Wits用户信息</param>
        /// <returns>apps数组</returns>
        private string[] GetCurrentUserApps(Db_crm_contract_serviceinfo_wits_user witsUser)
        {
            var apps = new List<string>();

            if (witsUser.GtisPermission == true)
                apps.Add("Gtis6");

            if (witsUser.GlobalSearchPermission == true)
                apps.Add("HQS");

            if (witsUser.SalesWitsPermission == true)
                apps.Add("CRM");

            if (witsUser.CollegePermission == true)
                apps.Add("College");

            return apps.ToArray();
        }

            /// <summary>
        /// 获取SaleWits服务的下次年度下发时间
        /// </summary>
        /// <param name="serviceId">SaleWits服务ID</param>
        /// <returns>下次下发时间，如果无法确定则返回null</returns>
        public DateTime? GetNextDistributionDate(string serviceId)
        {
            try
            {
                if (string.IsNullOrEmpty(serviceId))
                {
                    LogUtil.AddLog("ServiceId为空，无法获取下次下发时间");
                    return null;
                }

                LogUtil.AddLog($"开始获取SaleWits服务{serviceId}的下次下发时间");

                // 首先获取SaleWits服务信息，取得ResourceManagementId
                var salesWitsService = _dbOpe_crm_contract_serviceinfo_saleswits.GetData(x => x.Id == serviceId && x.Deleted == false);
                if (salesWitsService == null)
                {
                    LogUtil.AddLog($"未找到SaleWits服务{serviceId}");
                    return null;
                }

                var resourceManagementId = salesWitsService.ResourceManagementId;
                if (string.IsNullOrEmpty(resourceManagementId))
                {
                    LogUtil.AddLog($"SaleWits服务{serviceId}的ResourceManagementId为空，无法获取下次下发时间");
                    return null;
                }

                LogUtil.AddLog($"SaleWits服务{serviceId}对应的资源管理ID: {resourceManagementId}");

                // 获取最近一次年度下发记录
                var lastAnnualDistribution = _dbOpe_crm_salewits_resource_distribution
                    .GetDataList(x => x.ResourceManagementId == resourceManagementId &&
                                     x.DistributionType == "AnnualDistribution" &&
                                     x.IsSuccess == true)
                    .OrderByDescending(x => x.DistributionTime)
                    .FirstOrDefault();

                if (lastAnnualDistribution != null)
                {
                    // 有年度下发记录，下次下发时间 = 上次下发时间 + 1年
                    var nextDate = lastAnnualDistribution.DistributionTime.AddYears(1).Date;
                    LogUtil.AddLog($"资源管理{resourceManagementId}上次年度下发时间{lastAnnualDistribution.DistributionTime:yyyy-MM-dd}，下次下发时间{nextDate:yyyy-MM-dd}");
                    return nextDate;
                }

                // 没有年度下发记录，查找首次下发记录
                var firstDistribution = _dbOpe_crm_salewits_resource_distribution
                    .GetDataList(x => x.ResourceManagementId == resourceManagementId &&
                                     x.DistributionType == "FirstDistribution" &&
                                     x.IsSuccess == true)
                    .OrderBy(x => x.DistributionTime)
                    .FirstOrDefault();

                if (firstDistribution != null)
                {
                    // 有首次下发记录，下次下发时间 = 首次下发时间 + 1年
                    var nextDate = firstDistribution.DistributionTime.AddYears(1).Date;
                    LogUtil.AddLog($"资源管理{resourceManagementId}首次下发时间{firstDistribution.DistributionTime:yyyy-MM-dd}，下次下发时间{nextDate:yyyy-MM-dd}");
                    return nextDate;
                }

                LogUtil.AddLog($"资源管理{resourceManagementId}未找到任何下发记录，无法确定下次下发时间");
                return null;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取SaleWits服务{serviceId}下次下发时间异常: {ex.Message}", ex);
                return null;
            }
        }

        #endregion
    }
}